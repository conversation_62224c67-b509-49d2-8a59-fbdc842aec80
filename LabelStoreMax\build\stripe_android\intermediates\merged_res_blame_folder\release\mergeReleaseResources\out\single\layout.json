[{"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_timepicker_textinput_display.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_timepicker_textinput_display.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_text_input_start_icon.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_text_input_start_icon.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_bank_item.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_bank_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/select_dialog_item_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/select_dialog_item_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_popup_menu_item_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_popup_menu_item_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_bottom_sheet_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_bottom_sheet_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/m3_alert_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/m3_alert_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_days_of_week.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_days_of_week.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_menu_item_action_area.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_menu_item_action_area.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_alert_dialog_title_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_alert_dialog_title_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/m3_side_sheet_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/m3_side_sheet_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_alert_dialog_actions.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_alert_dialog_actions.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/browser_actions_context_menu_row.xml", "source": "com.flutter.stripe.stripe_android-browser-1.8.0-45:/layout/browser_actions_context_menu_row.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_chip_input_combo.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_chip_input_combo.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_clock_period_toggle.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_clock_period_toggle.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_text_input_date.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_text_input_date.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/custom_dialog.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/layout/custom_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_becs_debit_widget.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_becs_debit_widget.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/browser_actions_context_menu_page.xml", "source": "com.flutter.stripe.stripe_android-browser-1.8.0-45:/layout/browser_actions_context_menu_page.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_zone_single_select_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_zone_single_select_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_zone_web_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_zone_web_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_submit_dialog.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_submit_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_input_widget.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_input_widget.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_alert_dialog_button_bar_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_alert_dialog_button_bar_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_bank_list_payment_method.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_bank_list_payment_method.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_actions.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_actions.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_shipping_method_page.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_shipping_method_page.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_months.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_months.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_zone_multi_select_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_zone_multi_select_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_clock_display.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_clock_display.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_action_mode_bar.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_action_mode_bar.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_screen_simple.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_screen_simple.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_brand_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_brand_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_add_payment_method_card_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_add_payment_method_card_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_masked_card_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_masked_card_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/select_dialog_singlechoice_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/select_dialog_singlechoice_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_big_media_narrow_custom.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_big_media_narrow_custom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_add_payment_method_row.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_add_payment_method_row.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_header_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_header_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_layout_snackbar.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_layout_snackbar.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_radial_view_group.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_radial_view_group.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_activity.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_activity.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_widget_progress_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_widget_progress_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_clockface_view.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_clockface_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_brand_choice_list_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_brand_choice_list_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_shipping_method_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_shipping_method_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_big_media_custom.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_big_media_custom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_zone_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_zone_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_google_pay_row.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_google_pay_row.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_time_input.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_time_input.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_day.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_day.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_search_bar.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_search_bar.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_address_widget.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_address_widget.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_search_view.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_search_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_alert_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_alert_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_horizontal_divider.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_horizontal_divider.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_month_navigation.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_month_navigation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_list_menu_item_icon.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_list_menu_item_icon.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/ime_base_split_test_activity.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/layout/ime_base_split_test_activity.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_vertical_divider.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_vertical_divider.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_search_dropdown_item_icons_2line.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_search_dropdown_item_icons_2line.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_google_pay_button.xml", "source": "com.flutter.stripe.stripe_android-jetified-paymentsheet-21.6.0-53:/layout/stripe_google_pay_button.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_select_card_brand_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_select_card_brand_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_expanded_menu_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_expanded_menu_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_activity.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_activity.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_multiline_widget.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_multiline_widget.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_screen_simple_overlay_action_mode.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_screen_simple_overlay_action_mode.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_part_chronometer.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/layout/notification_template_part_chronometer.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_brand_zone_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_brand_zone_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_timepicker.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_timepicker.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_add_payment_method_activity.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_add_payment_method_activity.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_navigation_rail_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_navigation_rail_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_form_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_form_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_layout_tab_icon.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_layout_tab_icon.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_big_media_narrow.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_big_media_narrow.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/m3_auto_complete_simple_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/m3_auto_complete_simple_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_brand_spinner_dropdown.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_brand_spinner_dropdown.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_zone_text_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_zone_text_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/support_simple_spinner_dropdown_item.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_header_title_text.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_header_title_text.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_progress_view_layout.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_progress_view_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_media_cancel_action.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_media_cancel_action.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_list_menu_item_radio.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_list_menu_item_radio.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_search_view.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_search_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_screen_toolbar.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_screen_toolbar.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_month_labeled.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_month_labeled.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_country_text_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_country_text_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_dialog_title_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_dialog_title_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_header_toggle.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_header_toggle.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_activity_card_scan.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-ui-core-21.6.0-76:/layout/stripe_activity_card_scan.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_screen_content_include.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_screen_content_include.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_list_menu_item_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_list_menu_item_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_challenge_fragment.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_challenge_fragment.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_alert_select_dialog_multichoice.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_alert_select_dialog_multichoice.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_react_native_google_pay_button.xml", "source": "com.flutter.stripe.stripe_android-main-86:/layout/stripe_react_native_google_pay_button.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_country_dropdown_item.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_country_dropdown_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_shipping_method_widget.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_shipping_method_widget.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_clock_display_divider.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_clock_display_divider.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_navigation_menu_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_navigation_menu_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_tooltip.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_tooltip.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_activity_chooser_view.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_activity_chooser_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_navigation_item_header.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_navigation_item_header.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_list_menu_item_checkbox.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_list_menu_item_checkbox.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/m3_alert_dialog_title.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/m3_alert_dialog_title.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_header_selection_text.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_header_selection_text.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_3ds2_transaction_layout.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_3ds2_transaction_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_action_mode_close_item_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_action_mode_close_item_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_primary_button.xml", "source": "com.flutter.stripe.stripe_android-jetified-paymentsheet-21.6.0-53:/layout/stripe_primary_button.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_layout_snackbar_include.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_layout_snackbar_include.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/paybutton_generic.xml", "source": "com.flutter.stripe.stripe_android-jetified-play-services-wallet-19.4.0-13:/layout/paybutton_generic.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_day_of_week.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_day_of_week.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_year.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_year.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_popup_menu_header_item_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_popup_menu_header_item_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_shipping_info_page.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_shipping_info_page.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_navigation_item_separator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_navigation_item_separator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_layout_snackbar_include.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_layout_snackbar_include.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_navigation_item_subheader.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_navigation_item_subheader.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_navigation_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_navigation_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_media_action.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_media_action.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_fragment_primary_button_container.xml", "source": "com.flutter.stripe.stripe_android-jetified-paymentsheet-21.6.0-53:/layout/stripe_fragment_primary_button_container.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_media.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_media.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_vertical.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_vertical.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_card_brand_spinner_main.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_card_brand_spinner_main.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_horizontal.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_horizontal.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_calendar_month.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_calendar_month.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/m3_alert_dialog_actions.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/m3_alert_dialog_actions.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/wallet_test_layout.xml", "source": "com.flutter.stripe.stripe_android-jetified-play-services-wallet-19.4.0-13:/layout/wallet_test_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_time_chip.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_time_chip.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_fullscreen.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_fullscreen.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_payment_auth_web_view_activity.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_payment_auth_web_view_activity.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_navigation_menu.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_navigation_menu.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_auto_complete_simple_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_auto_complete_simple_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_lines_media.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_lines_media.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_layout_tab_text.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_layout_tab_text.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_textinput_timepicker.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_textinput_timepicker.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_action_bar_up_container.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_action_bar_up_container.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/ime_secondary_split_test_activity.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_masked_card_row.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/layout/stripe_masked_card_row.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_alert_select_dialog_singlechoice.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_alert_select_dialog_singlechoice.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_alert_dialog_title.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_alert_dialog_title.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_cascading_menu_item_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_cascading_menu_item_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_bottom_navigation_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_bottom_navigation_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_part_time.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/layout/notification_template_part_time.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_media_custom.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_media_custom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_action_bar_title_item.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_action_bar_title_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_text_input_date_range.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_text_input_date_range.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/select_dialog_multichoice_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/select_dialog_multichoice_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_timepicker_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_timepicker_dialog.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_alert_select_dialog_item.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_alert_select_dialog_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/material_clockface_textview.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/material_clockface_textview.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_action_menu_item_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_action_menu_item_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_action_menu_layout.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_action_menu_layout.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_select_dialog_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_select_dialog_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_alert_dialog_material.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_alert_dialog_material.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/abc_activity_chooser_view_list_item.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/layout/abc_activity_chooser_view_list_item.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_information_zone_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout/stripe_information_zone_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/stripe_hcaptcha_fragment.xml", "source": "com.flutter.stripe.stripe_android-jetified-hcaptcha-21.6.0-74:/layout/stripe_hcaptcha_fragment.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/design_text_input_end_icon.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/design_text_input_end_icon.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/notification_template_big_media.xml", "source": "com.flutter.stripe.stripe_android-media-1.0.0-54:/layout/notification_template_big_media.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_layout_snackbar.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_layout_snackbar.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout/mtrl_picker_header_fullscreen.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout/mtrl_picker_header_fullscreen.xml"}]