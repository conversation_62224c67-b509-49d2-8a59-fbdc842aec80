{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "162", "startColumns": "4", "startOffsets": "14922", "endColumns": "84", "endOffsets": "15002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5017,5124,5285,5418,5526,5668,5799,5916,6188,6363,6472,6641,6776,6945,7100,7164,7232", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "5119,5280,5413,5521,5663,5794,5911,6017,6358,6467,6636,6771,6940,7095,7159,7227,7316"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "49,50,72,73,75,86,87,147,148,150,151,154,155,159,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4753,4851,7493,7589,7771,8706,8792,13636,13729,13893,13963,14256,14341,14674,15176,15254,15322", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "4846,4935,7584,7687,7856,8787,8875,13724,13808,13958,14028,14336,14423,14742,15249,15317,15439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,211", "endColumns": "76,78,75", "endOffsets": "127,206,282"}, "to": {"startLines": "51,74,79", "startColumns": "4,4,4", "startOffsets": "4940,7692,8085", "endColumns": "76,78,75", "endOffsets": "5012,7766,8156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "167,168", "startColumns": "4,4", "startOffsets": "15444,15534", "endColumns": "89,89", "endOffsets": "15529,15619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,411,496,577,682,770,871,1005,1088,1149,1214,1308,1381,1442,1567,1633,1701,1762,1834,1894,1948,2068,2128,2190,2244,2321,2451,2538,2615,2705,2788,2870,3011,3091,3176,3303,3394,3470,3524,3577,3643,3717,3798,3869,3949,4022,4099,4176,4250,4360,4453,4528,4618,4709,4781,4859,4950,5004,5087,5155,5239,5326,5388,5452,5515,5587,5697,5810,5913,6022,6080,6137,6214,6299,6377", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "319,406,491,572,677,765,866,1000,1083,1144,1209,1303,1376,1437,1562,1628,1696,1757,1829,1889,1943,2063,2123,2185,2239,2316,2446,2533,2610,2700,2783,2865,3006,3086,3171,3298,3389,3465,3519,3572,3638,3712,3793,3864,3944,4017,4094,4171,4245,4355,4448,4523,4613,4704,4776,4854,4945,4999,5082,5150,5234,5321,5383,5447,5510,5582,5692,5805,5908,6017,6075,6132,6209,6294,6372,6446"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,76,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152,157,158,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3258,3345,3430,3511,3616,4435,4536,4670,7861,7922,8161,8633,8880,8941,9066,9132,9200,9261,9333,9393,9447,9567,9627,9689,9743,9820,9950,10037,10114,10204,10287,10369,10510,10590,10675,10802,10893,10969,11023,11076,11142,11216,11297,11368,11448,11521,11598,11675,11749,11859,11952,12027,12117,12208,12280,12358,12449,12503,12586,12654,12738,12825,12887,12951,13014,13086,13196,13309,13412,13521,13579,14033,14511,14596,14747", "endLines": "6,34,35,36,37,38,46,47,48,76,77,80,85,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152,157,158,160", "endColumns": "12,86,84,80,104,87,100,133,82,60,64,93,72,60,124,65,67,60,71,59,53,119,59,61,53,76,129,86,76,89,82,81,140,79,84,126,90,75,53,52,65,73,80,70,79,72,76,76,73,109,92,74,89,90,71,77,90,53,82,67,83,86,61,63,62,71,109,112,102,108,57,56,76,84,77,73", "endOffsets": "369,3340,3425,3506,3611,3699,4531,4665,4748,7917,7982,8250,8701,8936,9061,9127,9195,9256,9328,9388,9442,9562,9622,9684,9738,9815,9945,10032,10109,10199,10282,10364,10505,10585,10670,10797,10888,10964,11018,11071,11137,11211,11292,11363,11443,11516,11593,11670,11744,11854,11947,12022,12112,12203,12275,12353,12444,12498,12581,12649,12733,12820,12882,12946,13009,13081,13191,13304,13407,13516,13574,13631,14105,14591,14669,14816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "71,81,82,83", "startColumns": "4,4,4,4", "startOffsets": "7390,8255,8354,8470", "endColumns": "102,98,115,101", "endOffsets": "7488,8349,8465,8567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "39,40,41,42,43,44,45,161", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3704,3802,3904,4004,4105,4212,4320,14821", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3797,3899,3999,4100,4207,4315,4430,14917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,84", "endOffsets": "258,343"}, "to": {"startLines": "84,171", "startColumns": "4,4", "startOffsets": "8572,15791", "endColumns": "60,88", "endOffsets": "8628,15875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "60", "startColumns": "4", "startOffsets": "6022", "endColumns": "165", "endOffsets": "6183"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "374,494,604,713,799,903,1025,1107,1187,1297,1405,1511,1620,1731,1834,1946,2053,2158,2258,2343,2452,2563,2662,2773,2880,2985,3159,14428", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "489,599,708,794,898,1020,1102,1182,1292,1400,1506,1615,1726,1829,1941,2048,2153,2253,2338,2447,2558,2657,2768,2875,2980,3154,3253,14506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,272,352,498,667,752", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "169,267,347,493,662,747,829"}, "to": {"startLines": "70,78,149,153,163,169,170", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7321,7987,13813,14110,15007,15624,15709", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "7385,8080,13888,14251,15171,15704,15786"}}]}]}