1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.velvete.ly"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
15    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
17-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
18    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
18-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
19    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- Profile picture functionality permissions -->
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
22    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- Samsung -->
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
23-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
24    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
25    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
26    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
27    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
28    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
29    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
30    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
31    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
32    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
33    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
34    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
35
36    <queries>
36-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:10:5-39:15
37        <intent>
37-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
38            <action android:name="android.intent.action.VIEW" />
38-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
38-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
39
40            <data
40-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
41                android:mimeType="*/*"
41-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:15:17-39
42                android:scheme="*" />
42-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
43        </intent>
44        <intent>
44-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
45            <action android:name="android.intent.action.VIEW" />
45-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
45-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
46
47            <category android:name="android.intent.category.BROWSABLE" />
47-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:13-74
47-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:23-71
48
49            <data
49-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
50                android:host="pay"
51                android:mimeType="*/*"
51-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:15:17-39
52                android:scheme="upi" />
52-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
53        </intent>
54        <intent>
54-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
55            <action android:name="android.intent.action.MAIN" />
55-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
55-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
56        </intent>
57        <intent>
57-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
58            <action android:name="android.intent.action.SEND" />
58-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
58-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
59
60            <data android:mimeType="*/*" />
60-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
60-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:15:17-39
61        </intent>
62        <intent>
62-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
63            <action android:name="rzp.device_token.share" />
63-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
63-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
64        </intent>
65        <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
66        <package android:name="com.android.chrome" />
66-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
66-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
67
68        <intent>
68-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
69            <action android:name="android.support.customtabs.action.CustomTabsService" />
69-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
69-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
70        </intent> <!-- Added to check the default browser that will host the AuthFlow. -->
71        <intent>
71-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
72            <action android:name="android.intent.action.VIEW" />
72-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
72-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
73
74            <data android:scheme="http" />
74-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
74-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
75        </intent>
76
77        <package android:name="com.facebook.katana" /> <!-- Needs to be explicitly declared on Android R+ -->
77-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
77-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
78        <package android:name="com.google.android.apps.maps" />
78-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
78-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
79    </queries>
80
81    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
81-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
81-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
82    <uses-permission android:name="android.permission.WAKE_LOCK" />
82-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
82-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
83    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
83-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
83-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
84    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
84-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
84-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
85    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
85-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
85-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
86    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
86-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
86-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
87    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
87-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
87-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
88    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
89
90    <uses-feature
90-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
91        android:glEsVersion="0x00020000"
91-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
92        android:required="true" />
92-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
93
94    <permission
94-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
95        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
96        android:protectionLevel="signature" />
96-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
97
98    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
99    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
99-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
99-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
100
101    <application
102        android:name="android.app.Application"
103        android:allowBackup="false"
104        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
104-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
105        android:debuggable="true"
106        android:extractNativeLibs="true"
107        android:fullBackupContent="false"
108        android:icon="@mipmap/ic_launcher"
109        android:label="Label StoreMax"
110        android:supportsRtl="true"
110-->[com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:18-44
111        android:usesCleartextTraffic="true" >
112        <activity
113            android:name="com.velvete.ly.MainActivity"
114            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
115            android:exported="true"
116            android:hardwareAccelerated="true"
117            android:launchMode="singleTop"
118            android:screenOrientation="portrait"
119            android:theme="@style/LaunchTheme"
120            android:windowSoftInputMode="adjustResize" >
121            <meta-data
122                android:name="io.flutter.embedding.android.NormalTheme"
123                android:resource="@style/NormalTheme" />
124
125            <intent-filter>
126                <action android:name="android.intent.action.MAIN" />
126-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
126-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
127
128                <category android:name="android.intent.category.LAUNCHER" />
129            </intent-filter>
130        </activity>
131
132        <meta-data
133            android:name="flutterEmbedding"
134            android:value="2" />
135        <meta-data
136            android:name="com.google.android.geo.API_KEY"
137            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />
138
139        <activity
139-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
140            android:name="com.razorpay.CheckoutActivity"
140-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
141            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
141-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
142            android:exported="false"
142-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
143            android:theme="@style/CheckoutTheme" >
143-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
144            <intent-filter>
144-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
145                <action android:name="android.intent.action.MAIN" />
145-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
145-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
146            </intent-filter>
147        </activity>
148
149        <provider
149-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
150            android:name="androidx.startup.InitializationProvider"
150-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
151            android:authorities="com.velvete.ly.androidx-startup"
151-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
152            android:exported="false" >
152-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
153            <meta-data
153-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
154                android:name="com.razorpay.RazorpayInitializer"
154-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
155                android:value="androidx.startup" />
155-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
156            <meta-data
156-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
157                android:name="androidx.emoji2.text.EmojiCompatInitializer"
157-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
158                android:value="androidx.startup" />
158-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
159            <meta-data
159-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
160                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
160-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
161                android:value="androidx.startup" />
161-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
162            <meta-data
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
163                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
164                android:value="androidx.startup" />
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
165        </provider>
166
167        <activity
167-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
168            android:name="com.razorpay.MagicXActivity"
168-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
169            android:exported="false"
169-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
170            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
170-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
171
172        <meta-data
172-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
173            android:name="com.razorpay.plugin.googlepay_all"
173-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
174            android:value="com.razorpay.RzpGpayMerged" />
174-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
175
176        <activity
176-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
177            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
177-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
178            android:exported="false"
178-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
179            android:theme="@style/StripePaymentSheetDefaultTheme" />
179-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
180        <activity
180-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
181            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
181-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
182            android:exported="false"
182-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
183            android:theme="@style/StripePaymentSheetDefaultTheme" />
183-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
184        <activity
184-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
185            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
185-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
186            android:exported="false"
186-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
187            android:theme="@style/StripePaymentSheetDefaultTheme" />
187-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
188        <activity
188-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
189            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
189-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
190            android:exported="false"
190-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
191            android:theme="@style/StripePaymentSheetDefaultTheme" />
191-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
192        <activity
192-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
193            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
193-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
194            android:exported="false"
194-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
195            android:theme="@style/StripePaymentSheetDefaultTheme" />
195-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
196        <activity
196-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
197            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
197-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
198            android:exported="false"
198-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
199            android:theme="@style/StripePaymentSheetDefaultTheme" />
199-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
200        <activity
200-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
201            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
201-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
202            android:exported="false"
202-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
203            android:theme="@style/StripePaymentSheetDefaultTheme" />
203-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
204        <activity
204-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
205            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
205-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
206            android:exported="false"
206-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
207            android:theme="@style/StripePayLauncherDefaultTheme" />
207-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
208        <activity
208-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
209            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
209-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
210            android:theme="@style/StripePaymentSheetDefaultTheme" />
210-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
211        <activity
211-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
212            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
212-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
213            android:theme="@style/StripePaymentSheetDefaultTheme" />
213-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
214        <activity
214-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
215            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
215-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
216            android:theme="@style/StripePaymentSheetDefaultTheme" />
216-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
217        <activity
217-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
218            android:name="com.stripe.android.link.LinkActivity"
218-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
219            android:autoRemoveFromRecents="true"
219-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
220            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
220-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
221            android:exported="false"
221-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
222            android:label="@string/stripe_link"
222-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
223            android:theme="@style/StripeLinkBaseTheme"
223-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
224            android:windowSoftInputMode="adjustResize" />
224-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
225        <activity
225-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
226            android:name="com.stripe.android.link.LinkForegroundActivity"
226-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
227            android:autoRemoveFromRecents="true"
227-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
228            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
228-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
229            android:launchMode="singleTop"
229-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
230            android:theme="@style/StripeTransparentTheme" />
230-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
231        <activity
231-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
232            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
232-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
233            android:autoRemoveFromRecents="true"
233-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
234            android:exported="true"
234-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
235            android:launchMode="singleInstance"
235-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
236            android:theme="@style/StripeTransparentTheme" >
236-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
237            <intent-filter>
237-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
238                <action android:name="android.intent.action.VIEW" />
238-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
238-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
239
240                <category android:name="android.intent.category.DEFAULT" />
240-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:17-76
240-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:27-73
241                <category android:name="android.intent.category.BROWSABLE" />
241-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:13-74
241-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:23-71
242
243                <data
243-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
244                    android:host="complete"
245                    android:path="/com.velvete.ly"
246                    android:scheme="link-popup" />
246-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
247            </intent-filter>
248        </activity>
249        <activity
249-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
250            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
250-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
251            android:exported="false"
251-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
252            android:theme="@style/StripePaymentSheetDefaultTheme" />
252-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
253        <activity
253-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
254            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
254-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
255            android:exported="false"
255-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
256            android:theme="@style/StripeDefaultTheme" />
256-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
257        <activity
257-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
258            android:name="com.stripe.android.view.PaymentRelayActivity"
258-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
259            android:exported="false"
259-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
260            android:theme="@style/StripeTransparentTheme" />
260-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
261        <!--
262        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
263        launched the browser Activity will also handle the return URL deep link.
264        -->
265        <activity
265-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
266            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
266-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
267            android:exported="false"
267-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
268            android:launchMode="singleTask"
268-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
269            android:theme="@style/StripeTransparentTheme" />
269-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
270        <activity
270-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
271            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
271-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
272            android:exported="true"
272-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
273            android:launchMode="singleTask"
273-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
274            android:theme="@style/StripeTransparentTheme" >
274-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
275            <intent-filter>
275-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
276                <action android:name="android.intent.action.VIEW" />
276-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
276-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
277
278                <category android:name="android.intent.category.DEFAULT" />
278-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:17-76
278-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:27-73
279                <category android:name="android.intent.category.BROWSABLE" />
279-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:13-74
279-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:23-71
280
281                <!-- Must match `DefaultReturnUrl#value`. -->
282                <data
282-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
283                    android:host="payment_return_url"
284                    android:path="/com.velvete.ly"
285                    android:scheme="stripesdk" />
285-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
286            </intent-filter>
287        </activity>
288        <activity
288-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
289            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
289-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
290            android:exported="false"
290-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
291            android:theme="@style/StripeDefaultTheme" />
291-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
292        <activity
292-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
293            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
293-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
294            android:exported="false"
294-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
295            android:theme="@style/StripeGooglePayDefaultTheme" />
295-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
296        <activity
296-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
297            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
297-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
298            android:exported="false"
298-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
299            android:theme="@style/StripeGooglePayDefaultTheme" />
299-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
300        <activity
300-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
301            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
301-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
302            android:exported="false"
302-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
303            android:theme="@style/StripePayLauncherDefaultTheme" />
303-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
304        <activity
304-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
305            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
305-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
306            android:exported="false"
306-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
307            android:theme="@style/StripeTransparentTheme" />
307-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
308        <activity
308-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
309            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
309-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
310            android:exported="false"
310-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
311            android:theme="@style/Stripe3DS2Theme" />
311-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
312
313        <service
313-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
314            android:name="com.baseflow.geolocator.GeolocatorLocationService"
314-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
315            android:enabled="true"
315-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
316            android:exported="false"
316-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
317            android:foregroundServiceType="location" />
317-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
318
319        <provider
319-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
320            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
320-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
321            android:authorities="com.velvete.ly.flutter.image_provider"
321-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
322            android:exported="false"
322-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
323            android:grantUriPermissions="true" >
323-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
324            <meta-data
324-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
325                android:name="android.support.FILE_PROVIDER_PATHS"
325-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
326                android:resource="@xml/flutter_image_picker_file_paths" />
326-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
327        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
328        <service
328-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
329            android:name="com.google.android.gms.metadata.ModuleDependencies"
329-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
330            android:enabled="false"
330-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
331            android:exported="false" >
331-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
332            <intent-filter>
332-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
333                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
333-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
333-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
334            </intent-filter>
335
336            <meta-data
336-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
337                android:name="photopicker_activity:0:required"
337-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
338                android:value="" />
338-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
339        </service>
340
341        <activity
341-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
342            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
342-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
343            android:exported="false"
343-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
344            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
344-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
345
346        <service
346-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
347            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
347-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
348            android:exported="false"
348-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
349            android:permission="android.permission.BIND_JOB_SERVICE" />
349-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
350        <service
350-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
351            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
351-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
352            android:exported="false" >
352-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
353            <intent-filter>
353-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
354                <action android:name="com.google.firebase.MESSAGING_EVENT" />
354-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
354-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
355            </intent-filter>
356        </service>
357
358        <receiver
358-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
359            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
359-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
360            android:exported="true"
360-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
361            android:permission="com.google.android.c2dm.permission.SEND" >
361-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
362            <intent-filter>
362-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
363                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
363-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
363-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
364            </intent-filter>
365        </receiver>
366
367        <service
367-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
368            android:name="com.google.firebase.components.ComponentDiscoveryService"
368-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
369            android:directBootAware="true"
369-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
370            android:exported="false" >
370-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
371            <meta-data
371-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
372                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
372-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
373                android:value="com.google.firebase.components.ComponentRegistrar" />
373-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
374            <meta-data
374-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
375                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
375-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
376                android:value="com.google.firebase.components.ComponentRegistrar" />
376-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
377            <meta-data
377-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
378                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
378-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
379                android:value="com.google.firebase.components.ComponentRegistrar" />
379-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
380            <meta-data
380-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
381                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
381-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
382                android:value="com.google.firebase.components.ComponentRegistrar" />
382-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
383            <meta-data
383-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
384                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
384-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
385                android:value="com.google.firebase.components.ComponentRegistrar" />
385-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
386            <meta-data
386-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
387                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
387-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
388                android:value="com.google.firebase.components.ComponentRegistrar" />
388-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
389            <meta-data
389-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
390                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
390-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
391                android:value="com.google.firebase.components.ComponentRegistrar" />
391-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
392            <meta-data
392-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
393                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
393-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
394                android:value="com.google.firebase.components.ComponentRegistrar" />
394-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
395            <meta-data
395-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
396                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
396-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
397                android:value="com.google.firebase.components.ComponentRegistrar" />
397-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
398        </service>
399
400        <provider
400-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
401            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
401-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
402            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
402-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
403            android:exported="false"
403-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
404            android:initOrder="99" />
404-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
405
406        <activity
406-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
407            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
407-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
408            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
408-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
409            android:exported="false"
409-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
410            android:theme="@style/AppTheme" />
410-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
411        <activity
411-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
412            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
412-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
413            android:exported="false"
413-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
414            android:theme="@style/ThemeTransparent" />
414-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
415        <activity
415-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
416            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
416-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
417            android:exported="false"
417-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
418            android:theme="@style/ThemeTransparent" />
418-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
419        <activity
419-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
420            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
420-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
421            android:exported="false"
421-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
422            android:launchMode="singleInstance"
422-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
423            android:theme="@style/ThemeTransparent" />
423-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
424        <activity
424-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
425            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
425-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
426            android:exported="false"
426-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
427            android:launchMode="singleInstance"
427-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
428            android:theme="@style/ThemeTransparent" />
428-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
429
430        <receiver
430-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
431            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
431-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
432            android:enabled="true"
432-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
433            android:exported="false" />
433-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
434
435        <meta-data
435-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
436            android:name="io.flutter.embedded_views_preview"
436-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
437            android:value="true" />
437-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
438
439        <activity
439-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
440            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
440-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
441            android:exported="true"
441-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
442            android:launchMode="singleTask" >
442-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
443            <intent-filter>
443-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
444                <action android:name="android.intent.action.VIEW" />
444-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
444-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
445
446                <category android:name="android.intent.category.DEFAULT" />
446-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:17-76
446-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:27-73
447                <category android:name="android.intent.category.BROWSABLE" />
447-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:13-74
447-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:23-71
448
449                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
450                <data
450-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
451                    android:host="link-accounts"
452                    android:pathPrefix="/com.velvete.ly/authentication_return"
453                    android:scheme="stripe-auth" />
453-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
454
455                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
456                <data
456-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
457                    android:host="link-native-accounts"
458                    android:pathPrefix="/com.velvete.ly/authentication_return"
459                    android:scheme="stripe-auth" />
459-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
460
461                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
462                <data
462-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
463                    android:host="link-accounts"
464                    android:path="/com.velvete.ly/success"
465                    android:scheme="stripe-auth" />
465-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
466                <data
466-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
467                    android:host="link-accounts"
468                    android:path="/com.velvete.ly/cancel"
469                    android:scheme="stripe-auth" />
469-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
470
471                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
472                <data
472-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
473                    android:host="native-redirect"
474                    android:pathPrefix="/com.velvete.ly"
475                    android:scheme="stripe-auth" />
475-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
476
477                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
478                <data
478-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
479                    android:host="auth-redirect"
480                    android:pathPrefix="/com.velvete.ly"
481                    android:scheme="stripe" />
481-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
482            </intent-filter>
483        </activity>
484        <activity
484-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
485            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
485-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
486            android:exported="false"
486-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
487            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
487-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
488        <activity
488-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
489            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
489-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
490            android:exported="false"
490-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
491            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
491-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
492            android:windowSoftInputMode="adjustResize" />
492-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
493        <activity
493-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
494            android:name="com.facebook.FacebookActivity"
494-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:21:13-57
495            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
495-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:22:13-96
496            android:theme="@style/com_facebook_activity_theme" />
496-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
497        <activity android:name="com.facebook.CustomTabMainActivity" />
497-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
497-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
498        <activity
498-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
499            android:name="com.facebook.CustomTabActivity"
499-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:26:13-58
500            android:exported="true" >
500-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:27:13-36
501            <intent-filter>
501-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
502                <action android:name="android.intent.action.VIEW" />
502-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
502-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
503
504                <category android:name="android.intent.category.DEFAULT" />
504-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:17-76
504-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:27-73
505                <category android:name="android.intent.category.BROWSABLE" />
505-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:13-74
505-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:23-71
506
507                <data
507-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
508                    android:host="cct.com.velvete.ly"
509                    android:scheme="fbconnect" />
509-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
510            </intent-filter>
511        </activity>
512        <activity
512-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
513            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
513-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
514            android:excludeFromRecents="true"
514-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
515            android:exported="false"
515-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
516            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
516-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
517        <!--
518            Service handling Google Sign-In user revocation. For apps that do not integrate with
519            Google Sign-In, this service will never be started.
520        -->
521        <service
521-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
522            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
522-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
523            android:exported="true"
523-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
524            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
524-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
525            android:visibleToInstantApps="true" />
525-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
526        <!--
527         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
528         with the application context. This config is merged in with the host app's manifest,
529         but there can only be one provider with the same authority activated at any given
530         point; so if the end user has two or more different apps that use Facebook SDK, only the
531         first one will be able to use the provider. To work around this problem, we use the
532         following placeholder in the authority to identify each host application as if it was
533         a completely different provider.
534        -->
535        <provider
535-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
536            android:name="com.facebook.internal.FacebookInitProvider"
536-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
537            android:authorities="com.velvete.ly.FacebookInitProvider"
537-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
538            android:exported="false" />
538-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
539
540        <receiver
540-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
541            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
541-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
542            android:exported="false" >
542-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
543            <intent-filter>
543-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
544                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
544-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
544-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
545            </intent-filter>
546        </receiver>
547        <receiver
547-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
548            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
548-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
549            android:exported="false" >
549-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
550            <intent-filter>
550-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
551                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
551-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
551-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
552            </intent-filter>
553        </receiver>
554        <receiver
554-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
555            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
555-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
556            android:exported="true"
556-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
557            android:permission="com.google.android.c2dm.permission.SEND" >
557-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
558            <intent-filter>
558-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
559                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
559-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
559-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
560            </intent-filter>
561
562            <meta-data
562-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
563                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
563-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
564                android:value="true" />
564-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
565        </receiver>
566        <!--
567             FirebaseMessagingService performs security checks at runtime,
568             but set to not exported to explicitly avoid allowing another app to call it.
569        -->
570        <service
570-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
571            android:name="com.google.firebase.messaging.FirebaseMessagingService"
571-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
572            android:directBootAware="true"
572-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
573            android:exported="false" >
573-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
574            <intent-filter android:priority="-500" >
574-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
575                <action android:name="com.google.firebase.MESSAGING_EVENT" />
575-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
575-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
576            </intent-filter>
577        </service> <!-- Needs to be explicitly declared on P+ -->
578        <uses-library
578-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
579            android:name="org.apache.http.legacy"
579-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
580            android:required="false" />
580-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
581
582        <activity
582-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
583            android:name="com.google.android.gms.common.api.GoogleApiActivity"
583-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
584            android:exported="false"
584-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
585            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
585-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
586
587        <provider
587-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
588            android:name="com.google.firebase.provider.FirebaseInitProvider"
588-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
589            android:authorities="com.velvete.ly.firebaseinitprovider"
589-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
590            android:directBootAware="true"
590-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
591            android:exported="false"
591-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
592            android:initOrder="100" />
592-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
593
594        <uses-library
594-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
595            android:name="androidx.window.extensions"
595-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
596            android:required="false" />
596-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
597        <uses-library
597-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
598            android:name="androidx.window.sidecar"
598-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
599            android:required="false" />
599-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
600
601        <meta-data
601-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
602            android:name="com.google.android.gms.version"
602-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
603            android:value="@integer/google_play_services_version" />
603-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
604
605        <receiver
605-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
606            android:name="androidx.profileinstaller.ProfileInstallReceiver"
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
607            android:directBootAware="false"
607-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
608            android:enabled="true"
608-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
609            android:exported="true"
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
610            android:permission="android.permission.DUMP" >
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
611            <intent-filter>
611-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
612                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
612-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
612-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
613            </intent-filter>
614            <intent-filter>
614-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
615                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
615-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
615-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
616            </intent-filter>
617            <intent-filter>
617-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
618                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
618-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
618-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
619            </intent-filter>
620            <intent-filter>
620-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
621                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
621-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
621-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
622            </intent-filter>
623        </receiver>
624
625        <service
625-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
626            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
626-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
627            android:exported="false" >
627-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
628            <meta-data
628-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
629                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
629-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
630                android:value="cct" />
630-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
631        </service>
632        <service
632-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
633            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
633-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
634            android:exported="false"
634-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
635            android:permission="android.permission.BIND_JOB_SERVICE" >
635-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
636        </service>
637
638        <receiver
638-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
639            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
639-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
640            android:exported="false" />
640-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
641
642        <meta-data
642-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
643            android:name="aia-compat-api-min-version"
643-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
644            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
644-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
645        <activity
645-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
646            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
646-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
647            android:exported="false"
647-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
648            android:stateNotNeeded="true"
648-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
649            android:theme="@style/Theme.PlayCore.Transparent" />
649-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
650    </application>
651
652</manifest>
