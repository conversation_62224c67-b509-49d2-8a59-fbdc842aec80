{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,209", "endColumns": "81,71,81", "endOffsets": "132,204,286"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4768,10070,10437", "endColumns": "81,71,81", "endOffsets": "4845,10137,10514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3635,3738,3839,3945,4046,4154,17202", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3630,3733,3834,3940,4041,4149,4277,17298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,529,636,762,840,916,1007,1100,1195,1289,1389,1482,1577,1671,1762,1853,1935,2051,2161,2260,2373,2478,2592,2756,2856", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,524,631,757,835,911,1002,1095,1190,1284,1384,1477,1572,1666,1757,1848,1930,2046,2156,2255,2368,2473,2587,2751,2851,2934"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "327,441,553,666,751,858,984,1062,1138,1229,1322,1417,1511,1611,1704,1799,1893,1984,2075,2157,2273,2383,2482,2595,2700,2814,2978,16795", "endColumns": "113,111,112,84,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "436,548,661,746,853,979,1057,1133,1224,1317,1412,1506,1606,1699,1794,1888,1979,2070,2152,2268,2378,2477,2590,2695,2809,2973,3073,16873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "106,193", "startColumns": "4,4", "startOffsets": "10930,18169", "endColumns": "60,79", "endOffsets": "10986,18244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,350,493,608,700,785,869,961,1052,1171,1289,1381,1473,1581,1705,1788,1875,2072,2168,2268,2384,2490", "endColumns": "162,131,142,114,91,84,83,91,90,118,117,91,91,107,123,82,86,196,95,99,115,105,168", "endOffsets": "213,345,488,603,695,780,864,956,1047,1166,1284,1376,1468,1576,1700,1783,1870,2067,2163,2263,2379,2485,2654"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4850,5013,5145,5288,5403,5495,5580,5664,5756,5847,5966,6084,6176,6268,6376,6500,6583,6670,6867,6963,7063,7179,7285", "endColumns": "162,131,142,114,91,84,83,91,90,118,117,91,91,107,123,82,86,196,95,99,115,105,168", "endOffsets": "5008,5140,5283,5398,5490,5575,5659,5751,5842,5961,6079,6171,6263,6371,6495,6578,6665,6862,6958,7058,7174,7280,7449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,338,487,656,736", "endColumns": "69,85,76,148,168,79,76", "endOffsets": "170,256,333,482,651,731,808"}, "to": {"startLines": "92,100,171,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9690,10351,16177,16478,17396,18012,18092", "endColumns": "69,85,76,148,168,79,76", "endOffsets": "9755,10432,16249,16622,17560,18087,18164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4587,4684,9874,9970,10142,11057,11141,16001,16092,16254,16325,16627,16709,17049,17565,17642,17711", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "4679,4763,9965,10065,10226,11136,11229,16087,16172,16320,16391,16704,16790,17123,17637,17706,17824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,277,361,444,526,641,736,843,956,1041,1098,1161,1255,1321,1383,1486,1552,1623,1682,1758,1823,1877,1990,2048,2109,2163,2242,2358,2444,2527,2622,2708,2799,2941,3020,3099,3228,3316,3400,3457,3509,3575,3655,3745,3816,3895,3972,4049,4126,4195,4312,4411,4488,4581,4676,4750,4831,4927,4978,5062,5130,5216,5304,5367,5432,5495,5563,5668,5773,5868,5971,6032,6088,6170,6262,6341", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,128,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81,91,78,73", "endOffsets": "272,356,439,521,636,731,838,951,1036,1093,1156,1250,1316,1378,1481,1547,1618,1677,1753,1818,1872,1985,2043,2104,2158,2237,2353,2439,2522,2617,2703,2794,2936,3015,3094,3223,3311,3395,3452,3504,3570,3650,3740,3811,3890,3967,4044,4121,4190,4307,4406,4483,4576,4671,4745,4826,4922,4973,5057,5125,5211,5299,5362,5427,5490,5558,5663,5768,5863,5966,6027,6083,6165,6257,6336,6410"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3078,3162,3245,3327,3442,4282,4389,4502,10231,10288,10519,10991,11234,11296,11399,11465,11536,11595,11671,11736,11790,11903,11961,12022,12076,12155,12271,12357,12440,12535,12621,12712,12854,12933,13012,13141,13229,13313,13370,13422,13488,13568,13658,13729,13808,13885,13962,14039,14108,14225,14324,14401,14494,14589,14663,14744,14840,14891,14975,15043,15129,15217,15280,15345,15408,15476,15581,15686,15781,15884,15945,16396,16878,16970,17128", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,83,82,81,114,94,106,112,84,56,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,85,82,94,85,90,141,78,78,128,87,83,56,51,65,79,89,70,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81,91,78,73", "endOffsets": "322,3157,3240,3322,3437,3532,4384,4497,4582,10283,10346,10608,11052,11291,11394,11460,11531,11590,11666,11731,11785,11898,11956,12017,12071,12150,12266,12352,12435,12530,12616,12707,12849,12928,13007,13136,13224,13308,13365,13417,13483,13563,13653,13724,13803,13880,13957,14034,14103,14220,14319,14396,14489,14584,14658,14739,14835,14886,14970,15038,15124,15212,15275,15340,15403,15471,15576,15681,15776,15879,15940,15996,16473,16965,17044,17197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7454,7565,7735,7868,7983,8126,8255,8363,8608,8758,8871,9036,9171,9316,9473,9542,9605", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "7560,7730,7863,7978,8121,8250,8358,8457,8753,8866,9031,9166,9311,9468,9537,9600,9685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8462", "endColumns": "145", "endOffsets": "8603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "184", "startColumns": "4", "startOffsets": "17303", "endColumns": "92", "endOffsets": "17391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "189,190", "startColumns": "4,4", "startOffsets": "17829,17917", "endColumns": "87,94", "endOffsets": "17912,18007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9760,10613,10713,10829", "endColumns": "113,99,115,100", "endOffsets": "9869,10708,10824,10925"}}]}]}