  SuppressLint android.annotation  Notification android.app  equals android.app.Notification  getJAVAClass android.app.Notification  getJavaClass android.app.Notification  	javaClass android.app.Notification  AsyncQueryHandler android.content  
ComponentName android.content  ContentResolver android.content  
ContentValues android.content  Context android.content  Intent android.content  equals !android.content.AsyncQueryHandler  startInsert !android.content.AsyncQueryHandler  	className android.content.ComponentName  flattenToShortString android.content.ComponentName  flattenToString android.content.ComponentName  getCLASSName android.content.ComponentName  getClassName android.content.ComponentName  getPACKAGEName android.content.ComponentName  getPackageName android.content.ComponentName  packageName android.content.ComponentName  setClassName android.content.ComponentName  setPackageName android.content.ComponentName  call android.content.ContentResolver  insert android.content.ContentResolver  put android.content.ContentValues  applicationContext android.content.Context  contentResolver android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getLET android.content.Context  getLet android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  let android.content.Context  packageManager android.content.Context  packageName android.content.Context  
sendBroadcast android.content.Context  setApplicationContext android.content.Context  setContentResolver android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  ACTION_MAIN android.content.Intent  
CATEGORY_HOME android.content.Intent  action android.content.Intent  addCategory android.content.Intent  	component android.content.Intent  equals android.content.Intent  	getACTION android.content.Intent  	getAction android.content.Intent  getCOMPONENT android.content.Intent  getComponent android.content.Intent  putExtra android.content.Intent  	setAction android.content.Intent  setComponent android.content.Intent  
setPackage android.content.Intent  PackageManager android.content.pm  ProviderInfo android.content.pm  ResolveInfo android.content.pm  packageName android.content.pm.ActivityInfo  MATCH_DEFAULT_ONLY !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  queryBroadcastReceivers !android.content.pm.PackageManager  queryIntentActivities !android.content.pm.PackageManager  resolveActivity !android.content.pm.PackageManager  resolveContentProvider !android.content.pm.PackageManager  equals android.content.pm.ProviderInfo  activityInfo android.content.pm.ResolveInfo  resolvePackageName android.content.pm.ResolveInfo  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Looper 
android.os  putInt android.os.BaseBundle  	putString android.os.BaseBundle  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  putInt android.os.Bundle  	putString android.os.Bundle  startInsert android.os.Handler  equals android.os.Looper  
getMainLooper android.os.Looper  myLooper android.os.Looper  Log android.util  e android.util.Log  i android.util.Log  Keep androidx.annotation  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	arguments #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Any 	java.lang  ApexLauncherBadge 	java.lang  AsusLauncherBadge 	java.lang  BADGE_CONTENT_URI 	java.lang  Badge 	java.lang  BadgeException 	java.lang  
BroadcastTool 	java.lang  Build 	java.lang  Bundle 	java.lang  
CLASS_NAME 	java.lang  COLUMN_BADGES_COUNT 	java.lang  COLUMN_CLASS 	java.lang  COLUMN_PACKAGE 	java.lang  CONTENT_URI 	java.lang  COUNT 	java.lang  Class 	java.lang  Collections 	java.lang  
ContentValues 	java.lang  DefaultBadge 	java.lang  EXTRA_BADGE_COMPONENT 	java.lang  EXTRA_BADGE_COUNT 	java.lang  EXTRA_COMPONENT 	java.lang  EXTRA_COUNT 	java.lang  	Exception 	java.lang  HtcLauncherBadge 	java.lang  HuaweiLauncherBadge 	java.lang  
INTENT_ACTION 	java.lang  INTENT_EXTRA_ACTIVITY_NAME 	java.lang  INTENT_EXTRA_BADGE_COUNT 	java.lang   INTENT_EXTRA_BADGE_UPGRADE_COUNT 	java.lang  INTENT_EXTRA_CLASS_NAME 	java.lang  INTENT_EXTRA_MESSAGE 	java.lang  INTENT_EXTRA_PACKAGE_NAME 	java.lang  INTENT_EXTRA_SHOW_MESSAGE 	java.lang  INTENT_SET_NOTIFICATION 	java.lang  INTENT_UPDATE_COUNTER 	java.lang  INTENT_UPDATE_SHORTCUT 	java.lang  Int 	java.lang  Intent 	java.lang  LGLauncherBadge 	java.lang  LauncherTool 	java.lang  Log 	java.lang  Looper 	java.lang  METHOD_TO_CALL 	java.lang  
MethodChannel 	java.lang  	MiUIBadge 	java.lang  NowaLauncherBadge 	java.lang  OPPOLauncherBadge 	java.lang  PACKAGE_NAME 	java.lang  PROVIDER_COLUMNS_ACTIVITY_NAME 	java.lang  PROVIDER_COLUMNS_BADGE_COUNT 	java.lang  PROVIDER_COLUMNS_PACKAGE_NAME 	java.lang  PROVIDER_CONTENT_URI 	java.lang  PackageManager 	java.lang  SONY_HOME_PROVIDER_NAME 	java.lang  SamsungLauncherBadge 	java.lang  SonyLauncherBadge 	java.lang  String 	java.lang  Uri 	java.lang  VivoLauncherBadge 	java.lang  YandexLauncherBadge 	java.lang  ZTELauncherBadge 	java.lang  contains 	java.lang  	emptyList 	java.lang  get 	java.lang  indices 	java.lang  java 	java.lang  	javaClass 	java.lang  javaPrimitiveType 	java.lang  let 	java.lang  listOf 	java.lang  
mutableListOf 	java.lang  plus 	java.lang  synchronized 	java.lang  until 	java.lang  getDeclaredConstructor java.lang.Class  getDeclaredField java.lang.Class  getDeclaredMethod java.lang.Class  String java.lang.Exception  	Throwable java.lang.Exception  valueOf java.lang.String  Field java.lang.reflect  Method java.lang.reflect  get "java.lang.reflect.AccessibleObject  invoke "java.lang.reflect.AccessibleObject  newInstance "java.lang.reflect.AccessibleObject  newInstance java.lang.reflect.Constructor  invoke java.lang.reflect.Executable  newInstance java.lang.reflect.Executable  get java.lang.reflect.Field  invoke java.lang.reflect.Method  Collections 	java.util  swap java.util.Collections  Any kotlin  ApexLauncherBadge kotlin  AsusLauncherBadge kotlin  BADGE_CONTENT_URI kotlin  Badge kotlin  BadgeException kotlin  Boolean kotlin  
BroadcastTool kotlin  Build kotlin  Bundle kotlin  
CLASS_NAME kotlin  COLUMN_BADGES_COUNT kotlin  COLUMN_CLASS kotlin  COLUMN_PACKAGE kotlin  CONTENT_URI kotlin  COUNT kotlin  Class kotlin  Collections kotlin  
ContentValues kotlin  DefaultBadge kotlin  EXTRA_BADGE_COMPONENT kotlin  EXTRA_BADGE_COUNT kotlin  EXTRA_COMPONENT kotlin  EXTRA_COUNT kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  HtcLauncherBadge kotlin  HuaweiLauncherBadge kotlin  
INTENT_ACTION kotlin  INTENT_EXTRA_ACTIVITY_NAME kotlin  INTENT_EXTRA_BADGE_COUNT kotlin   INTENT_EXTRA_BADGE_UPGRADE_COUNT kotlin  INTENT_EXTRA_CLASS_NAME kotlin  INTENT_EXTRA_MESSAGE kotlin  INTENT_EXTRA_PACKAGE_NAME kotlin  INTENT_EXTRA_SHOW_MESSAGE kotlin  INTENT_SET_NOTIFICATION kotlin  INTENT_UPDATE_COUNTER kotlin  INTENT_UPDATE_SHORTCUT kotlin  Int kotlin  Intent kotlin  LGLauncherBadge kotlin  LauncherTool kotlin  Log kotlin  Looper kotlin  METHOD_TO_CALL kotlin  
MethodChannel kotlin  	MiUIBadge kotlin  Nothing kotlin  NowaLauncherBadge kotlin  OPPOLauncherBadge kotlin  PACKAGE_NAME kotlin  PROVIDER_COLUMNS_ACTIVITY_NAME kotlin  PROVIDER_COLUMNS_BADGE_COUNT kotlin  PROVIDER_COLUMNS_PACKAGE_NAME kotlin  PROVIDER_CONTENT_URI kotlin  PackageManager kotlin  SONY_HOME_PROVIDER_NAME kotlin  SamsungLauncherBadge kotlin  SonyLauncherBadge kotlin  String kotlin  	Throwable kotlin  Throws kotlin  Unit kotlin  Uri kotlin  VivoLauncherBadge kotlin  Volatile kotlin  YandexLauncherBadge kotlin  ZTELauncherBadge kotlin  contains kotlin  	emptyList kotlin  get kotlin  indices kotlin  java kotlin  	javaClass kotlin  javaPrimitiveType kotlin  let kotlin  listOf kotlin  
mutableListOf kotlin  plus kotlin  synchronized kotlin  until kotlin  getJAVAClass 
kotlin.Any  getJavaClass 
kotlin.Any  	Companion 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  getPLUS 
kotlin.String  getPlus 
kotlin.String  Any kotlin.annotation  ApexLauncherBadge kotlin.annotation  AsusLauncherBadge kotlin.annotation  BADGE_CONTENT_URI kotlin.annotation  Badge kotlin.annotation  BadgeException kotlin.annotation  
BroadcastTool kotlin.annotation  Build kotlin.annotation  Bundle kotlin.annotation  
CLASS_NAME kotlin.annotation  COLUMN_BADGES_COUNT kotlin.annotation  COLUMN_CLASS kotlin.annotation  COLUMN_PACKAGE kotlin.annotation  CONTENT_URI kotlin.annotation  COUNT kotlin.annotation  Class kotlin.annotation  Collections kotlin.annotation  
ContentValues kotlin.annotation  DefaultBadge kotlin.annotation  EXTRA_BADGE_COMPONENT kotlin.annotation  EXTRA_BADGE_COUNT kotlin.annotation  EXTRA_COMPONENT kotlin.annotation  EXTRA_COUNT kotlin.annotation  	Exception kotlin.annotation  HtcLauncherBadge kotlin.annotation  HuaweiLauncherBadge kotlin.annotation  
INTENT_ACTION kotlin.annotation  INTENT_EXTRA_ACTIVITY_NAME kotlin.annotation  INTENT_EXTRA_BADGE_COUNT kotlin.annotation   INTENT_EXTRA_BADGE_UPGRADE_COUNT kotlin.annotation  INTENT_EXTRA_CLASS_NAME kotlin.annotation  INTENT_EXTRA_MESSAGE kotlin.annotation  INTENT_EXTRA_PACKAGE_NAME kotlin.annotation  INTENT_EXTRA_SHOW_MESSAGE kotlin.annotation  INTENT_SET_NOTIFICATION kotlin.annotation  INTENT_UPDATE_COUNTER kotlin.annotation  INTENT_UPDATE_SHORTCUT kotlin.annotation  Int kotlin.annotation  Intent kotlin.annotation  LGLauncherBadge kotlin.annotation  LauncherTool kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  METHOD_TO_CALL kotlin.annotation  
MethodChannel kotlin.annotation  	MiUIBadge kotlin.annotation  NowaLauncherBadge kotlin.annotation  OPPOLauncherBadge kotlin.annotation  PACKAGE_NAME kotlin.annotation  PROVIDER_COLUMNS_ACTIVITY_NAME kotlin.annotation  PROVIDER_COLUMNS_BADGE_COUNT kotlin.annotation  PROVIDER_COLUMNS_PACKAGE_NAME kotlin.annotation  PROVIDER_CONTENT_URI kotlin.annotation  PackageManager kotlin.annotation  SONY_HOME_PROVIDER_NAME kotlin.annotation  SamsungLauncherBadge kotlin.annotation  SonyLauncherBadge kotlin.annotation  Throws kotlin.annotation  Uri kotlin.annotation  VivoLauncherBadge kotlin.annotation  Volatile kotlin.annotation  YandexLauncherBadge kotlin.annotation  ZTELauncherBadge kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  get kotlin.annotation  indices kotlin.annotation  java kotlin.annotation  	javaClass kotlin.annotation  javaPrimitiveType kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  
mutableListOf kotlin.annotation  plus kotlin.annotation  synchronized kotlin.annotation  until kotlin.annotation  Any kotlin.collections  ApexLauncherBadge kotlin.collections  AsusLauncherBadge kotlin.collections  BADGE_CONTENT_URI kotlin.collections  Badge kotlin.collections  BadgeException kotlin.collections  
BroadcastTool kotlin.collections  Build kotlin.collections  Bundle kotlin.collections  
CLASS_NAME kotlin.collections  COLUMN_BADGES_COUNT kotlin.collections  COLUMN_CLASS kotlin.collections  COLUMN_PACKAGE kotlin.collections  CONTENT_URI kotlin.collections  COUNT kotlin.collections  Class kotlin.collections  Collections kotlin.collections  
ContentValues kotlin.collections  DefaultBadge kotlin.collections  EXTRA_BADGE_COMPONENT kotlin.collections  EXTRA_BADGE_COUNT kotlin.collections  EXTRA_COMPONENT kotlin.collections  EXTRA_COUNT kotlin.collections  	Exception kotlin.collections  HtcLauncherBadge kotlin.collections  HuaweiLauncherBadge kotlin.collections  
INTENT_ACTION kotlin.collections  INTENT_EXTRA_ACTIVITY_NAME kotlin.collections  INTENT_EXTRA_BADGE_COUNT kotlin.collections   INTENT_EXTRA_BADGE_UPGRADE_COUNT kotlin.collections  INTENT_EXTRA_CLASS_NAME kotlin.collections  INTENT_EXTRA_MESSAGE kotlin.collections  INTENT_EXTRA_PACKAGE_NAME kotlin.collections  INTENT_EXTRA_SHOW_MESSAGE kotlin.collections  INTENT_SET_NOTIFICATION kotlin.collections  INTENT_UPDATE_COUNTER kotlin.collections  INTENT_UPDATE_SHORTCUT kotlin.collections  Int kotlin.collections  Intent kotlin.collections  LGLauncherBadge kotlin.collections  LauncherTool kotlin.collections  List kotlin.collections  Log kotlin.collections  Looper kotlin.collections  METHOD_TO_CALL kotlin.collections  Map kotlin.collections  
MethodChannel kotlin.collections  	MiUIBadge kotlin.collections  MutableList kotlin.collections  NowaLauncherBadge kotlin.collections  OPPOLauncherBadge kotlin.collections  PACKAGE_NAME kotlin.collections  PROVIDER_COLUMNS_ACTIVITY_NAME kotlin.collections  PROVIDER_COLUMNS_BADGE_COUNT kotlin.collections  PROVIDER_COLUMNS_PACKAGE_NAME kotlin.collections  PROVIDER_CONTENT_URI kotlin.collections  PackageManager kotlin.collections  SONY_HOME_PROVIDER_NAME kotlin.collections  SamsungLauncherBadge kotlin.collections  SonyLauncherBadge kotlin.collections  Throws kotlin.collections  Uri kotlin.collections  VivoLauncherBadge kotlin.collections  Volatile kotlin.collections  YandexLauncherBadge kotlin.collections  ZTELauncherBadge kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  get kotlin.collections  indices kotlin.collections  java kotlin.collections  	javaClass kotlin.collections  javaPrimitiveType kotlin.collections  let kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  plus kotlin.collections  synchronized kotlin.collections  until kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getCONTAINS kotlin.collections.List  getContains kotlin.collections.List  
getINDICES kotlin.collections.List  
getIndices kotlin.collections.List  getGET kotlin.collections.Map  getGet kotlin.collections.Map  Any kotlin.comparisons  ApexLauncherBadge kotlin.comparisons  AsusLauncherBadge kotlin.comparisons  BADGE_CONTENT_URI kotlin.comparisons  Badge kotlin.comparisons  BadgeException kotlin.comparisons  
BroadcastTool kotlin.comparisons  Build kotlin.comparisons  Bundle kotlin.comparisons  
CLASS_NAME kotlin.comparisons  COLUMN_BADGES_COUNT kotlin.comparisons  COLUMN_CLASS kotlin.comparisons  COLUMN_PACKAGE kotlin.comparisons  CONTENT_URI kotlin.comparisons  COUNT kotlin.comparisons  Class kotlin.comparisons  Collections kotlin.comparisons  
ContentValues kotlin.comparisons  DefaultBadge kotlin.comparisons  EXTRA_BADGE_COMPONENT kotlin.comparisons  EXTRA_BADGE_COUNT kotlin.comparisons  EXTRA_COMPONENT kotlin.comparisons  EXTRA_COUNT kotlin.comparisons  	Exception kotlin.comparisons  HtcLauncherBadge kotlin.comparisons  HuaweiLauncherBadge kotlin.comparisons  
INTENT_ACTION kotlin.comparisons  INTENT_EXTRA_ACTIVITY_NAME kotlin.comparisons  INTENT_EXTRA_BADGE_COUNT kotlin.comparisons   INTENT_EXTRA_BADGE_UPGRADE_COUNT kotlin.comparisons  INTENT_EXTRA_CLASS_NAME kotlin.comparisons  INTENT_EXTRA_MESSAGE kotlin.comparisons  INTENT_EXTRA_PACKAGE_NAME kotlin.comparisons  INTENT_EXTRA_SHOW_MESSAGE kotlin.comparisons  INTENT_SET_NOTIFICATION kotlin.comparisons  INTENT_UPDATE_COUNTER kotlin.comparisons  INTENT_UPDATE_SHORTCUT kotlin.comparisons  Int kotlin.comparisons  Intent kotlin.comparisons  LGLauncherBadge kotlin.comparisons  LauncherTool kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  METHOD_TO_CALL kotlin.comparisons  
MethodChannel kotlin.comparisons  	MiUIBadge kotlin.comparisons  NowaLauncherBadge kotlin.comparisons  OPPOLauncherBadge kotlin.comparisons  PACKAGE_NAME kotlin.comparisons  PROVIDER_COLUMNS_ACTIVITY_NAME kotlin.comparisons  PROVIDER_COLUMNS_BADGE_COUNT kotlin.comparisons  PROVIDER_COLUMNS_PACKAGE_NAME kotlin.comparisons  PROVIDER_CONTENT_URI kotlin.comparisons  PackageManager kotlin.comparisons  SONY_HOME_PROVIDER_NAME kotlin.comparisons  SamsungLauncherBadge kotlin.comparisons  SonyLauncherBadge kotlin.comparisons  Throws kotlin.comparisons  Uri kotlin.comparisons  VivoLauncherBadge kotlin.comparisons  Volatile kotlin.comparisons  YandexLauncherBadge kotlin.comparisons  ZTELauncherBadge kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  get kotlin.comparisons  indices kotlin.comparisons  java kotlin.comparisons  	javaClass kotlin.comparisons  javaPrimitiveType kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  
mutableListOf kotlin.comparisons  plus kotlin.comparisons  synchronized kotlin.comparisons  until kotlin.comparisons  Any 	kotlin.io  ApexLauncherBadge 	kotlin.io  AsusLauncherBadge 	kotlin.io  BADGE_CONTENT_URI 	kotlin.io  Badge 	kotlin.io  BadgeException 	kotlin.io  
BroadcastTool 	kotlin.io  Build 	kotlin.io  Bundle 	kotlin.io  
CLASS_NAME 	kotlin.io  COLUMN_BADGES_COUNT 	kotlin.io  COLUMN_CLASS 	kotlin.io  COLUMN_PACKAGE 	kotlin.io  CONTENT_URI 	kotlin.io  COUNT 	kotlin.io  Class 	kotlin.io  Collections 	kotlin.io  
ContentValues 	kotlin.io  DefaultBadge 	kotlin.io  EXTRA_BADGE_COMPONENT 	kotlin.io  EXTRA_BADGE_COUNT 	kotlin.io  EXTRA_COMPONENT 	kotlin.io  EXTRA_COUNT 	kotlin.io  	Exception 	kotlin.io  HtcLauncherBadge 	kotlin.io  HuaweiLauncherBadge 	kotlin.io  
INTENT_ACTION 	kotlin.io  INTENT_EXTRA_ACTIVITY_NAME 	kotlin.io  INTENT_EXTRA_BADGE_COUNT 	kotlin.io   INTENT_EXTRA_BADGE_UPGRADE_COUNT 	kotlin.io  INTENT_EXTRA_CLASS_NAME 	kotlin.io  INTENT_EXTRA_MESSAGE 	kotlin.io  INTENT_EXTRA_PACKAGE_NAME 	kotlin.io  INTENT_EXTRA_SHOW_MESSAGE 	kotlin.io  INTENT_SET_NOTIFICATION 	kotlin.io  INTENT_UPDATE_COUNTER 	kotlin.io  INTENT_UPDATE_SHORTCUT 	kotlin.io  Int 	kotlin.io  Intent 	kotlin.io  LGLauncherBadge 	kotlin.io  LauncherTool 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  METHOD_TO_CALL 	kotlin.io  
MethodChannel 	kotlin.io  	MiUIBadge 	kotlin.io  NowaLauncherBadge 	kotlin.io  OPPOLauncherBadge 	kotlin.io  PACKAGE_NAME 	kotlin.io  PROVIDER_COLUMNS_ACTIVITY_NAME 	kotlin.io  PROVIDER_COLUMNS_BADGE_COUNT 	kotlin.io  PROVIDER_COLUMNS_PACKAGE_NAME 	kotlin.io  PROVIDER_CONTENT_URI 	kotlin.io  PackageManager 	kotlin.io  SONY_HOME_PROVIDER_NAME 	kotlin.io  SamsungLauncherBadge 	kotlin.io  SonyLauncherBadge 	kotlin.io  Throws 	kotlin.io  Uri 	kotlin.io  VivoLauncherBadge 	kotlin.io  Volatile 	kotlin.io  YandexLauncherBadge 	kotlin.io  ZTELauncherBadge 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  get 	kotlin.io  indices 	kotlin.io  java 	kotlin.io  	javaClass 	kotlin.io  javaPrimitiveType 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  
mutableListOf 	kotlin.io  plus 	kotlin.io  synchronized 	kotlin.io  until 	kotlin.io  Any 
kotlin.jvm  ApexLauncherBadge 
kotlin.jvm  AsusLauncherBadge 
kotlin.jvm  BADGE_CONTENT_URI 
kotlin.jvm  Badge 
kotlin.jvm  BadgeException 
kotlin.jvm  
BroadcastTool 
kotlin.jvm  Build 
kotlin.jvm  Bundle 
kotlin.jvm  
CLASS_NAME 
kotlin.jvm  COLUMN_BADGES_COUNT 
kotlin.jvm  COLUMN_CLASS 
kotlin.jvm  COLUMN_PACKAGE 
kotlin.jvm  CONTENT_URI 
kotlin.jvm  COUNT 
kotlin.jvm  Class 
kotlin.jvm  Collections 
kotlin.jvm  
ContentValues 
kotlin.jvm  DefaultBadge 
kotlin.jvm  EXTRA_BADGE_COMPONENT 
kotlin.jvm  EXTRA_BADGE_COUNT 
kotlin.jvm  EXTRA_COMPONENT 
kotlin.jvm  EXTRA_COUNT 
kotlin.jvm  	Exception 
kotlin.jvm  HtcLauncherBadge 
kotlin.jvm  HuaweiLauncherBadge 
kotlin.jvm  
INTENT_ACTION 
kotlin.jvm  INTENT_EXTRA_ACTIVITY_NAME 
kotlin.jvm  INTENT_EXTRA_BADGE_COUNT 
kotlin.jvm   INTENT_EXTRA_BADGE_UPGRADE_COUNT 
kotlin.jvm  INTENT_EXTRA_CLASS_NAME 
kotlin.jvm  INTENT_EXTRA_MESSAGE 
kotlin.jvm  INTENT_EXTRA_PACKAGE_NAME 
kotlin.jvm  INTENT_EXTRA_SHOW_MESSAGE 
kotlin.jvm  INTENT_SET_NOTIFICATION 
kotlin.jvm  INTENT_UPDATE_COUNTER 
kotlin.jvm  INTENT_UPDATE_SHORTCUT 
kotlin.jvm  Int 
kotlin.jvm  Intent 
kotlin.jvm  LGLauncherBadge 
kotlin.jvm  LauncherTool 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  METHOD_TO_CALL 
kotlin.jvm  
MethodChannel 
kotlin.jvm  	MiUIBadge 
kotlin.jvm  NowaLauncherBadge 
kotlin.jvm  OPPOLauncherBadge 
kotlin.jvm  PACKAGE_NAME 
kotlin.jvm  PROVIDER_COLUMNS_ACTIVITY_NAME 
kotlin.jvm  PROVIDER_COLUMNS_BADGE_COUNT 
kotlin.jvm  PROVIDER_COLUMNS_PACKAGE_NAME 
kotlin.jvm  PROVIDER_CONTENT_URI 
kotlin.jvm  PackageManager 
kotlin.jvm  SONY_HOME_PROVIDER_NAME 
kotlin.jvm  SamsungLauncherBadge 
kotlin.jvm  SonyLauncherBadge 
kotlin.jvm  Throws 
kotlin.jvm  Uri 
kotlin.jvm  VivoLauncherBadge 
kotlin.jvm  Volatile 
kotlin.jvm  YandexLauncherBadge 
kotlin.jvm  ZTELauncherBadge 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  get 
kotlin.jvm  indices 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  javaPrimitiveType 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  plus 
kotlin.jvm  synchronized 
kotlin.jvm  until 
kotlin.jvm  Any 
kotlin.ranges  ApexLauncherBadge 
kotlin.ranges  AsusLauncherBadge 
kotlin.ranges  BADGE_CONTENT_URI 
kotlin.ranges  Badge 
kotlin.ranges  BadgeException 
kotlin.ranges  
BroadcastTool 
kotlin.ranges  Build 
kotlin.ranges  Bundle 
kotlin.ranges  
CLASS_NAME 
kotlin.ranges  COLUMN_BADGES_COUNT 
kotlin.ranges  COLUMN_CLASS 
kotlin.ranges  COLUMN_PACKAGE 
kotlin.ranges  CONTENT_URI 
kotlin.ranges  COUNT 
kotlin.ranges  Class 
kotlin.ranges  Collections 
kotlin.ranges  
ContentValues 
kotlin.ranges  DefaultBadge 
kotlin.ranges  EXTRA_BADGE_COMPONENT 
kotlin.ranges  EXTRA_BADGE_COUNT 
kotlin.ranges  EXTRA_COMPONENT 
kotlin.ranges  EXTRA_COUNT 
kotlin.ranges  	Exception 
kotlin.ranges  HtcLauncherBadge 
kotlin.ranges  HuaweiLauncherBadge 
kotlin.ranges  
INTENT_ACTION 
kotlin.ranges  INTENT_EXTRA_ACTIVITY_NAME 
kotlin.ranges  INTENT_EXTRA_BADGE_COUNT 
kotlin.ranges   INTENT_EXTRA_BADGE_UPGRADE_COUNT 
kotlin.ranges  INTENT_EXTRA_CLASS_NAME 
kotlin.ranges  INTENT_EXTRA_MESSAGE 
kotlin.ranges  INTENT_EXTRA_PACKAGE_NAME 
kotlin.ranges  INTENT_EXTRA_SHOW_MESSAGE 
kotlin.ranges  INTENT_SET_NOTIFICATION 
kotlin.ranges  INTENT_UPDATE_COUNTER 
kotlin.ranges  INTENT_UPDATE_SHORTCUT 
kotlin.ranges  Int 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  LGLauncherBadge 
kotlin.ranges  LauncherTool 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  METHOD_TO_CALL 
kotlin.ranges  
MethodChannel 
kotlin.ranges  	MiUIBadge 
kotlin.ranges  NowaLauncherBadge 
kotlin.ranges  OPPOLauncherBadge 
kotlin.ranges  PACKAGE_NAME 
kotlin.ranges  PROVIDER_COLUMNS_ACTIVITY_NAME 
kotlin.ranges  PROVIDER_COLUMNS_BADGE_COUNT 
kotlin.ranges  PROVIDER_COLUMNS_PACKAGE_NAME 
kotlin.ranges  PROVIDER_CONTENT_URI 
kotlin.ranges  PackageManager 
kotlin.ranges  SONY_HOME_PROVIDER_NAME 
kotlin.ranges  SamsungLauncherBadge 
kotlin.ranges  SonyLauncherBadge 
kotlin.ranges  Throws 
kotlin.ranges  Uri 
kotlin.ranges  VivoLauncherBadge 
kotlin.ranges  Volatile 
kotlin.ranges  YandexLauncherBadge 
kotlin.ranges  ZTELauncherBadge 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  get 
kotlin.ranges  indices 
kotlin.ranges  java 
kotlin.ranges  	javaClass 
kotlin.ranges  javaPrimitiveType 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  plus 
kotlin.ranges  synchronized 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJAVAPrimitiveType kotlin.reflect.KClass  getJava kotlin.reflect.KClass  getJavaPrimitiveType kotlin.reflect.KClass  java kotlin.reflect.KClass  javaPrimitiveType kotlin.reflect.KClass  Any kotlin.sequences  ApexLauncherBadge kotlin.sequences  AsusLauncherBadge kotlin.sequences  BADGE_CONTENT_URI kotlin.sequences  Badge kotlin.sequences  BadgeException kotlin.sequences  
BroadcastTool kotlin.sequences  Build kotlin.sequences  Bundle kotlin.sequences  
CLASS_NAME kotlin.sequences  COLUMN_BADGES_COUNT kotlin.sequences  COLUMN_CLASS kotlin.sequences  COLUMN_PACKAGE kotlin.sequences  CONTENT_URI kotlin.sequences  COUNT kotlin.sequences  Class kotlin.sequences  Collections kotlin.sequences  
ContentValues kotlin.sequences  DefaultBadge kotlin.sequences  EXTRA_BADGE_COMPONENT kotlin.sequences  EXTRA_BADGE_COUNT kotlin.sequences  EXTRA_COMPONENT kotlin.sequences  EXTRA_COUNT kotlin.sequences  	Exception kotlin.sequences  HtcLauncherBadge kotlin.sequences  HuaweiLauncherBadge kotlin.sequences  
INTENT_ACTION kotlin.sequences  INTENT_EXTRA_ACTIVITY_NAME kotlin.sequences  INTENT_EXTRA_BADGE_COUNT kotlin.sequences   INTENT_EXTRA_BADGE_UPGRADE_COUNT kotlin.sequences  INTENT_EXTRA_CLASS_NAME kotlin.sequences  INTENT_EXTRA_MESSAGE kotlin.sequences  INTENT_EXTRA_PACKAGE_NAME kotlin.sequences  INTENT_EXTRA_SHOW_MESSAGE kotlin.sequences  INTENT_SET_NOTIFICATION kotlin.sequences  INTENT_UPDATE_COUNTER kotlin.sequences  INTENT_UPDATE_SHORTCUT kotlin.sequences  Int kotlin.sequences  Intent kotlin.sequences  LGLauncherBadge kotlin.sequences  LauncherTool kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  METHOD_TO_CALL kotlin.sequences  
MethodChannel kotlin.sequences  	MiUIBadge kotlin.sequences  NowaLauncherBadge kotlin.sequences  OPPOLauncherBadge kotlin.sequences  PACKAGE_NAME kotlin.sequences  PROVIDER_COLUMNS_ACTIVITY_NAME kotlin.sequences  PROVIDER_COLUMNS_BADGE_COUNT kotlin.sequences  PROVIDER_COLUMNS_PACKAGE_NAME kotlin.sequences  PROVIDER_CONTENT_URI kotlin.sequences  PackageManager kotlin.sequences  SONY_HOME_PROVIDER_NAME kotlin.sequences  SamsungLauncherBadge kotlin.sequences  SonyLauncherBadge kotlin.sequences  Throws kotlin.sequences  Uri kotlin.sequences  VivoLauncherBadge kotlin.sequences  Volatile kotlin.sequences  YandexLauncherBadge kotlin.sequences  ZTELauncherBadge kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  get kotlin.sequences  indices kotlin.sequences  java kotlin.sequences  	javaClass kotlin.sequences  javaPrimitiveType kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  
mutableListOf kotlin.sequences  plus kotlin.sequences  synchronized kotlin.sequences  until kotlin.sequences  Any kotlin.text  ApexLauncherBadge kotlin.text  AsusLauncherBadge kotlin.text  BADGE_CONTENT_URI kotlin.text  Badge kotlin.text  BadgeException kotlin.text  
BroadcastTool kotlin.text  Build kotlin.text  Bundle kotlin.text  
CLASS_NAME kotlin.text  COLUMN_BADGES_COUNT kotlin.text  COLUMN_CLASS kotlin.text  COLUMN_PACKAGE kotlin.text  CONTENT_URI kotlin.text  COUNT kotlin.text  Class kotlin.text  Collections kotlin.text  
ContentValues kotlin.text  DefaultBadge kotlin.text  EXTRA_BADGE_COMPONENT kotlin.text  EXTRA_BADGE_COUNT kotlin.text  EXTRA_COMPONENT kotlin.text  EXTRA_COUNT kotlin.text  	Exception kotlin.text  HtcLauncherBadge kotlin.text  HuaweiLauncherBadge kotlin.text  
INTENT_ACTION kotlin.text  INTENT_EXTRA_ACTIVITY_NAME kotlin.text  INTENT_EXTRA_BADGE_COUNT kotlin.text   INTENT_EXTRA_BADGE_UPGRADE_COUNT kotlin.text  INTENT_EXTRA_CLASS_NAME kotlin.text  INTENT_EXTRA_MESSAGE kotlin.text  INTENT_EXTRA_PACKAGE_NAME kotlin.text  INTENT_EXTRA_SHOW_MESSAGE kotlin.text  INTENT_SET_NOTIFICATION kotlin.text  INTENT_UPDATE_COUNTER kotlin.text  INTENT_UPDATE_SHORTCUT kotlin.text  Int kotlin.text  Intent kotlin.text  LGLauncherBadge kotlin.text  LauncherTool kotlin.text  Log kotlin.text  Looper kotlin.text  METHOD_TO_CALL kotlin.text  
MethodChannel kotlin.text  	MiUIBadge kotlin.text  NowaLauncherBadge kotlin.text  OPPOLauncherBadge kotlin.text  PACKAGE_NAME kotlin.text  PROVIDER_COLUMNS_ACTIVITY_NAME kotlin.text  PROVIDER_COLUMNS_BADGE_COUNT kotlin.text  PROVIDER_COLUMNS_PACKAGE_NAME kotlin.text  PROVIDER_CONTENT_URI kotlin.text  PackageManager kotlin.text  SONY_HOME_PROVIDER_NAME kotlin.text  SamsungLauncherBadge kotlin.text  SonyLauncherBadge kotlin.text  Throws kotlin.text  Uri kotlin.text  VivoLauncherBadge kotlin.text  Volatile kotlin.text  YandexLauncherBadge kotlin.text  ZTELauncherBadge kotlin.text  contains kotlin.text  	emptyList kotlin.text  get kotlin.text  indices kotlin.text  java kotlin.text  	javaClass kotlin.text  javaPrimitiveType kotlin.text  let kotlin.text  listOf kotlin.text  
mutableListOf kotlin.text  plus kotlin.text  synchronized kotlin.text  until kotlin.text  AppBadgePlusPlugin me.liolin.app_badge_plus  Badge me.liolin.app_badge_plus  Int me.liolin.app_badge_plus  Map me.liolin.app_badge_plus  
MethodChannel me.liolin.app_badge_plus  get me.liolin.app_badge_plus  let me.liolin.app_badge_plus  Badge +me.liolin.app_badge_plus.AppBadgePlusPlugin  Context +me.liolin.app_badge_plus.AppBadgePlusPlugin  
FlutterPlugin +me.liolin.app_badge_plus.AppBadgePlusPlugin  Int +me.liolin.app_badge_plus.AppBadgePlusPlugin  Map +me.liolin.app_badge_plus.AppBadgePlusPlugin  
MethodCall +me.liolin.app_badge_plus.AppBadgePlusPlugin  
MethodChannel +me.liolin.app_badge_plus.AppBadgePlusPlugin  Result +me.liolin.app_badge_plus.AppBadgePlusPlugin  channel +me.liolin.app_badge_plus.AppBadgePlusPlugin  context +me.liolin.app_badge_plus.AppBadgePlusPlugin  get +me.liolin.app_badge_plus.AppBadgePlusPlugin  getGET +me.liolin.app_badge_plus.AppBadgePlusPlugin  getGet +me.liolin.app_badge_plus.AppBadgePlusPlugin  getLET +me.liolin.app_badge_plus.AppBadgePlusPlugin  getLet +me.liolin.app_badge_plus.AppBadgePlusPlugin  let +me.liolin.app_badge_plus.AppBadgePlusPlugin  Any me.liolin.app_badge_plus.badge  ApexLauncherBadge me.liolin.app_badge_plus.badge  AsusLauncherBadge me.liolin.app_badge_plus.badge  Badge me.liolin.app_badge_plus.badge  BadgeException me.liolin.app_badge_plus.badge  Boolean me.liolin.app_badge_plus.badge  Class me.liolin.app_badge_plus.badge  DefaultBadge me.liolin.app_badge_plus.badge  	Exception me.liolin.app_badge_plus.badge  HtcLauncherBadge me.liolin.app_badge_plus.badge  HuaweiLauncherBadge me.liolin.app_badge_plus.badge  IBadge me.liolin.app_badge_plus.badge  Int me.liolin.app_badge_plus.badge  LGLauncherBadge me.liolin.app_badge_plus.badge  LauncherTool me.liolin.app_badge_plus.badge  List me.liolin.app_badge_plus.badge  Log me.liolin.app_badge_plus.badge  	MiUIBadge me.liolin.app_badge_plus.badge  MutableList me.liolin.app_badge_plus.badge  NowaLauncherBadge me.liolin.app_badge_plus.badge  OPPOLauncherBadge me.liolin.app_badge_plus.badge  SamsungLauncherBadge me.liolin.app_badge_plus.badge  SonyLauncherBadge me.liolin.app_badge_plus.badge  String me.liolin.app_badge_plus.badge  	Throwable me.liolin.app_badge_plus.badge  Throws me.liolin.app_badge_plus.badge  VivoLauncherBadge me.liolin.app_badge_plus.badge  Volatile me.liolin.app_badge_plus.badge  YandexLauncherBadge me.liolin.app_badge_plus.badge  ZTELauncherBadge me.liolin.app_badge_plus.badge  contains me.liolin.app_badge_plus.badge  java me.liolin.app_badge_plus.badge  
mutableListOf me.liolin.app_badge_plus.badge  synchronized me.liolin.app_badge_plus.badge  until me.liolin.app_badge_plus.badge  Any $me.liolin.app_badge_plus.badge.Badge  ApexLauncherBadge $me.liolin.app_badge_plus.badge.Badge  AsusLauncherBadge $me.liolin.app_badge_plus.badge.Badge  BADGES $me.liolin.app_badge_plus.badge.Badge  BadgeException $me.liolin.app_badge_plus.badge.Badge  Boolean $me.liolin.app_badge_plus.badge.Badge  Class $me.liolin.app_badge_plus.badge.Badge  Context $me.liolin.app_badge_plus.badge.Badge  DefaultBadge $me.liolin.app_badge_plus.badge.Badge  	Exception $me.liolin.app_badge_plus.badge.Badge  HtcLauncherBadge $me.liolin.app_badge_plus.badge.Badge  HuaweiLauncherBadge $me.liolin.app_badge_plus.badge.Badge  IBadge $me.liolin.app_badge_plus.badge.Badge  Int $me.liolin.app_badge_plus.badge.Badge  Intent $me.liolin.app_badge_plus.badge.Badge  LGLauncherBadge $me.liolin.app_badge_plus.badge.Badge  LauncherTool $me.liolin.app_badge_plus.badge.Badge  List $me.liolin.app_badge_plus.badge.Badge  Log $me.liolin.app_badge_plus.badge.Badge  	MiUIBadge $me.liolin.app_badge_plus.badge.Badge  MutableList $me.liolin.app_badge_plus.badge.Badge  Notification $me.liolin.app_badge_plus.badge.Badge  NowaLauncherBadge $me.liolin.app_badge_plus.badge.Badge  OPPOLauncherBadge $me.liolin.app_badge_plus.badge.Badge  ResolveInfo $me.liolin.app_badge_plus.badge.Badge  SamsungLauncherBadge $me.liolin.app_badge_plus.badge.Badge  SonyLauncherBadge $me.liolin.app_badge_plus.badge.Badge  TAG $me.liolin.app_badge_plus.badge.Badge  Throws $me.liolin.app_badge_plus.badge.Badge  VivoLauncherBadge $me.liolin.app_badge_plus.badge.Badge  Volatile $me.liolin.app_badge_plus.badge.Badge  YandexLauncherBadge $me.liolin.app_badge_plus.badge.Badge  ZTELauncherBadge $me.liolin.app_badge_plus.badge.Badge  badgeSupportedLock $me.liolin.app_badge_plus.badge.Badge  contains $me.liolin.app_badge_plus.badge.Badge  getCONTAINS $me.liolin.app_badge_plus.badge.Badge  getContains $me.liolin.app_badge_plus.badge.Badge  getMUTABLEListOf $me.liolin.app_badge_plus.badge.Badge  getMutableListOf $me.liolin.app_badge_plus.badge.Badge  getSYNCHRONIZED $me.liolin.app_badge_plus.badge.Badge  getSynchronized $me.liolin.app_badge_plus.badge.Badge  getUNTIL $me.liolin.app_badge_plus.badge.Badge  getUntil $me.liolin.app_badge_plus.badge.Badge  iBadge $me.liolin.app_badge_plus.badge.Badge  	initBadge $me.liolin.app_badge_plus.badge.Badge  isBadgeSupported $me.liolin.app_badge_plus.badge.Badge  java $me.liolin.app_badge_plus.badge.Badge  
mutableListOf $me.liolin.app_badge_plus.badge.Badge  notification $me.liolin.app_badge_plus.badge.Badge  synchronized $me.liolin.app_badge_plus.badge.Badge  until $me.liolin.app_badge_plus.badge.Badge  updateBadge $me.liolin.app_badge_plus.badge.Badge  updateBadgeOrThrow $me.liolin.app_badge_plus.badge.Badge  String -me.liolin.app_badge_plus.badge.BadgeException  	Throwable -me.liolin.app_badge_plus.badge.BadgeException  Context %me.liolin.app_badge_plus.badge.IBadge  Int %me.liolin.app_badge_plus.badge.IBadge  List %me.liolin.app_badge_plus.badge.IBadge  String %me.liolin.app_badge_plus.badge.IBadge  equals %me.liolin.app_badge_plus.badge.IBadge  getSupportLaunchers %me.liolin.app_badge_plus.badge.IBadge  updateBadge %me.liolin.app_badge_plus.badge.IBadge  ApexLauncherBadge me.liolin.app_badge_plus.impl  AsusLauncherBadge me.liolin.app_badge_plus.impl  BADGE_CONTENT_URI me.liolin.app_badge_plus.impl  Badge me.liolin.app_badge_plus.impl  Boolean me.liolin.app_badge_plus.impl  
BroadcastTool me.liolin.app_badge_plus.impl  Bundle me.liolin.app_badge_plus.impl  
CLASS_NAME me.liolin.app_badge_plus.impl  COLUMN_BADGES_COUNT me.liolin.app_badge_plus.impl  COLUMN_CLASS me.liolin.app_badge_plus.impl  COLUMN_PACKAGE me.liolin.app_badge_plus.impl  CONTENT_URI me.liolin.app_badge_plus.impl  COUNT me.liolin.app_badge_plus.impl  
ContentValues me.liolin.app_badge_plus.impl  DefaultBadge me.liolin.app_badge_plus.impl  EXTRA_BADGE_COMPONENT me.liolin.app_badge_plus.impl  EXTRA_BADGE_COUNT me.liolin.app_badge_plus.impl  EXTRA_COMPONENT me.liolin.app_badge_plus.impl  EXTRA_COUNT me.liolin.app_badge_plus.impl  	Exception me.liolin.app_badge_plus.impl  HtcLauncherBadge me.liolin.app_badge_plus.impl  HuaweiLauncherBadge me.liolin.app_badge_plus.impl  
INTENT_ACTION me.liolin.app_badge_plus.impl  INTENT_EXTRA_ACTIVITY_NAME me.liolin.app_badge_plus.impl  INTENT_EXTRA_BADGE_COUNT me.liolin.app_badge_plus.impl   INTENT_EXTRA_BADGE_UPGRADE_COUNT me.liolin.app_badge_plus.impl  INTENT_EXTRA_CLASS_NAME me.liolin.app_badge_plus.impl  INTENT_EXTRA_MESSAGE me.liolin.app_badge_plus.impl  INTENT_EXTRA_PACKAGE_NAME me.liolin.app_badge_plus.impl  INTENT_EXTRA_SHOW_MESSAGE me.liolin.app_badge_plus.impl  INTENT_SET_NOTIFICATION me.liolin.app_badge_plus.impl  INTENT_UPDATE_COUNTER me.liolin.app_badge_plus.impl  INTENT_UPDATE_SHORTCUT me.liolin.app_badge_plus.impl  Int me.liolin.app_badge_plus.impl  Intent me.liolin.app_badge_plus.impl  LGLauncherBadge me.liolin.app_badge_plus.impl  LauncherTool me.liolin.app_badge_plus.impl  List me.liolin.app_badge_plus.impl  Log me.liolin.app_badge_plus.impl  Looper me.liolin.app_badge_plus.impl  METHOD_TO_CALL me.liolin.app_badge_plus.impl  	MiUIBadge me.liolin.app_badge_plus.impl  NowaLauncherBadge me.liolin.app_badge_plus.impl  OPPOLauncherBadge me.liolin.app_badge_plus.impl  PACKAGE_NAME me.liolin.app_badge_plus.impl  PROVIDER_COLUMNS_ACTIVITY_NAME me.liolin.app_badge_plus.impl  PROVIDER_COLUMNS_BADGE_COUNT me.liolin.app_badge_plus.impl  PROVIDER_COLUMNS_PACKAGE_NAME me.liolin.app_badge_plus.impl  PROVIDER_CONTENT_URI me.liolin.app_badge_plus.impl  SONY_HOME_PROVIDER_NAME me.liolin.app_badge_plus.impl  SamsungLauncherBadge me.liolin.app_badge_plus.impl  SonyLauncherBadge me.liolin.app_badge_plus.impl  String me.liolin.app_badge_plus.impl  	Throwable me.liolin.app_badge_plus.impl  Throws me.liolin.app_badge_plus.impl  Uri me.liolin.app_badge_plus.impl  VivoLauncherBadge me.liolin.app_badge_plus.impl  YandexLauncherBadge me.liolin.app_badge_plus.impl  ZTELauncherBadge me.liolin.app_badge_plus.impl  	emptyList me.liolin.app_badge_plus.impl  java me.liolin.app_badge_plus.impl  	javaClass me.liolin.app_badge_plus.impl  javaPrimitiveType me.liolin.app_badge_plus.impl  listOf me.liolin.app_badge_plus.impl  plus me.liolin.app_badge_plus.impl  
BroadcastTool /me.liolin.app_badge_plus.impl.ApexLauncherBadge  
CLASS_NAME /me.liolin.app_badge_plus.impl.ApexLauncherBadge  COUNT /me.liolin.app_badge_plus.impl.ApexLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.ApexLauncherBadge  Context /me.liolin.app_badge_plus.impl.ApexLauncherBadge  INTENT_UPDATE_COUNTER /me.liolin.app_badge_plus.impl.ApexLauncherBadge  Int /me.liolin.app_badge_plus.impl.ApexLauncherBadge  Intent /me.liolin.app_badge_plus.impl.ApexLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.ApexLauncherBadge  List /me.liolin.app_badge_plus.impl.ApexLauncherBadge  PACKAGE_NAME /me.liolin.app_badge_plus.impl.ApexLauncherBadge  String /me.liolin.app_badge_plus.impl.ApexLauncherBadge  	getLISTOf /me.liolin.app_badge_plus.impl.ApexLauncherBadge  	getListOf /me.liolin.app_badge_plus.impl.ApexLauncherBadge  listOf /me.liolin.app_badge_plus.impl.ApexLauncherBadge  
BroadcastTool 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  
CLASS_NAME 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  COUNT 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  Context 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  INTENT_UPDATE_COUNTER 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  Int 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  List 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  PACKAGE_NAME 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  String 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  	getLISTOf 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  	getListOf 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.ApexLauncherBadge.Companion  	Companion /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Context /me.liolin.app_badge_plus.impl.AsusLauncherBadge  
INTENT_ACTION /me.liolin.app_badge_plus.impl.AsusLauncherBadge  INTENT_EXTRA_ACTIVITY_NAME /me.liolin.app_badge_plus.impl.AsusLauncherBadge  INTENT_EXTRA_BADGE_COUNT /me.liolin.app_badge_plus.impl.AsusLauncherBadge  INTENT_EXTRA_PACKAGE_NAME /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Int /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Intent /me.liolin.app_badge_plus.impl.AsusLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.AsusLauncherBadge  List /me.liolin.app_badge_plus.impl.AsusLauncherBadge  String /me.liolin.app_badge_plus.impl.AsusLauncherBadge  	getLISTOf /me.liolin.app_badge_plus.impl.AsusLauncherBadge  	getListOf /me.liolin.app_badge_plus.impl.AsusLauncherBadge  listOf /me.liolin.app_badge_plus.impl.AsusLauncherBadge  Context 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  
INTENT_ACTION 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  INTENT_EXTRA_ACTIVITY_NAME 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  Int 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  List 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  String 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  	getLISTOf 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  	getListOf 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.AsusLauncherBadge.Companion  Context *me.liolin.app_badge_plus.impl.DefaultBadge  Int *me.liolin.app_badge_plus.impl.DefaultBadge  List *me.liolin.app_badge_plus.impl.DefaultBadge  String *me.liolin.app_badge_plus.impl.DefaultBadge  	emptyList *me.liolin.app_badge_plus.impl.DefaultBadge  getEMPTYList *me.liolin.app_badge_plus.impl.DefaultBadge  getEmptyList *me.liolin.app_badge_plus.impl.DefaultBadge  
BroadcastTool .me.liolin.app_badge_plus.impl.HtcLauncherBadge  COUNT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  	Companion .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Context .me.liolin.app_badge_plus.impl.HtcLauncherBadge  EXTRA_COMPONENT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  EXTRA_COUNT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  	Exception .me.liolin.app_badge_plus.impl.HtcLauncherBadge  INTENT_SET_NOTIFICATION .me.liolin.app_badge_plus.impl.HtcLauncherBadge  INTENT_UPDATE_SHORTCUT .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Int .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Intent .me.liolin.app_badge_plus.impl.HtcLauncherBadge  LauncherTool .me.liolin.app_badge_plus.impl.HtcLauncherBadge  List .me.liolin.app_badge_plus.impl.HtcLauncherBadge  Log .me.liolin.app_badge_plus.impl.HtcLauncherBadge  PACKAGE_NAME .me.liolin.app_badge_plus.impl.HtcLauncherBadge  String .me.liolin.app_badge_plus.impl.HtcLauncherBadge  	getLISTOf .me.liolin.app_badge_plus.impl.HtcLauncherBadge  	getListOf .me.liolin.app_badge_plus.impl.HtcLauncherBadge  listOf .me.liolin.app_badge_plus.impl.HtcLauncherBadge  
BroadcastTool 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  COUNT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Context 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  EXTRA_COMPONENT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  EXTRA_COUNT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  	Exception 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  INTENT_SET_NOTIFICATION 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  INTENT_UPDATE_SHORTCUT 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Int 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Intent 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  LauncherTool 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  List 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Log 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  PACKAGE_NAME 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  String 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  	getLISTOf 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  	getListOf 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  listOf 8me.liolin.app_badge_plus.impl.HtcLauncherBadge.Companion  Bundle 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  Context 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  Int 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  LauncherTool 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  List 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  String 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  Uri 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  	getLISTOf 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  	getListOf 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  listOf 1me.liolin.app_badge_plus.impl.HuaweiLauncherBadge  
BroadcastTool -me.liolin.app_badge_plus.impl.LGLauncherBadge  	Companion -me.liolin.app_badge_plus.impl.LGLauncherBadge  Context -me.liolin.app_badge_plus.impl.LGLauncherBadge  
INTENT_ACTION -me.liolin.app_badge_plus.impl.LGLauncherBadge  INTENT_EXTRA_BADGE_COUNT -me.liolin.app_badge_plus.impl.LGLauncherBadge  INTENT_EXTRA_CLASS_NAME -me.liolin.app_badge_plus.impl.LGLauncherBadge  INTENT_EXTRA_PACKAGE_NAME -me.liolin.app_badge_plus.impl.LGLauncherBadge  Int -me.liolin.app_badge_plus.impl.LGLauncherBadge  Intent -me.liolin.app_badge_plus.impl.LGLauncherBadge  LauncherTool -me.liolin.app_badge_plus.impl.LGLauncherBadge  List -me.liolin.app_badge_plus.impl.LGLauncherBadge  String -me.liolin.app_badge_plus.impl.LGLauncherBadge  	getLISTOf -me.liolin.app_badge_plus.impl.LGLauncherBadge  	getListOf -me.liolin.app_badge_plus.impl.LGLauncherBadge  listOf -me.liolin.app_badge_plus.impl.LGLauncherBadge  
BroadcastTool 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  Context 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  
INTENT_ACTION 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  INTENT_EXTRA_CLASS_NAME 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  Int 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  Intent 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  LauncherTool 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  List 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  String 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  	getLISTOf 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  	getListOf 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  listOf 7me.liolin.app_badge_plus.impl.LGLauncherBadge.Companion  Badge 'me.liolin.app_badge_plus.impl.MiUIBadge  Context 'me.liolin.app_badge_plus.impl.MiUIBadge  	Exception 'me.liolin.app_badge_plus.impl.MiUIBadge  Int 'me.liolin.app_badge_plus.impl.MiUIBadge  List 'me.liolin.app_badge_plus.impl.MiUIBadge  Log 'me.liolin.app_badge_plus.impl.MiUIBadge  Notification 'me.liolin.app_badge_plus.impl.MiUIBadge  String 'me.liolin.app_badge_plus.impl.MiUIBadge  SuppressLint 'me.liolin.app_badge_plus.impl.MiUIBadge  	getLISTOf 'me.liolin.app_badge_plus.impl.MiUIBadge  	getListOf 'me.liolin.app_badge_plus.impl.MiUIBadge  	javaClass 'me.liolin.app_badge_plus.impl.MiUIBadge  javaPrimitiveType 'me.liolin.app_badge_plus.impl.MiUIBadge  listOf 'me.liolin.app_badge_plus.impl.MiUIBadge  notificationBadge 'me.liolin.app_badge_plus.impl.MiUIBadge  CONTENT_URI /me.liolin.app_badge_plus.impl.NowaLauncherBadge  COUNT /me.liolin.app_badge_plus.impl.NowaLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.NowaLauncherBadge  
ContentValues /me.liolin.app_badge_plus.impl.NowaLauncherBadge  Context /me.liolin.app_badge_plus.impl.NowaLauncherBadge  Int /me.liolin.app_badge_plus.impl.NowaLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.NowaLauncherBadge  List /me.liolin.app_badge_plus.impl.NowaLauncherBadge  String /me.liolin.app_badge_plus.impl.NowaLauncherBadge  Uri /me.liolin.app_badge_plus.impl.NowaLauncherBadge  	getLISTOf /me.liolin.app_badge_plus.impl.NowaLauncherBadge  	getListOf /me.liolin.app_badge_plus.impl.NowaLauncherBadge  getPLUS /me.liolin.app_badge_plus.impl.NowaLauncherBadge  getPlus /me.liolin.app_badge_plus.impl.NowaLauncherBadge  listOf /me.liolin.app_badge_plus.impl.NowaLauncherBadge  plus /me.liolin.app_badge_plus.impl.NowaLauncherBadge  CONTENT_URI 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  COUNT 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  
ContentValues 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  Context 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  Int 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  List 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  String 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  Uri 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  	getLISTOf 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  	getListOf 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  getPLUS 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  getPlus 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  plus 9me.liolin.app_badge_plus.impl.NowaLauncherBadge.Companion  Bundle /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Context /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	Exception /me.liolin.app_badge_plus.impl.OPPOLauncherBadge   INTENT_EXTRA_BADGE_UPGRADE_COUNT /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Int /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  List /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Log /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  PROVIDER_CONTENT_URI /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  String /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	Throwable /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Throws /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Uri /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  executeBadgeByContentProvider /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	getLISTOf /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  	getListOf /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  listOf /me.liolin.app_badge_plus.impl.OPPOLauncherBadge  Bundle 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Context 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	Exception 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion   INTENT_EXTRA_BADGE_UPGRADE_COUNT 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Int 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  List 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Log 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  PROVIDER_CONTENT_URI 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  String 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	Throwable 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Throws 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  Uri 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	getLISTOf 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	getListOf 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.OPPOLauncherBadge.Companion  	Companion 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Context 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  
INTENT_ACTION 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  INTENT_EXTRA_BADGE_COUNT 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  INTENT_EXTRA_CLASS_NAME 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  INTENT_EXTRA_PACKAGE_NAME 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Int 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Intent 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  LauncherTool 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  List 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  String 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  	getLISTOf 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  	getListOf 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  listOf 2me.liolin.app_badge_plus.impl.SamsungLauncherBadge  Context <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  
INTENT_ACTION <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  INTENT_EXTRA_CLASS_NAME <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  Int <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  Intent <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  LauncherTool <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  List <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  String <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  	getLISTOf <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  	getListOf <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  listOf <me.liolin.app_badge_plus.impl.SamsungLauncherBadge.Companion  AsyncQueryHandler /me.liolin.app_badge_plus.impl.SonyLauncherBadge  BADGE_CONTENT_URI /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Boolean /me.liolin.app_badge_plus.impl.SonyLauncherBadge  	Companion /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
ComponentName /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
ContentValues /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Context /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
INTENT_ACTION /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_ACTIVITY_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_MESSAGE /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_PACKAGE_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  INTENT_EXTRA_SHOW_MESSAGE /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Int /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Intent /me.liolin.app_badge_plus.impl.SonyLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.SonyLauncherBadge  List /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Looper /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_COLUMNS_ACTIVITY_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_COLUMNS_BADGE_COUNT /me.liolin.app_badge_plus.impl.SonyLauncherBadge  PROVIDER_COLUMNS_PACKAGE_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  SONY_HOME_PROVIDER_NAME /me.liolin.app_badge_plus.impl.SonyLauncherBadge  String /me.liolin.app_badge_plus.impl.SonyLauncherBadge  SuppressLint /me.liolin.app_badge_plus.impl.SonyLauncherBadge  Uri /me.liolin.app_badge_plus.impl.SonyLauncherBadge  createContentValues /me.liolin.app_badge_plus.impl.SonyLauncherBadge  executeBadgeByBroadcast /me.liolin.app_badge_plus.impl.SonyLauncherBadge  executeBadgeByContentProvider /me.liolin.app_badge_plus.impl.SonyLauncherBadge  	getLISTOf /me.liolin.app_badge_plus.impl.SonyLauncherBadge  	getListOf /me.liolin.app_badge_plus.impl.SonyLauncherBadge  insertBadgeAsync /me.liolin.app_badge_plus.impl.SonyLauncherBadge  insertBadgeSync /me.liolin.app_badge_plus.impl.SonyLauncherBadge  listOf /me.liolin.app_badge_plus.impl.SonyLauncherBadge  
mQueryHandler /me.liolin.app_badge_plus.impl.SonyLauncherBadge  sonyBadgeContentProviderExists /me.liolin.app_badge_plus.impl.SonyLauncherBadge  AsyncQueryHandler 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  BADGE_CONTENT_URI 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Boolean 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  
ComponentName 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  
ContentValues 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Context 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  
INTENT_ACTION 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_ACTIVITY_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_MESSAGE 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  INTENT_EXTRA_SHOW_MESSAGE 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Int 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  List 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Looper 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_COLUMNS_ACTIVITY_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_COLUMNS_BADGE_COUNT 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_COLUMNS_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  PROVIDER_CONTENT_URI 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  SONY_HOME_PROVIDER_NAME 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  String 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  SuppressLint 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  Uri 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  	getLISTOf 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  	getListOf 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.SonyLauncherBadge.Companion  	Companion /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Context /me.liolin.app_badge_plus.impl.VivoLauncherBadge  
INTENT_ACTION /me.liolin.app_badge_plus.impl.VivoLauncherBadge  INTENT_EXTRA_BADGE_COUNT /me.liolin.app_badge_plus.impl.VivoLauncherBadge  INTENT_EXTRA_CLASS_NAME /me.liolin.app_badge_plus.impl.VivoLauncherBadge  INTENT_EXTRA_PACKAGE_NAME /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Int /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Intent /me.liolin.app_badge_plus.impl.VivoLauncherBadge  LauncherTool /me.liolin.app_badge_plus.impl.VivoLauncherBadge  List /me.liolin.app_badge_plus.impl.VivoLauncherBadge  String /me.liolin.app_badge_plus.impl.VivoLauncherBadge  	getLISTOf /me.liolin.app_badge_plus.impl.VivoLauncherBadge  	getListOf /me.liolin.app_badge_plus.impl.VivoLauncherBadge  listOf /me.liolin.app_badge_plus.impl.VivoLauncherBadge  Context 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  
INTENT_ACTION 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  INTENT_EXTRA_BADGE_COUNT 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  INTENT_EXTRA_CLASS_NAME 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  INTENT_EXTRA_PACKAGE_NAME 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  Int 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  Intent 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  LauncherTool 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  List 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  String 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  	getLISTOf 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  	getListOf 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  listOf 9me.liolin.app_badge_plus.impl.VivoLauncherBadge.Companion  Bundle 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  COLUMN_BADGES_COUNT 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  COLUMN_CLASS 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  COLUMN_PACKAGE 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  CONTENT_URI 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  	Companion 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Context 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Int 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  LauncherTool 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  List 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  METHOD_TO_CALL 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  String 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  Uri 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  getJAVA 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  getJava 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  	getLISTOf 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  	getListOf 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  java 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  listOf 1me.liolin.app_badge_plus.impl.YandexLauncherBadge  	AUTHORITY ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Bundle ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  COLUMN_BADGES_COUNT ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  COLUMN_CLASS ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  COLUMN_PACKAGE ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  CONTENT_URI ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Context ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Int ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  LauncherTool ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  List ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  METHOD_TO_CALL ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  String ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Uri ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  getJAVA ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  getJava ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  	getLISTOf ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  	getListOf ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  java ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  listOf ;me.liolin.app_badge_plus.impl.YandexLauncherBadge.Companion  Bundle .me.liolin.app_badge_plus.impl.ZTELauncherBadge  CONTENT_URI .me.liolin.app_badge_plus.impl.ZTELauncherBadge  	Companion .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Context .me.liolin.app_badge_plus.impl.ZTELauncherBadge  EXTRA_BADGE_COMPONENT .me.liolin.app_badge_plus.impl.ZTELauncherBadge  EXTRA_BADGE_COUNT .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Int .me.liolin.app_badge_plus.impl.ZTELauncherBadge  LauncherTool .me.liolin.app_badge_plus.impl.ZTELauncherBadge  List .me.liolin.app_badge_plus.impl.ZTELauncherBadge  String .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Uri .me.liolin.app_badge_plus.impl.ZTELauncherBadge  	emptyList .me.liolin.app_badge_plus.impl.ZTELauncherBadge  getEMPTYList .me.liolin.app_badge_plus.impl.ZTELauncherBadge  getEmptyList .me.liolin.app_badge_plus.impl.ZTELauncherBadge  Bundle 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  CONTENT_URI 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  Context 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  EXTRA_BADGE_COMPONENT 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  EXTRA_BADGE_COUNT 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  Int 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  LauncherTool 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  List 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  String 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  Uri 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  	emptyList 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  getEMPTYList 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  getEmptyList 8me.liolin.app_badge_plus.impl.ZTELauncherBadge.Companion  BadgeException me.liolin.app_badge_plus.util  
BroadcastTool me.liolin.app_badge_plus.util  Build me.liolin.app_badge_plus.util  Collections me.liolin.app_badge_plus.util  	Exception me.liolin.app_badge_plus.util  Intent me.liolin.app_badge_plus.util  LauncherTool me.liolin.app_badge_plus.util  List me.liolin.app_badge_plus.util  Log me.liolin.app_badge_plus.util  PackageManager me.liolin.app_badge_plus.util  String me.liolin.app_badge_plus.util  Throws me.liolin.app_badge_plus.util  indices me.liolin.app_badge_plus.util  BadgeException +me.liolin.app_badge_plus.util.BroadcastTool  Build +me.liolin.app_badge_plus.util.BroadcastTool  Context +me.liolin.app_badge_plus.util.BroadcastTool  	Exception +me.liolin.app_badge_plus.util.BroadcastTool  Intent +me.liolin.app_badge_plus.util.BroadcastTool  List +me.liolin.app_badge_plus.util.BroadcastTool  Log +me.liolin.app_badge_plus.util.BroadcastTool  ResolveInfo +me.liolin.app_badge_plus.util.BroadcastTool  TAG +me.liolin.app_badge_plus.util.BroadcastTool  Throws +me.liolin.app_badge_plus.util.BroadcastTool  resolveBroadcast +me.liolin.app_badge_plus.util.BroadcastTool  
sendBroadcast +me.liolin.app_badge_plus.util.BroadcastTool  sendDefaultBroadcast +me.liolin.app_badge_plus.util.BroadcastTool  sendIntentExplicitly +me.liolin.app_badge_plus.util.BroadcastTool  Collections *me.liolin.app_badge_plus.util.LauncherTool  
ComponentName *me.liolin.app_badge_plus.util.LauncherTool  Context *me.liolin.app_badge_plus.util.LauncherTool  Intent *me.liolin.app_badge_plus.util.LauncherTool  List *me.liolin.app_badge_plus.util.LauncherTool  Log *me.liolin.app_badge_plus.util.LauncherTool  PackageManager *me.liolin.app_badge_plus.util.LauncherTool  ResolveInfo *me.liolin.app_badge_plus.util.LauncherTool  String *me.liolin.app_badge_plus.util.LauncherTool  TAG *me.liolin.app_badge_plus.util.LauncherTool  getClassName *me.liolin.app_badge_plus.util.LauncherTool  getComponentName *me.liolin.app_badge_plus.util.LauncherTool  getLauncherList *me.liolin.app_badge_plus.util.LauncherTool  indices *me.liolin.app_badge_plus.util.LauncherTool  makeSureLauncherOnTop *me.liolin.app_badge_plus.util.LauncherTool                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                