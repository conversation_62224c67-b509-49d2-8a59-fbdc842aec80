[{"merged": "com.flutter.stripe.stripe_android-release-91:/layout-land/stripe_challenge_zone_view.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/layout-land/stripe_challenge_zone_view.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout-land/material_timepicker.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout-land/material_timepicker.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout-land/material_clock_period_toggle_land.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout-land/material_clock_period_toggle_land.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/layout-land/mtrl_picker_header_dialog.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/layout-land/mtrl_picker_header_dialog.xml"}]