{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4783,4888,5051,5179,5287,5455,5583,5705,5959,6147,6255,6425,6556,6715,6893,6961,7030", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "4883,5046,5174,5282,5450,5578,5700,5809,6142,6250,6420,6551,6710,6888,6956,7025,7112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,228,315,415,504,587,669,809,909,1022,1115,1248,1315,1432,1496,1670,1867,2014,2106,2221,2317,2416,2596,2707,2807,3067,3183,3325,3537,3755,3853,3979,4179,4282,4343,4409,4493,4596,4693,4766,4864,4948,5059,5283,5351,5420,5500,5620,5694,5782,5848,5931,6002,6070,6194,6281,6388,6485,6561,6643,6712,6804,6865,6975,7092,7197,7326,7389,7492,7603,7740,7811,7909,8015,8073,8131,8222,8330,8506,8787,9099,9196,9272,9358,9430,9546,9666,9753,9828,9901,9989,10082,10176,10320,10470,10538,10619,10673,10836,10906,10968,11047,11125,11199,11278,11422,11548,11662,11750,11826,11910,11981", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "127,223,310,410,499,582,664,804,904,1017,1110,1243,1310,1427,1491,1665,1862,2009,2101,2216,2312,2411,2591,2702,2802,3062,3178,3320,3532,3750,3848,3974,4174,4277,4338,4404,4488,4591,4688,4761,4859,4943,5054,5278,5346,5415,5495,5615,5689,5777,5843,5926,5997,6065,6189,6276,6383,6480,6556,6638,6707,6799,6860,6970,7087,7192,7321,7384,7487,7598,7735,7806,7904,8010,8068,8126,8217,8325,8501,8782,9094,9191,9267,9353,9425,9541,9661,9748,9823,9896,9984,10077,10171,10315,10465,10533,10614,10668,10831,10901,10963,11042,11120,11194,11273,11417,11543,11657,11745,11821,11905,11976,12148"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15432,15509,15605,21345,23220,23309,23392,25472,26704,26852,26965,29561,31391,31521,31761,32070,32244,32441,32748,32946,33162,33258,33357,33537,33648,33748,34008,34124,34266,34478,34696,34882,35008,35208,35311,35372,35438,35522,35625,35722,35795,35893,35977,36088,36312,36380,36449,36529,36649,36723,36811,36877,36960,37031,37099,37655,37742,37849,37946,38022,38104,38173,38265,38326,38436,38553,38658,38787,38850,38953,39064,39201,39463,39805,41434,41492,41550,41641,41749,41925,42206,42518,42615,43249,43335,43407,43523,43643,43730,43805,43878,43966,44059,44153,44297,44447,44515,44677,44731,44894,44964,45026,45105,45183,45257,45336,45480,45606,45720,45808,45884,45968,46039", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "15504,15600,15687,21440,23304,23387,23469,25607,26799,26960,27053,29689,31453,31633,31820,32239,32436,32583,32835,33056,33253,33352,33532,33643,33743,34003,34119,34261,34473,34691,34789,35003,35203,35306,35367,35433,35517,35620,35717,35790,35888,35972,36083,36307,36375,36444,36524,36644,36718,36806,36872,36955,37026,37094,37218,37737,37844,37941,38017,38099,38168,38260,38321,38431,38548,38653,38782,38845,38948,39059,39196,39267,39556,39906,41487,41545,41636,41744,41920,42201,42513,42610,42686,43330,43402,43518,43638,43725,43800,43873,43961,44054,44148,44292,44442,44510,44591,44726,44889,44959,45021,45100,45178,45252,45331,45475,45601,45715,45803,45879,45963,46034,46206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5814", "endColumns": "144", "endOffsets": "5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,214", "endColumns": "76,81,76", "endOffsets": "127,209,286"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4706,7429,7721", "endColumns": "76,81,76", "endOffsets": "4778,7506,7793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,204,272,358,429,490,563,630,692,760,830,890,951,1031,1095,1167,1230,1309,1374,1464,1544,1631,1724,1837,1928,1978,2026,2102,2164,2232,2301,2409,2498,2598", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "116,199,267,353,424,485,558,625,687,755,825,885,946,1026,1090,1162,1225,1304,1369,1459,1539,1626,1719,1832,1923,1973,2021,2097,2159,2227,2296,2404,2493,2593,2692"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15992,16244,16516,16662,16748,16819,17316,17389,17529,17591,17659,17729,17789,17850,17930,18053,18125,18370,19279,19344,20411,20572,20659,25211,25324,26654,27560,27608,27738,28515,28583,29364,29472,30220,30320", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "16053,16322,16579,16743,16814,16875,17384,17451,17586,17654,17724,17784,17845,17925,17989,18120,18183,18444,19339,19429,20486,20654,20747,25319,25410,26699,27603,27679,27795,28578,28647,29467,29556,30315,30414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,4224,4327,4447,7597,7657,7798,8265,8520,8585,8675,8739,8807,8869,8942,9006,9060,9186,9244,9306,9360,9436,9579,9666,9746,9845,9931,10013,10152,10234,10316,10452,10539,10619,10675,10726,10792,10867,10947,11018,11097,11170,11247,11316,11390,11497,11590,11667,11760,11858,11932,12013,12112,12165,12249,12315,12404,12492,12554,12618,12681,12749,12865,12973,13080,13182,13242,13616,13963,14046,14201", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3130,3210,3292,3391,3487,4322,4442,4523,7652,7716,7885,8339,8580,8670,8734,8802,8864,8937,9001,9055,9181,9239,9301,9355,9431,9574,9661,9741,9840,9926,10008,10147,10229,10311,10447,10534,10614,10670,10721,10787,10862,10942,11013,11092,11165,11242,11311,11385,11492,11585,11662,11755,11853,11927,12008,12107,12160,12244,12310,12399,12487,12549,12613,12676,12744,12860,12968,13075,13177,13237,13292,13697,14041,14120,14275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,152,260,327,395,461,547", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "147,255,322,390,456,542,611"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14381,14478,14586,14653,14721,14787,14873", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "14473,14581,14648,14716,14782,14868,14937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,13877", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,13958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "8204,46657", "endColumns": "60,79", "endOffsets": "8260,46732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,199,261,343,408,481,540,621,696,764,826", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "133,194,256,338,403,476,535,616,691,759,821,893"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16058,16880,17022,17084,17166,17456,17994,18188,18449,18783,19135,19434", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "16136,16936,17079,17161,17226,17524,18048,18264,18519,18846,19192,19501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "46481,46568", "endColumns": "86,88", "endOffsets": "46563,46652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7117,7890,7989,8101", "endColumns": "115,98,111,102", "endOffsets": "7228,7984,8096,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,333,429,634,681,752,857,931,1163,1240,1347,1420,1477,1541,1612,1699,1999,2078,2145,2199,2253,2343,2475,2606,2664,2755,2838,2926,2992,3098,3381,3462,3540,3603,3664,3726,3787,3861,3947,4053,4154,4242,4341,4434,4509,4588,4674,4865,5065,5179,5309,5365,6078,6188,6253", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "170,328,424,629,676,747,852,926,1158,1235,1342,1415,1472,1536,1607,1694,1994,2073,2140,2194,2248,2338,2470,2601,2659,2750,2833,2921,2987,3093,3376,3457,3535,3598,3659,3721,3782,3856,3942,4048,4149,4237,4336,4429,4504,4583,4669,4860,5060,5174,5304,5360,6073,6183,6248,6303"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20971,21091,21249,21445,22507,22554,22625,22730,22804,23036,23113,25138,25415,25612,25914,25985,26354,27058,27137,27684,28652,28862,28952,29084,29215,29273,29864,30132,30587,30737,30843,31126,31207,31458,31638,31699,32009,32588,32662,32840,33061,34794,37223,37322,37415,37490,37569,39272,39966,40166,40280,40410,40546,41259,41369,43194", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "21086,21244,21340,21645,22549,22620,22725,22799,23031,23108,23215,25206,25467,25671,25980,26067,26649,27132,27199,27733,28701,28947,29079,29210,29268,29359,29942,30215,30648,30838,31121,31202,31280,31516,31694,31756,32065,32657,32743,32941,33157,34877,37317,37410,37485,37564,37650,39458,40161,40275,40405,40461,41254,41364,41429,43244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4528,4623,7233,7330,7511,8344,8423,13297,13388,13475,13547,13702,13787,14125,46211,46287,46359", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4618,4701,7325,7424,7592,8418,8515,13383,13470,13542,13611,13782,13872,14196,46282,46354,46476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3492,3589,3691,3790,3890,3997,4103,14280", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3584,3686,3785,3885,3992,4098,4219,14376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,485,545,600,680,761,845,948,1051,1137,1215,1296,1381,1482,1577,1649,1741,1829,1917,2025,2107,2199,2278,2377,2455,2555,2650,2741,2828,2935,3012,3093,3206,3312,3382,3450,4169,4864,4942,5065,5173,5228,5328,5420,5491,5588,5689,5746,5833,5884,5962,6071,6146,6220,6287,6353,6401,6487,6587,6660,6710,6757,6826,6884,6947,7154,7328,7472,7537,7628,7703,7798,7888,7983,8060,8151,8235,8341,8430,8525,8580,8719,8769,8824,8904,8976,9049,9118,9194,9285,9355,9407", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "124,207,269,343,426,480,540,595,675,756,840,943,1046,1132,1210,1291,1376,1477,1572,1644,1736,1824,1912,2020,2102,2194,2273,2372,2450,2550,2645,2736,2823,2930,3007,3088,3201,3307,3377,3445,4164,4859,4937,5060,5168,5223,5323,5415,5486,5583,5684,5741,5828,5879,5957,6066,6141,6215,6282,6348,6396,6482,6582,6655,6705,6752,6821,6879,6942,7149,7323,7467,7532,7623,7698,7793,7883,7978,8055,8146,8230,8336,8425,8520,8575,8714,8764,8819,8899,8971,9044,9113,9189,9280,9350,9402,9483"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14942,15016,15099,15161,15235,15318,15372,15692,15747,15827,15908,16141,16327,16430,16584,16941,17231,18269,18524,18619,18691,18851,18939,19027,19197,19506,19598,19677,19776,19854,19954,20049,20140,20227,20334,20491,20752,20865,21650,21720,21788,23474,24169,24247,24370,24478,24533,24633,24725,24796,24893,24994,25051,25676,25727,25805,26072,26147,26221,26288,26804,27204,27290,27390,27463,27513,27800,27869,27927,27990,28197,28371,28706,28771,29694,29769,29947,30037,30419,30496,30653,31285,31825,31914,39561,39616,39755,39911,40466,42691,42763,42836,42905,42981,43072,43142,44596", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "15011,15094,15156,15230,15313,15367,15427,15742,15822,15903,15987,16239,16425,16511,16657,17017,17311,18365,18614,18686,18778,18934,19022,19130,19274,19593,19672,19771,19849,19949,20044,20135,20222,20329,20406,20567,20860,20966,21715,21783,22502,24164,24242,24365,24473,24528,24628,24720,24791,24888,24989,25046,25133,25722,25800,25909,26142,26216,26283,26349,26847,27285,27385,27458,27508,27555,27864,27922,27985,28192,28366,28510,28766,28857,29764,29859,30032,30127,30491,30582,30732,31386,31909,32004,39611,39750,39800,39961,40541,42758,42831,42900,42976,43067,43137,43189,44672"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4783,4888,5051,5179,5287,5455,5583,5705,5959,6147,6255,6425,6556,6715,6893,6961,7030", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "4883,5046,5174,5282,5450,5578,5700,5809,6142,6250,6420,6551,6710,6888,6956,7025,7112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,228,315,415,504,587,669,809,909,1022,1115,1248,1315,1432,1496,1670,1867,2014,2106,2221,2317,2416,2596,2707,2807,3067,3183,3325,3537,3755,3853,3979,4179,4282,4343,4409,4493,4596,4693,4766,4864,4948,5059,5283,5351,5420,5500,5620,5694,5782,5848,5931,6002,6070,6194,6281,6388,6485,6561,6643,6712,6804,6865,6975,7092,7197,7326,7389,7492,7603,7740,7811,7909,8015,8073,8131,8222,8330,8506,8787,9099,9196,9272,9358,9430,9546,9666,9753,9828,9901,9989,10082,10176,10320,10470,10538,10619,10673,10836,10906,10968,11047,11125,11199,11278,11422,11548,11662,11750,11826,11910,11981", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "127,223,310,410,499,582,664,804,904,1017,1110,1243,1310,1427,1491,1665,1862,2009,2101,2216,2312,2411,2591,2702,2802,3062,3178,3320,3532,3750,3848,3974,4174,4277,4338,4404,4488,4591,4688,4761,4859,4943,5054,5278,5346,5415,5495,5615,5689,5777,5843,5926,5997,6065,6189,6276,6383,6480,6556,6638,6707,6799,6860,6970,7087,7192,7321,7384,7487,7598,7735,7806,7904,8010,8068,8126,8217,8325,8501,8782,9094,9191,9267,9353,9425,9541,9661,9748,9823,9896,9984,10077,10171,10315,10465,10533,10614,10668,10831,10901,10963,11042,11120,11194,11273,11417,11543,11657,11745,11821,11905,11976,12148"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15432,15509,15605,21345,23220,23309,23392,25472,26704,26852,26965,29561,31391,31521,31761,32070,32244,32441,32748,32946,33162,33258,33357,33537,33648,33748,34008,34124,34266,34478,34696,34882,35008,35208,35311,35372,35438,35522,35625,35722,35795,35893,35977,36088,36312,36380,36449,36529,36649,36723,36811,36877,36960,37031,37099,37655,37742,37849,37946,38022,38104,38173,38265,38326,38436,38553,38658,38787,38850,38953,39064,39201,39463,39805,41434,41492,41550,41641,41749,41925,42206,42518,42615,43249,43335,43407,43523,43643,43730,43805,43878,43966,44059,44153,44297,44447,44515,44677,44731,44894,44964,45026,45105,45183,45257,45336,45480,45606,45720,45808,45884,45968,46039", "endColumns": "76,95,86,99,88,82,81,139,99,112,92,132,66,116,63,173,196,146,91,114,95,98,179,110,99,259,115,141,211,217,97,125,199,102,60,65,83,102,96,72,97,83,110,223,67,68,79,119,73,87,65,82,70,67,123,86,106,96,75,81,68,91,60,109,116,104,128,62,102,110,136,70,97,105,57,57,90,107,175,280,311,96,75,85,71,115,119,86,74,72,87,92,93,143,149,67,80,53,162,69,61,78,77,73,78,143,125,113,87,75,83,70,171", "endOffsets": "15504,15600,15687,21440,23304,23387,23469,25607,26799,26960,27053,29689,31453,31633,31820,32239,32436,32583,32835,33056,33253,33352,33532,33643,33743,34003,34119,34261,34473,34691,34789,35003,35203,35306,35367,35433,35517,35620,35717,35790,35888,35972,36083,36307,36375,36444,36524,36644,36718,36806,36872,36955,37026,37094,37218,37737,37844,37941,38017,38099,38168,38260,38321,38431,38548,38653,38782,38845,38948,39059,39196,39267,39556,39906,41487,41545,41636,41744,41920,42201,42513,42610,42686,43330,43402,43518,43638,43725,43800,43873,43961,44054,44148,44292,44442,44510,44591,44726,44889,44959,45021,45100,45178,45252,45331,45475,45601,45715,45803,45879,45963,46034,46206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5814", "endColumns": "144", "endOffsets": "5954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,214", "endColumns": "76,81,76", "endOffsets": "127,209,286"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4706,7429,7721", "endColumns": "76,81,76", "endOffsets": "4778,7506,7793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,204,272,358,429,490,563,630,692,760,830,890,951,1031,1095,1167,1230,1309,1374,1464,1544,1631,1724,1837,1928,1978,2026,2102,2164,2232,2301,2409,2498,2598", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "116,199,267,353,424,485,558,625,687,755,825,885,946,1026,1090,1162,1225,1304,1369,1459,1539,1626,1719,1832,1923,1973,2021,2097,2159,2227,2296,2404,2493,2593,2692"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15992,16244,16516,16662,16748,16819,17316,17389,17529,17591,17659,17729,17789,17850,17930,18053,18125,18370,19279,19344,20411,20572,20659,25211,25324,26654,27560,27608,27738,28515,28583,29364,29472,30220,30320", "endColumns": "65,82,67,85,70,60,72,66,61,67,69,59,60,79,63,71,62,78,64,89,79,86,92,112,90,49,47,75,61,67,68,107,88,99,98", "endOffsets": "16053,16322,16579,16743,16814,16875,17384,17451,17586,17654,17724,17784,17845,17925,17989,18120,18183,18444,19339,19429,20486,20654,20747,25319,25410,26699,27603,27679,27795,28578,28647,29467,29556,30315,30414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,434,516,615,711,814,934,1015,1075,1139,1231,1310,1375,1465,1529,1597,1659,1732,1796,1850,1976,2034,2096,2150,2226,2369,2456,2536,2635,2721,2803,2942,3024,3106,3242,3329,3409,3465,3516,3582,3657,3737,3808,3887,3960,4037,4106,4180,4287,4380,4457,4550,4648,4722,4803,4902,4955,5039,5105,5194,5282,5344,5408,5471,5539,5655,5763,5870,5972,6032,6087,6173,6256,6335", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "268,349,429,511,610,706,809,929,1010,1070,1134,1226,1305,1370,1460,1524,1592,1654,1727,1791,1845,1971,2029,2091,2145,2221,2364,2451,2531,2630,2716,2798,2937,3019,3101,3237,3324,3404,3460,3511,3577,3652,3732,3803,3882,3955,4032,4101,4175,4282,4375,4452,4545,4643,4717,4798,4897,4950,5034,5100,5189,5277,5339,5403,5466,5534,5650,5758,5865,5967,6027,6082,6168,6251,6330,6409"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3054,3135,3215,3297,3396,4224,4327,4447,7597,7657,7798,8265,8520,8585,8675,8739,8807,8869,8942,9006,9060,9186,9244,9306,9360,9436,9579,9666,9746,9845,9931,10013,10152,10234,10316,10452,10539,10619,10675,10726,10792,10867,10947,11018,11097,11170,11247,11316,11390,11497,11590,11667,11760,11858,11932,12013,12112,12165,12249,12315,12404,12492,12554,12618,12681,12749,12865,12973,13080,13182,13242,13616,13963,14046,14201", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,79,81,98,95,102,119,80,59,63,91,78,64,89,63,67,61,72,63,53,125,57,61,53,75,142,86,79,98,85,81,138,81,81,135,86,79,55,50,65,74,79,70,78,72,76,68,73,106,92,76,92,97,73,80,98,52,83,65,88,87,61,63,62,67,115,107,106,101,59,54,85,82,78,78", "endOffsets": "318,3130,3210,3292,3391,3487,4322,4442,4523,7652,7716,7885,8339,8580,8670,8734,8802,8864,8937,9001,9055,9181,9239,9301,9355,9431,9574,9661,9741,9840,9926,10008,10147,10229,10311,10447,10534,10614,10670,10721,10787,10862,10942,11013,11092,11165,11242,11311,11385,11492,11585,11662,11755,11853,11927,12008,12107,12160,12244,12310,12399,12487,12549,12613,12676,12744,12860,12968,13075,13177,13237,13292,13697,14041,14120,14275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,152,260,327,395,461,547", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "147,255,322,390,456,542,611"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14381,14478,14586,14653,14721,14787,14873", "endColumns": "96,107,66,67,65,85,68", "endOffsets": "14473,14581,14648,14716,14782,14868,14937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,537,644,733,834,952,1037,1117,1209,1303,1400,1494,1593,1687,1783,1878,1970,2062,2147,2254,2365,2467,2575,2683,2790,2955,13877", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "426,532,639,728,829,947,1032,1112,1204,1298,1395,1489,1588,1682,1778,1873,1965,2057,2142,2249,2360,2462,2570,2678,2785,2950,3049,13958"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "8204,46657", "endColumns": "60,79", "endOffsets": "8260,46732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,199,261,343,408,481,540,621,696,764,826", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "133,194,256,338,403,476,535,616,691,759,821,893"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16058,16880,17022,17084,17166,17456,17994,18188,18449,18783,19135,19434", "endColumns": "82,60,61,81,64,72,58,80,74,67,61,71", "endOffsets": "16136,16936,17079,17161,17226,17524,18048,18264,18519,18846,19192,19501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "46481,46568", "endColumns": "86,88", "endOffsets": "46563,46652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7117,7890,7989,8101", "endColumns": "115,98,111,102", "endOffsets": "7228,7984,8096,8199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,333,429,634,681,752,857,931,1163,1240,1347,1420,1477,1541,1612,1699,1999,2078,2145,2199,2253,2343,2475,2606,2664,2755,2838,2926,2992,3098,3381,3462,3540,3603,3664,3726,3787,3861,3947,4053,4154,4242,4341,4434,4509,4588,4674,4865,5065,5179,5309,5365,6078,6188,6253", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "170,328,424,629,676,747,852,926,1158,1235,1342,1415,1472,1536,1607,1694,1994,2073,2140,2194,2248,2338,2470,2601,2659,2750,2833,2921,2987,3093,3376,3457,3535,3598,3659,3721,3782,3856,3942,4048,4149,4237,4336,4429,4504,4583,4669,4860,5060,5174,5304,5360,6073,6183,6248,6303"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20971,21091,21249,21445,22507,22554,22625,22730,22804,23036,23113,25138,25415,25612,25914,25985,26354,27058,27137,27684,28652,28862,28952,29084,29215,29273,29864,30132,30587,30737,30843,31126,31207,31458,31638,31699,32009,32588,32662,32840,33061,34794,37223,37322,37415,37490,37569,39272,39966,40166,40280,40410,40546,41259,41369,43194", "endColumns": "119,157,95,204,46,70,104,73,231,76,106,72,56,63,70,86,299,78,66,53,53,89,131,130,57,90,82,87,65,105,282,80,77,62,60,61,60,73,85,105,100,87,98,92,74,78,85,190,199,113,129,55,712,109,64,54", "endOffsets": "21086,21244,21340,21645,22549,22620,22725,22799,23031,23108,23215,25206,25467,25671,25980,26067,26649,27132,27199,27733,28701,28947,29079,29210,29268,29359,29942,30215,30648,30838,31121,31202,31280,31516,31694,31756,32065,32657,32743,32941,33157,34877,37317,37410,37485,37564,37650,39458,40161,40275,40405,40461,41254,41364,41429,43244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4528,4623,7233,7330,7511,8344,8423,13297,13388,13475,13547,13702,13787,14125,46211,46287,46359", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4618,4701,7325,7424,7592,8418,8515,13383,13470,13542,13611,13782,13872,14196,46282,46354,46476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3492,3589,3691,3790,3890,3997,4103,14280", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3584,3686,3785,3885,3992,4098,4219,14376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,485,545,600,680,761,845,948,1051,1137,1215,1296,1381,1482,1577,1649,1741,1829,1917,2025,2107,2199,2278,2377,2455,2555,2650,2741,2828,2935,3012,3093,3206,3312,3382,3450,4169,4864,4942,5065,5173,5228,5328,5420,5491,5588,5689,5746,5833,5884,5962,6071,6146,6220,6287,6353,6401,6487,6587,6660,6710,6757,6826,6884,6947,7154,7328,7472,7537,7628,7703,7798,7888,7983,8060,8151,8235,8341,8430,8525,8580,8719,8769,8824,8904,8976,9049,9118,9194,9285,9355,9407", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "124,207,269,343,426,480,540,595,675,756,840,943,1046,1132,1210,1291,1376,1477,1572,1644,1736,1824,1912,2020,2102,2194,2273,2372,2450,2550,2645,2736,2823,2930,3007,3088,3201,3307,3377,3445,4164,4859,4937,5060,5168,5223,5323,5415,5486,5583,5684,5741,5828,5879,5957,6066,6141,6215,6282,6348,6396,6482,6582,6655,6705,6752,6821,6879,6942,7149,7323,7467,7532,7623,7698,7793,7883,7978,8055,8146,8230,8336,8425,8520,8575,8714,8764,8819,8899,8971,9044,9113,9189,9280,9350,9402,9483"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14942,15016,15099,15161,15235,15318,15372,15692,15747,15827,15908,16141,16327,16430,16584,16941,17231,18269,18524,18619,18691,18851,18939,19027,19197,19506,19598,19677,19776,19854,19954,20049,20140,20227,20334,20491,20752,20865,21650,21720,21788,23474,24169,24247,24370,24478,24533,24633,24725,24796,24893,24994,25051,25676,25727,25805,26072,26147,26221,26288,26804,27204,27290,27390,27463,27513,27800,27869,27927,27990,28197,28371,28706,28771,29694,29769,29947,30037,30419,30496,30653,31285,31825,31914,39561,39616,39755,39911,40466,42691,42763,42836,42905,42981,43072,43142,44596", "endColumns": "73,82,61,73,82,53,59,54,79,80,83,102,102,85,77,80,84,100,94,71,91,87,87,107,81,91,78,98,77,99,94,90,86,106,76,80,112,105,69,67,718,694,77,122,107,54,99,91,70,96,100,56,86,50,77,108,74,73,66,65,47,85,99,72,49,46,68,57,62,206,173,143,64,90,74,94,89,94,76,90,83,105,88,94,54,138,49,54,79,71,72,68,75,90,69,51,80", "endOffsets": "15011,15094,15156,15230,15313,15367,15427,15742,15822,15903,15987,16239,16425,16511,16657,17017,17311,18365,18614,18686,18778,18934,19022,19130,19274,19593,19672,19771,19849,19949,20044,20135,20222,20329,20406,20567,20860,20966,21715,21783,22502,24164,24242,24365,24473,24528,24628,24720,24791,24888,24989,25046,25133,25722,25800,25909,26142,26216,26283,26349,26847,27285,27385,27458,27508,27555,27864,27922,27985,28192,28366,28510,28766,28857,29764,29859,30032,30127,30491,30582,30732,31386,31909,32004,39611,39750,39800,39961,40541,42758,42831,42900,42976,43067,43137,43189,44672"}}]}]}