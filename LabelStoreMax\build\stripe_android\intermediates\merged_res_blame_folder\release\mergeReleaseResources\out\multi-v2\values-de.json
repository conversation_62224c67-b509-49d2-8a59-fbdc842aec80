{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,209", "endColumns": "77,75,76", "endOffsets": "128,204,281"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4715,7446,7737", "endColumns": "77,75,76", "endOffsets": "4788,7517,7809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,247,315,382,449,529", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "140,242,310,377,444,524,598"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14327,14417,14519,14587,14654,14721,14801", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "14412,14514,14582,14649,14716,14796,14870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,337,440,691,738,805,906,975,1265,1328,1426,1494,1549,1613,1691,1785,2107,2183,2247,2300,2353,2444,2575,2691,2748,2837,2918,3004,3071,3177,3533,3613,3690,3753,3813,3876,3936,4010,4093,4189,4288,4371,4477,4577,4651,4730,4821,5060,5308,5425,5555,5614,6420,6524,6589", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "170,332,435,686,733,800,901,970,1260,1323,1421,1489,1544,1608,1686,1780,2102,2178,2242,2295,2348,2439,2570,2686,2743,2832,2913,2999,3066,3172,3528,3608,3685,3748,3808,3871,3931,4005,4088,4184,4283,4366,4472,4572,4646,4725,4816,5055,5303,5420,5550,5609,6415,6519,6584,6639"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20900,21020,21182,21385,22491,22538,22605,22706,22775,23065,23128,25144,25412,25613,25912,25990,26366,27099,27175,27729,28761,29060,29151,29282,29398,29455,30050,30304,30748,30894,31000,31356,31436,31687,31896,31956,32263,32896,32970,33146,33348,35102,37587,37693,37793,37867,37946,39650,40396,40644,40761,40891,41019,41825,41929,43839", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "21015,21177,21280,21631,22533,22600,22701,22770,23060,23123,23221,25207,25462,25672,25985,26079,26683,27170,27234,27777,28809,29146,29277,29393,29450,29539,30126,30385,30810,30995,31351,31431,31508,31745,31951,32014,32318,32965,33048,33237,33442,35180,37688,37788,37862,37941,38032,39884,40639,40756,40886,40945,41820,41924,41989,43889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,13833", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,13910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4793,4902,5066,5194,5306,5484,5615,5736,6000,6180,6292,6461,6592,6754,6930,7001,7064", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "4897,5061,5189,5301,5479,5610,5731,5850,6175,6287,6456,6587,6749,6925,6996,7059,7139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8220,47367", "endColumns": "60,77", "endOffsets": "8276,47440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,190,252,336,405,483,542,618,692,767,833", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "127,185,247,331,400,478,537,613,687,762,828,899"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15983,16788,16924,16986,17070,17367,17906,18098,18344,18674,19036,19336", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "16055,16841,16981,17065,17134,17440,17960,18169,18413,18744,19097,19402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "47190,47277", "endColumns": "86,89", "endOffsets": "47272,47362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,14226", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,14322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,7609,7671,7814,8281,8528,8587,8695,8761,8830,8888,8960,9024,9078,9206,9266,9328,9382,9460,9597,9689,9767,9861,9947,10031,10176,10260,10346,10479,10569,10648,10705,10756,10822,10896,10978,11049,11124,11198,11276,11348,11422,11532,11624,11706,11795,11884,11958,12036,12122,12177,12256,12323,12403,12487,12549,12613,12676,12745,12852,12959,13058,13164,13225,13586,13915,13998,14150", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,7666,7732,7903,8346,8582,8690,8756,8825,8883,8955,9019,9073,9201,9261,9323,9377,9455,9592,9684,9762,9856,9942,10026,10171,10255,10341,10474,10564,10643,10700,10751,10817,10891,10973,11044,11119,11193,11271,11343,11417,11527,11619,11701,11790,11879,11953,12031,12117,12172,12251,12318,12398,12482,12544,12608,12671,12740,12847,12954,13053,13159,13220,13275,13663,13993,14070,14221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7144,7908,8009,8120", "endColumns": "103,100,110,99", "endOffsets": "7243,8004,8115,8215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,197,264,349,418,479,551,618,682,750,820,880,942,1015,1079,1149,1212,1286,1349,1434,1511,1598,1691,1803,1891,1940,1988,2074,2136,2211,2280,2379,2467,2565", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "115,192,259,344,413,474,546,613,677,745,815,875,937,1010,1074,1144,1207,1281,1344,1429,1506,1593,1686,1798,1886,1935,1983,2069,2131,2206,2275,2374,2462,2560,2654"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15918,16157,16417,16573,16658,16727,17228,17300,17445,17509,17577,17647,17707,17769,17842,17965,18035,18270,19188,19251,20351,20523,20610,25212,25324,26688,27595,27643,27782,28617,28692,29544,29643,30390,30488", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "15978,16229,16479,16653,16722,16783,17295,17362,17504,17572,17642,17702,17764,17837,17901,18030,18093,18339,19246,19331,20423,20605,20698,25319,25407,26732,27638,27724,27839,28687,28756,29638,29726,30483,30577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5855", "endColumns": "144", "endOffsets": "5995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,218,299,399,489,567,644,790,890,1002,1101,1234,1295,1441,1508,1700,1908,2081,2174,2280,2377,2473,2669,2775,2871,3129,3245,3383,3601,3832,3935,4062,4274,4371,4435,4502,4593,4689,4780,4856,4954,5047,5150,5394,5466,5534,5610,5731,5808,5897,5982,6071,6143,6212,6337,6430,6534,6628,6704,6787,6858,6948,7011,7116,7234,7342,7472,7538,7634,7735,7878,7950,8042,8149,8208,8273,8359,8468,8658,8976,9311,9410,9496,9580,9653,9770,9907,9998,10073,10151,10246,10329,10421,10562,10714,10782,10856,10915,11099,11167,11226,11304,11378,11456,11544,11689,11828,11952,12044,12124,12210,12284", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "123,213,294,394,484,562,639,785,885,997,1096,1229,1290,1436,1503,1695,1903,2076,2169,2275,2372,2468,2664,2770,2866,3124,3240,3378,3596,3827,3930,4057,4269,4366,4430,4497,4588,4684,4775,4851,4949,5042,5145,5389,5461,5529,5605,5726,5803,5892,5977,6066,6138,6207,6332,6425,6529,6623,6699,6782,6853,6943,7006,7111,7229,7337,7467,7533,7629,7730,7873,7945,8037,8144,8203,8268,8354,8463,8653,8971,9306,9405,9491,9575,9648,9765,9902,9993,10068,10146,10241,10324,10416,10557,10709,10777,10851,10910,11094,11162,11221,11299,11373,11451,11539,11684,11823,11947,12039,12119,12205,12279,12446"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15361,15434,15524,21285,23226,23316,23394,25467,26737,26888,27000,29731,31626,31750,32019,32323,32515,32723,33053,33242,33447,33544,33640,33836,33942,34038,34296,34412,34550,34768,34999,35185,35312,35524,35621,35685,35752,35843,35939,36030,36106,36204,36297,36400,36644,36716,36784,36860,36981,37058,37147,37232,37321,37393,37462,38037,38130,38234,38328,38404,38487,38558,38648,38711,38816,38934,39042,39172,39238,39334,39435,39578,39889,40234,41994,42053,42118,42204,42313,42503,42821,43156,43255,43894,43978,44051,44168,44305,44396,44471,44549,44644,44727,44819,44960,45112,45180,45330,45389,45573,45641,45700,45778,45852,45930,46018,46163,46302,46426,46518,46598,46684,46758", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "15429,15519,15600,21380,23311,23389,23466,25608,26832,26995,27094,29859,31682,31891,32081,32510,32718,32891,33141,33343,33539,33635,33831,33937,34033,34291,34407,34545,34763,34994,35097,35307,35519,35616,35680,35747,35838,35934,36025,36101,36199,36292,36395,36639,36711,36779,36855,36976,37053,37142,37227,37316,37388,37457,37582,38125,38229,38323,38399,38482,38553,38643,38706,38811,38929,39037,39167,39233,39329,39430,39573,39645,39976,40336,42048,42113,42199,42308,42498,42816,43151,43250,43336,43973,44046,44163,44300,44391,44466,44544,44639,44722,44814,44955,45107,45175,45249,45384,45568,45636,45695,45773,45847,45925,46013,46158,46297,46421,46513,46593,46679,46753,46920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "28968", "endColumns": "91", "endOffsets": "29055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,273,342,420,482,541,597,684,766,854,951,1048,1134,1223,1301,1390,1486,1580,1651,1742,1827,1916,2029,2115,2206,2284,2382,2471,2575,2664,2755,2849,2974,3059,3154,3256,3351,3436,3501,4206,4899,4973,5103,5209,5264,5372,5472,5540,5634,5734,5791,5879,5931,6011,6114,6191,6263,6330,6396,6447,6528,6623,6705,6752,6803,6878,6936,6997,7217,7417,7576,7641,7730,7820,7916,8002,8089,8166,8255,8334,8447,8534,8624,8680,8825,8877,8932,9001,9070,9145,9209,9281,9370,9443,9499", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "120,199,268,337,415,477,536,592,679,761,849,946,1043,1129,1218,1296,1385,1481,1575,1646,1737,1822,1911,2024,2110,2201,2279,2377,2466,2570,2659,2750,2844,2969,3054,3149,3251,3346,3431,3496,4201,4894,4968,5098,5204,5259,5367,5467,5535,5629,5729,5786,5874,5926,6006,6109,6186,6258,6325,6391,6442,6523,6618,6700,6747,6798,6873,6931,6992,7212,7412,7571,7636,7725,7815,7911,7997,8084,8161,8250,8329,8442,8529,8619,8675,8820,8872,8927,8996,9065,9140,9204,9276,9365,9438,9494,9570"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14875,14945,15024,15093,15162,15240,15302,15605,15661,15748,15830,16060,16234,16331,16484,16846,17139,18174,18418,18512,18583,18749,18834,18923,19102,19407,19498,19576,19674,19763,19867,19956,20047,20141,20266,20428,20703,20805,21636,21721,21786,23471,24164,24238,24368,24474,24529,24637,24737,24805,24899,24999,25056,25677,25729,25809,26084,26161,26233,26300,26837,27239,27320,27415,27497,27544,27844,27919,27977,28038,28258,28458,28814,28879,29864,29954,30131,30217,30582,30659,30815,31513,32086,32173,39981,40037,40182,40341,40950,43341,43410,43485,43549,43621,43710,43783,45254", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "14940,15019,15088,15157,15235,15297,15356,15656,15743,15825,15913,16152,16326,16412,16568,16919,17223,18265,18507,18578,18669,18829,18918,19031,19183,19493,19571,19669,19758,19862,19951,20042,20136,20261,20346,20518,20800,20895,21716,21781,22486,24159,24233,24363,24469,24524,24632,24732,24800,24894,24994,25051,25139,25724,25804,25907,26156,26228,26295,26361,26883,27315,27410,27492,27539,27590,27914,27972,28033,28253,28453,28612,28874,28963,29949,30045,30212,30299,30654,30743,30889,31621,32168,32258,40032,40177,40229,40391,41014,43405,43480,43544,43616,43705,43778,43834,45325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4531,4627,7248,7346,7522,8351,8436,13280,13369,13457,13522,13668,13749,14075,46925,47004,47070", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "4622,4710,7341,7441,7604,8431,8523,13364,13452,13517,13581,13744,13828,14145,46999,47065,47185"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,209", "endColumns": "77,75,76", "endOffsets": "128,204,281"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4715,7446,7737", "endColumns": "77,75,76", "endOffsets": "4788,7517,7809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,247,315,382,449,529", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "140,242,310,377,444,524,598"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14327,14417,14519,14587,14654,14721,14801", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "14412,14514,14582,14649,14716,14796,14870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,337,440,691,738,805,906,975,1265,1328,1426,1494,1549,1613,1691,1785,2107,2183,2247,2300,2353,2444,2575,2691,2748,2837,2918,3004,3071,3177,3533,3613,3690,3753,3813,3876,3936,4010,4093,4189,4288,4371,4477,4577,4651,4730,4821,5060,5308,5425,5555,5614,6420,6524,6589", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "170,332,435,686,733,800,901,970,1260,1323,1421,1489,1544,1608,1686,1780,2102,2178,2242,2295,2348,2439,2570,2686,2743,2832,2913,2999,3066,3172,3528,3608,3685,3748,3808,3871,3931,4005,4088,4184,4283,4366,4472,4572,4646,4725,4816,5055,5303,5420,5550,5609,6415,6519,6584,6639"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20900,21020,21182,21385,22491,22538,22605,22706,22775,23065,23128,25144,25412,25613,25912,25990,26366,27099,27175,27729,28761,29060,29151,29282,29398,29455,30050,30304,30748,30894,31000,31356,31436,31687,31896,31956,32263,32896,32970,33146,33348,35102,37587,37693,37793,37867,37946,39650,40396,40644,40761,40891,41019,41825,41929,43839", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "21015,21177,21280,21631,22533,22600,22701,22770,23060,23123,23221,25207,25462,25672,25985,26079,26683,27170,27234,27777,28809,29146,29277,29393,29450,29539,30126,30385,30810,30995,31351,31431,31508,31745,31951,32014,32318,32965,33048,33237,33442,35180,37688,37788,37862,37941,38032,39884,40639,40756,40886,40945,41820,41924,41989,43889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,13833", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,13910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4793,4902,5066,5194,5306,5484,5615,5736,6000,6180,6292,6461,6592,6754,6930,7001,7064", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "4897,5061,5189,5301,5479,5610,5731,5850,6175,6287,6456,6587,6749,6925,6996,7059,7139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8220,47367", "endColumns": "60,77", "endOffsets": "8276,47440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,190,252,336,405,483,542,618,692,767,833", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "127,185,247,331,400,478,537,613,687,762,828,899"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15983,16788,16924,16986,17070,17367,17906,18098,18344,18674,19036,19336", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "16055,16841,16981,17065,17134,17440,17960,18169,18413,18744,19097,19402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "47190,47277", "endColumns": "86,89", "endOffsets": "47272,47362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,14226", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,14322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,7609,7671,7814,8281,8528,8587,8695,8761,8830,8888,8960,9024,9078,9206,9266,9328,9382,9460,9597,9689,9767,9861,9947,10031,10176,10260,10346,10479,10569,10648,10705,10756,10822,10896,10978,11049,11124,11198,11276,11348,11422,11532,11624,11706,11795,11884,11958,12036,12122,12177,12256,12323,12403,12487,12549,12613,12676,12745,12852,12959,13058,13164,13225,13586,13915,13998,14150", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,7666,7732,7903,8346,8582,8690,8756,8825,8883,8955,9019,9073,9201,9261,9323,9377,9455,9592,9684,9762,9856,9942,10026,10171,10255,10341,10474,10564,10643,10700,10751,10817,10891,10973,11044,11119,11193,11271,11343,11417,11527,11619,11701,11790,11879,11953,12031,12117,12172,12251,12318,12398,12482,12544,12608,12671,12740,12847,12954,13053,13159,13220,13275,13663,13993,14070,14221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7144,7908,8009,8120", "endColumns": "103,100,110,99", "endOffsets": "7243,8004,8115,8215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,197,264,349,418,479,551,618,682,750,820,880,942,1015,1079,1149,1212,1286,1349,1434,1511,1598,1691,1803,1891,1940,1988,2074,2136,2211,2280,2379,2467,2565", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "115,192,259,344,413,474,546,613,677,745,815,875,937,1010,1074,1144,1207,1281,1344,1429,1506,1593,1686,1798,1886,1935,1983,2069,2131,2206,2275,2374,2462,2560,2654"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15918,16157,16417,16573,16658,16727,17228,17300,17445,17509,17577,17647,17707,17769,17842,17965,18035,18270,19188,19251,20351,20523,20610,25212,25324,26688,27595,27643,27782,28617,28692,29544,29643,30390,30488", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "15978,16229,16479,16653,16722,16783,17295,17362,17504,17572,17642,17702,17764,17837,17901,18030,18093,18339,19246,19331,20423,20605,20698,25319,25407,26732,27638,27724,27839,28687,28756,29638,29726,30483,30577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5855", "endColumns": "144", "endOffsets": "5995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,218,299,399,489,567,644,790,890,1002,1101,1234,1295,1441,1508,1700,1908,2081,2174,2280,2377,2473,2669,2775,2871,3129,3245,3383,3601,3832,3935,4062,4274,4371,4435,4502,4593,4689,4780,4856,4954,5047,5150,5394,5466,5534,5610,5731,5808,5897,5982,6071,6143,6212,6337,6430,6534,6628,6704,6787,6858,6948,7011,7116,7234,7342,7472,7538,7634,7735,7878,7950,8042,8149,8208,8273,8359,8468,8658,8976,9311,9410,9496,9580,9653,9770,9907,9998,10073,10151,10246,10329,10421,10562,10714,10782,10856,10915,11099,11167,11226,11304,11378,11456,11544,11689,11828,11952,12044,12124,12210,12284", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "123,213,294,394,484,562,639,785,885,997,1096,1229,1290,1436,1503,1695,1903,2076,2169,2275,2372,2468,2664,2770,2866,3124,3240,3378,3596,3827,3930,4057,4269,4366,4430,4497,4588,4684,4775,4851,4949,5042,5145,5389,5461,5529,5605,5726,5803,5892,5977,6066,6138,6207,6332,6425,6529,6623,6699,6782,6853,6943,7006,7111,7229,7337,7467,7533,7629,7730,7873,7945,8037,8144,8203,8268,8354,8463,8653,8971,9306,9405,9491,9575,9648,9765,9902,9993,10068,10146,10241,10324,10416,10557,10709,10777,10851,10910,11094,11162,11221,11299,11373,11451,11539,11684,11823,11947,12039,12119,12205,12279,12446"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15361,15434,15524,21285,23226,23316,23394,25467,26737,26888,27000,29731,31626,31750,32019,32323,32515,32723,33053,33242,33447,33544,33640,33836,33942,34038,34296,34412,34550,34768,34999,35185,35312,35524,35621,35685,35752,35843,35939,36030,36106,36204,36297,36400,36644,36716,36784,36860,36981,37058,37147,37232,37321,37393,37462,38037,38130,38234,38328,38404,38487,38558,38648,38711,38816,38934,39042,39172,39238,39334,39435,39578,39889,40234,41994,42053,42118,42204,42313,42503,42821,43156,43255,43894,43978,44051,44168,44305,44396,44471,44549,44644,44727,44819,44960,45112,45180,45330,45389,45573,45641,45700,45778,45852,45930,46018,46163,46302,46426,46518,46598,46684,46758", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "15429,15519,15600,21380,23311,23389,23466,25608,26832,26995,27094,29859,31682,31891,32081,32510,32718,32891,33141,33343,33539,33635,33831,33937,34033,34291,34407,34545,34763,34994,35097,35307,35519,35616,35680,35747,35838,35934,36025,36101,36199,36292,36395,36639,36711,36779,36855,36976,37053,37142,37227,37316,37388,37457,37582,38125,38229,38323,38399,38482,38553,38643,38706,38811,38929,39037,39167,39233,39329,39430,39573,39645,39976,40336,42048,42113,42199,42308,42498,42816,43151,43250,43336,43973,44046,44163,44300,44391,44466,44544,44639,44722,44814,44955,45107,45175,45249,45384,45568,45636,45695,45773,45847,45925,46013,46158,46297,46421,46513,46593,46679,46753,46920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "28968", "endColumns": "91", "endOffsets": "29055"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,273,342,420,482,541,597,684,766,854,951,1048,1134,1223,1301,1390,1486,1580,1651,1742,1827,1916,2029,2115,2206,2284,2382,2471,2575,2664,2755,2849,2974,3059,3154,3256,3351,3436,3501,4206,4899,4973,5103,5209,5264,5372,5472,5540,5634,5734,5791,5879,5931,6011,6114,6191,6263,6330,6396,6447,6528,6623,6705,6752,6803,6878,6936,6997,7217,7417,7576,7641,7730,7820,7916,8002,8089,8166,8255,8334,8447,8534,8624,8680,8825,8877,8932,9001,9070,9145,9209,9281,9370,9443,9499", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "120,199,268,337,415,477,536,592,679,761,849,946,1043,1129,1218,1296,1385,1481,1575,1646,1737,1822,1911,2024,2110,2201,2279,2377,2466,2570,2659,2750,2844,2969,3054,3149,3251,3346,3431,3496,4201,4894,4968,5098,5204,5259,5367,5467,5535,5629,5729,5786,5874,5926,6006,6109,6186,6258,6325,6391,6442,6523,6618,6700,6747,6798,6873,6931,6992,7212,7412,7571,7636,7725,7815,7911,7997,8084,8161,8250,8329,8442,8529,8619,8675,8820,8872,8927,8996,9065,9140,9204,9276,9365,9438,9494,9570"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14875,14945,15024,15093,15162,15240,15302,15605,15661,15748,15830,16060,16234,16331,16484,16846,17139,18174,18418,18512,18583,18749,18834,18923,19102,19407,19498,19576,19674,19763,19867,19956,20047,20141,20266,20428,20703,20805,21636,21721,21786,23471,24164,24238,24368,24474,24529,24637,24737,24805,24899,24999,25056,25677,25729,25809,26084,26161,26233,26300,26837,27239,27320,27415,27497,27544,27844,27919,27977,28038,28258,28458,28814,28879,29864,29954,30131,30217,30582,30659,30815,31513,32086,32173,39981,40037,40182,40341,40950,43341,43410,43485,43549,43621,43710,43783,45254", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "14940,15019,15088,15157,15235,15297,15356,15656,15743,15825,15913,16152,16326,16412,16568,16919,17223,18265,18507,18578,18669,18829,18918,19031,19183,19493,19571,19669,19758,19862,19951,20042,20136,20261,20346,20518,20800,20895,21716,21781,22486,24159,24233,24363,24469,24524,24632,24732,24800,24894,24994,25051,25139,25724,25804,25907,26156,26228,26295,26361,26883,27315,27410,27492,27539,27590,27914,27972,28033,28253,28453,28612,28874,28963,29949,30045,30212,30299,30654,30743,30889,31621,32168,32258,40032,40177,40229,40391,41014,43405,43480,43544,43616,43705,43778,43834,45325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4531,4627,7248,7346,7522,8351,8436,13280,13369,13457,13522,13668,13749,14075,46925,47004,47070", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "4622,4710,7341,7441,7604,8431,8523,13364,13452,13517,13581,13744,13828,14145,46999,47065,47185"}}]}]}