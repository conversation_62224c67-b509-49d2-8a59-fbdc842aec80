{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8335,48048", "endColumns": "60,78", "endOffsets": "8391,48122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7227,8009,8111,8230", "endColumns": "106,101,118,104", "endOffsets": "7329,8106,8225,8330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,196,258,340,408,479,537,619,692,759,819", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "131,191,253,335,403,474,532,614,687,754,814,884"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16205,17028,17170,17232,17314,17617,18170,18362,18613,18943,19321,19613", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "16281,17083,17227,17309,17377,17683,18223,18439,18681,19005,19376,19678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,322,423,633,680,752,856,931,1208,1271,1366,1440,1495,1559,1632,1721,2042,2109,2176,2231,2286,2380,2530,2643,2702,2789,2877,2965,3036,3126,3428,3509,3588,3665,3727,3789,3853,3937,4016,4142,4247,4346,4464,4567,4641,4720,4811,5009,5218,5348,5492,5554,6355,6463,6528", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "154,317,418,628,675,747,851,926,1203,1266,1361,1435,1490,1554,1627,1716,2037,2104,2171,2226,2281,2375,2525,2638,2697,2784,2872,2960,3031,3121,3423,3504,3583,3660,3722,3784,3848,3932,4011,4137,4242,4341,4459,4562,4636,4715,4806,5004,5213,5343,5487,5549,6350,6458,6523,6581"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21188,21292,21455,21640,22761,22808,22880,22984,23059,23336,23399,25421,25706,25913,26228,26301,26676,27400,27467,28041,29068,29367,29461,29611,29724,29783,30406,30712,31203,31354,31444,31746,31827,32086,32287,32349,32681,33288,33372,33541,33777,35605,38128,38246,38349,38423,38502,40310,41051,41260,41390,41534,41665,42466,42574,44485", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "21287,21450,21551,21845,22803,22875,22979,23054,23331,23394,23489,25490,25756,25972,26296,26385,26992,27462,27529,28091,29118,29456,29606,29719,29778,29865,30489,30795,31269,31439,31741,31822,31901,32158,32344,32406,32740,33367,33446,33662,33877,35699,38241,38344,38418,38497,38588,40503,41255,41385,41529,41591,42461,42569,42634,44538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "47864,47953", "endColumns": "88,94", "endOffsets": "47948,48043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,7704,7769,7912,8396,8650,8716,8818,8883,8958,9014,9093,9153,9207,9329,9388,9450,9504,9586,9721,9813,9888,9983,10064,10148,10292,10371,10452,10593,10686,10765,10820,10871,10937,11017,11098,11169,11249,11322,11400,11473,11545,11657,11750,11822,11914,12006,12080,12164,12256,12313,12397,12463,12546,12633,12695,12759,12822,12900,13002,13106,13203,13307,13366,13739,14081,14168,14321", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,7764,7830,8004,8474,8711,8813,8878,8953,9009,9088,9148,9202,9324,9383,9445,9499,9581,9716,9808,9883,9978,10059,10143,10287,10366,10447,10588,10681,10760,10815,10866,10932,11012,11093,11164,11244,11317,11395,11468,11540,11652,11745,11817,11909,12001,12075,12159,12251,12308,12392,12458,12541,12628,12690,12754,12817,12895,12997,13101,13198,13302,13361,13416,13823,14163,14240,14397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,201,268,353,424,485,557,630,692,764,834,902,962,1036,1112,1183,1246,1311,1376,1461,1542,1633,1730,1859,1941,1992,2040,2132,2196,2271,2342,2462,2551,2663", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "115,196,263,348,419,480,552,625,687,759,829,897,957,1031,1107,1178,1241,1306,1371,1456,1537,1628,1725,1854,1936,1987,2035,2127,2191,2266,2337,2457,2546,2658,2770"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16140,16389,16661,16811,16896,16967,17472,17544,17688,17750,17822,17892,17960,18020,18094,18228,18299,18548,19463,19528,20597,20763,20854,25495,25624,26997,27901,27949,28096,28922,28997,29870,29990,30800,30912", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "16200,16465,16723,16891,16962,17023,17539,17612,17745,17817,17887,17955,18015,18089,18165,18294,18357,18608,19523,19608,20673,20849,20946,25619,25701,27043,27944,28036,28155,28992,29063,29985,30074,30907,31019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,13994", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,14076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,213", "endColumns": "74,82,76", "endOffsets": "125,208,285"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4750,7534,7835", "endColumns": "74,82,76", "endOffsets": "4820,7612,7907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,454,529", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "140,246,313,381,449,524,592"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14503,14593,14699,14766,14834,14902,14977", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "14588,14694,14761,14829,14897,14972,15040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,388,482,566,649,801,885,1001,1105,1238,1304,1428,1493,1664,1863,2036,2126,2236,2338,2440,2633,2755,2857,3120,3231,3373,3604,3859,3959,4096,4306,4405,4466,4532,4617,4719,4822,4897,4997,5089,5194,5447,5517,5589,5670,5804,5879,5972,6039,6123,6194,6263,6383,6480,6594,6697,6785,6884,6955,7050,7115,7230,7347,7454,7596,7661,7765,7877,8028,8100,8196,8310,8372,8430,8509,8633,8813,9124,9458,9557,9637,9738,9814,9939,10096,10182,10270,10340,10427,10519,10616,10747,10889,10969,11041,11100,11264,11333,11394,11479,11555,11633,11727,11883,12004,12125,12217,12293,12375,12452", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "129,216,299,383,477,561,644,796,880,996,1100,1233,1299,1423,1488,1659,1858,2031,2121,2231,2333,2435,2628,2750,2852,3115,3226,3368,3599,3854,3954,4091,4301,4400,4461,4527,4612,4714,4817,4892,4992,5084,5189,5442,5512,5584,5665,5799,5874,5967,6034,6118,6189,6258,6378,6475,6589,6692,6780,6879,6950,7045,7110,7225,7342,7449,7591,7656,7760,7872,8023,8095,8191,8305,8367,8425,8504,8628,8808,9119,9453,9552,9632,9733,9809,9934,10091,10177,10265,10335,10422,10514,10611,10742,10884,10964,11036,11095,11259,11328,11389,11474,11550,11628,11722,11878,11999,12120,12212,12288,12370,12447,12605"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15561,15640,15727,21556,23494,23588,23672,25761,27048,27180,27296,30079,32020,32163,32411,32745,32916,33115,33451,33667,33882,33984,34086,34279,34401,34503,34766,34877,35019,35250,35505,35704,35841,36051,36150,36211,36277,36362,36464,36567,36642,36742,36834,36939,37192,37262,37334,37415,37549,37624,37717,37784,37868,37939,38008,38593,38690,38804,38907,38995,39094,39165,39260,39325,39440,39557,39664,39806,39871,39975,40087,40238,40508,40855,42639,42701,42759,42838,42962,43142,43453,43787,43886,44543,44644,44720,44845,45002,45088,45176,45246,45333,45425,45522,45653,45795,45875,46026,46085,46249,46318,46379,46464,46540,46618,46712,46868,46989,47110,47202,47278,47360,47437", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "15635,15722,15805,21635,23583,23667,23750,25908,27127,27291,27395,30207,32081,32282,32471,32911,33110,33283,33536,33772,33979,34081,34274,34396,34498,34761,34872,35014,35245,35500,35600,35836,36046,36145,36206,36272,36357,36459,36562,36637,36737,36829,36934,37187,37257,37329,37410,37544,37619,37712,37779,37863,37934,38003,38123,38685,38799,38902,38990,39089,39160,39255,39320,39435,39552,39659,39801,39866,39970,40082,40233,40305,40599,40964,42696,42754,42833,42957,43137,43448,43782,43881,43961,44639,44715,44840,44997,45083,45171,45241,45328,45420,45517,45648,45790,45870,45942,46080,46244,46313,46374,46459,46535,46613,46707,46863,46984,47105,47197,47273,47355,47432,47590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5888", "endColumns": "164", "endOffsets": "6048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,14402", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,14498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4825,4931,5111,5241,5350,5521,5654,5775,6053,6231,6343,6528,6664,6824,7003,7076,7143", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4926,5106,5236,5345,5516,5649,5770,5883,6226,6338,6523,6659,6819,6998,7071,7138,7222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "29280", "endColumns": "86", "endOffsets": "29362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,228,290,366,451,512,571,649,727,815,901,1004,1107,1195,1278,1360,1450,1554,1649,1719,1811,1900,1997,2122,2204,2296,2373,2472,2548,2648,2748,2843,2928,3043,3118,3203,3325,3440,3523,3588,4351,5022,5101,5215,5326,5381,5492,5604,5672,5773,5873,5929,6017,6067,6150,6268,6344,6421,6488,6554,6602,6690,6793,6872,6920,6969,7055,7113,7177,7378,7574,7731,7799,7888,7974,8082,8190,8300,8383,8479,8559,8673,8775,8878,8932,9077,9129,9211,9280,9350,9426,9496,9570,9668,9748,9799", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "132,223,285,361,446,507,566,644,722,810,896,999,1102,1190,1273,1355,1445,1549,1644,1714,1806,1895,1992,2117,2199,2291,2368,2467,2543,2643,2743,2838,2923,3038,3113,3198,3320,3435,3518,3583,4346,5017,5096,5210,5321,5376,5487,5599,5667,5768,5868,5924,6012,6062,6145,6263,6339,6416,6483,6549,6597,6685,6788,6867,6915,6964,7050,7108,7172,7373,7569,7726,7794,7883,7969,8077,8185,8295,8378,8474,8554,8668,8770,8873,8927,9072,9124,9206,9275,9345,9421,9491,9565,9663,9743,9794,9873"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15045,15127,15218,15280,15356,15441,15502,15810,15888,15966,16054,16286,16470,16573,16728,17088,17382,18444,18686,18781,18851,19010,19099,19196,19381,19683,19775,19852,19951,20027,20127,20227,20322,20407,20522,20678,20951,21073,21850,21933,21998,23755,24426,24505,24619,24730,24785,24896,25008,25076,25177,25277,25333,25977,26027,26110,26390,26466,26543,26610,27132,27534,27622,27725,27804,27852,28160,28246,28304,28368,28569,28765,29123,29191,30212,30298,30494,30602,31024,31107,31274,31906,32476,32578,40604,40658,40803,40969,41596,43966,44036,44112,44182,44256,44354,44434,45947", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "15122,15213,15275,15351,15436,15497,15556,15883,15961,16049,16135,16384,16568,16656,16806,17165,17467,18543,18776,18846,18938,19094,19191,19316,19458,19770,19847,19946,20022,20122,20222,20317,20402,20517,20592,20758,21068,21183,21928,21993,22756,24421,24500,24614,24725,24780,24891,25003,25071,25172,25272,25328,25416,26022,26105,26223,26461,26538,26605,26671,27175,27617,27720,27799,27847,27896,28241,28299,28363,28564,28760,28917,29186,29275,30293,30401,30597,30707,31102,31198,31349,32015,32573,32676,40653,40798,40850,41046,41660,44031,44107,44177,44251,44349,44429,44480,46021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4662,7334,7434,7617,8479,8558,13421,13513,13600,13671,13828,13909,14245,47595,47673,47742", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4657,4745,7429,7529,7699,8553,8645,13508,13595,13666,13734,13904,13989,14316,47668,47737,47859"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8335,48048", "endColumns": "60,78", "endOffsets": "8391,48122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7227,8009,8111,8230", "endColumns": "106,101,118,104", "endOffsets": "7329,8106,8225,8330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,196,258,340,408,479,537,619,692,759,819", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "131,191,253,335,403,474,532,614,687,754,814,884"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16205,17028,17170,17232,17314,17617,18170,18362,18613,18943,19321,19613", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "16281,17083,17227,17309,17377,17683,18223,18439,18681,19005,19376,19678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,322,423,633,680,752,856,931,1208,1271,1366,1440,1495,1559,1632,1721,2042,2109,2176,2231,2286,2380,2530,2643,2702,2789,2877,2965,3036,3126,3428,3509,3588,3665,3727,3789,3853,3937,4016,4142,4247,4346,4464,4567,4641,4720,4811,5009,5218,5348,5492,5554,6355,6463,6528", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "154,317,418,628,675,747,851,926,1203,1266,1361,1435,1490,1554,1627,1716,2037,2104,2171,2226,2281,2375,2525,2638,2697,2784,2872,2960,3031,3121,3423,3504,3583,3660,3722,3784,3848,3932,4011,4137,4242,4341,4459,4562,4636,4715,4806,5004,5213,5343,5487,5549,6350,6458,6523,6581"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21188,21292,21455,21640,22761,22808,22880,22984,23059,23336,23399,25421,25706,25913,26228,26301,26676,27400,27467,28041,29068,29367,29461,29611,29724,29783,30406,30712,31203,31354,31444,31746,31827,32086,32287,32349,32681,33288,33372,33541,33777,35605,38128,38246,38349,38423,38502,40310,41051,41260,41390,41534,41665,42466,42574,44485", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "21287,21450,21551,21845,22803,22875,22979,23054,23331,23394,23489,25490,25756,25972,26296,26385,26992,27462,27529,28091,29118,29456,29606,29719,29778,29865,30489,30795,31269,31439,31741,31822,31901,32158,32344,32406,32740,33367,33446,33662,33877,35699,38241,38344,38418,38497,38588,40503,41255,41385,41529,41591,42461,42569,42634,44538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "47864,47953", "endColumns": "88,94", "endOffsets": "47948,48043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,7704,7769,7912,8396,8650,8716,8818,8883,8958,9014,9093,9153,9207,9329,9388,9450,9504,9586,9721,9813,9888,9983,10064,10148,10292,10371,10452,10593,10686,10765,10820,10871,10937,11017,11098,11169,11249,11322,11400,11473,11545,11657,11750,11822,11914,12006,12080,12164,12256,12313,12397,12463,12546,12633,12695,12759,12822,12900,13002,13106,13203,13307,13366,13739,14081,14168,14321", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,7764,7830,8004,8474,8711,8813,8878,8953,9009,9088,9148,9202,9324,9383,9445,9499,9581,9716,9808,9883,9978,10059,10143,10287,10366,10447,10588,10681,10760,10815,10866,10932,11012,11093,11164,11244,11317,11395,11468,11540,11652,11745,11817,11909,12001,12075,12159,12251,12308,12392,12458,12541,12628,12690,12754,12817,12895,12997,13101,13198,13302,13361,13416,13823,14163,14240,14397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,201,268,353,424,485,557,630,692,764,834,902,962,1036,1112,1183,1246,1311,1376,1461,1542,1633,1730,1859,1941,1992,2040,2132,2196,2271,2342,2462,2551,2663", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "115,196,263,348,419,480,552,625,687,759,829,897,957,1031,1107,1178,1241,1306,1371,1456,1537,1628,1725,1854,1936,1987,2035,2127,2191,2266,2337,2457,2546,2658,2770"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16140,16389,16661,16811,16896,16967,17472,17544,17688,17750,17822,17892,17960,18020,18094,18228,18299,18548,19463,19528,20597,20763,20854,25495,25624,26997,27901,27949,28096,28922,28997,29870,29990,30800,30912", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "16200,16465,16723,16891,16962,17023,17539,17612,17745,17817,17887,17955,18015,18089,18165,18294,18357,18608,19523,19608,20673,20849,20946,25619,25701,27043,27944,28036,28155,28992,29063,29985,30074,30907,31019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,13994", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,14076"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,213", "endColumns": "74,82,76", "endOffsets": "125,208,285"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4750,7534,7835", "endColumns": "74,82,76", "endOffsets": "4820,7612,7907"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,454,529", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "140,246,313,381,449,524,592"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14503,14593,14699,14766,14834,14902,14977", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "14588,14694,14761,14829,14897,14972,15040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,388,482,566,649,801,885,1001,1105,1238,1304,1428,1493,1664,1863,2036,2126,2236,2338,2440,2633,2755,2857,3120,3231,3373,3604,3859,3959,4096,4306,4405,4466,4532,4617,4719,4822,4897,4997,5089,5194,5447,5517,5589,5670,5804,5879,5972,6039,6123,6194,6263,6383,6480,6594,6697,6785,6884,6955,7050,7115,7230,7347,7454,7596,7661,7765,7877,8028,8100,8196,8310,8372,8430,8509,8633,8813,9124,9458,9557,9637,9738,9814,9939,10096,10182,10270,10340,10427,10519,10616,10747,10889,10969,11041,11100,11264,11333,11394,11479,11555,11633,11727,11883,12004,12125,12217,12293,12375,12452", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "129,216,299,383,477,561,644,796,880,996,1100,1233,1299,1423,1488,1659,1858,2031,2121,2231,2333,2435,2628,2750,2852,3115,3226,3368,3599,3854,3954,4091,4301,4400,4461,4527,4612,4714,4817,4892,4992,5084,5189,5442,5512,5584,5665,5799,5874,5967,6034,6118,6189,6258,6378,6475,6589,6692,6780,6879,6950,7045,7110,7225,7342,7449,7591,7656,7760,7872,8023,8095,8191,8305,8367,8425,8504,8628,8808,9119,9453,9552,9632,9733,9809,9934,10091,10177,10265,10335,10422,10514,10611,10742,10884,10964,11036,11095,11259,11328,11389,11474,11550,11628,11722,11878,11999,12120,12212,12288,12370,12447,12605"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15561,15640,15727,21556,23494,23588,23672,25761,27048,27180,27296,30079,32020,32163,32411,32745,32916,33115,33451,33667,33882,33984,34086,34279,34401,34503,34766,34877,35019,35250,35505,35704,35841,36051,36150,36211,36277,36362,36464,36567,36642,36742,36834,36939,37192,37262,37334,37415,37549,37624,37717,37784,37868,37939,38008,38593,38690,38804,38907,38995,39094,39165,39260,39325,39440,39557,39664,39806,39871,39975,40087,40238,40508,40855,42639,42701,42759,42838,42962,43142,43453,43787,43886,44543,44644,44720,44845,45002,45088,45176,45246,45333,45425,45522,45653,45795,45875,46026,46085,46249,46318,46379,46464,46540,46618,46712,46868,46989,47110,47202,47278,47360,47437", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "15635,15722,15805,21635,23583,23667,23750,25908,27127,27291,27395,30207,32081,32282,32471,32911,33110,33283,33536,33772,33979,34081,34274,34396,34498,34761,34872,35014,35245,35500,35600,35836,36046,36145,36206,36272,36357,36459,36562,36637,36737,36829,36934,37187,37257,37329,37410,37544,37619,37712,37779,37863,37934,38003,38123,38685,38799,38902,38990,39089,39160,39255,39320,39435,39552,39659,39801,39866,39970,40082,40233,40305,40599,40964,42696,42754,42833,42957,43137,43448,43782,43881,43961,44639,44715,44840,44997,45083,45171,45241,45328,45420,45517,45648,45790,45870,45942,46080,46244,46313,46374,46459,46535,46613,46707,46863,46984,47105,47197,47273,47355,47432,47590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5888", "endColumns": "164", "endOffsets": "6048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,14402", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,14498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4825,4931,5111,5241,5350,5521,5654,5775,6053,6231,6343,6528,6664,6824,7003,7076,7143", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "4926,5106,5236,5345,5516,5649,5770,5883,6226,6338,6523,6659,6819,6998,7071,7138,7222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "29280", "endColumns": "86", "endOffsets": "29362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,228,290,366,451,512,571,649,727,815,901,1004,1107,1195,1278,1360,1450,1554,1649,1719,1811,1900,1997,2122,2204,2296,2373,2472,2548,2648,2748,2843,2928,3043,3118,3203,3325,3440,3523,3588,4351,5022,5101,5215,5326,5381,5492,5604,5672,5773,5873,5929,6017,6067,6150,6268,6344,6421,6488,6554,6602,6690,6793,6872,6920,6969,7055,7113,7177,7378,7574,7731,7799,7888,7974,8082,8190,8300,8383,8479,8559,8673,8775,8878,8932,9077,9129,9211,9280,9350,9426,9496,9570,9668,9748,9799", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "132,223,285,361,446,507,566,644,722,810,896,999,1102,1190,1273,1355,1445,1549,1644,1714,1806,1895,1992,2117,2199,2291,2368,2467,2543,2643,2743,2838,2923,3038,3113,3198,3320,3435,3518,3583,4346,5017,5096,5210,5321,5376,5487,5599,5667,5768,5868,5924,6012,6062,6145,6263,6339,6416,6483,6549,6597,6685,6788,6867,6915,6964,7050,7108,7172,7373,7569,7726,7794,7883,7969,8077,8185,8295,8378,8474,8554,8668,8770,8873,8927,9072,9124,9206,9275,9345,9421,9491,9565,9663,9743,9794,9873"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15045,15127,15218,15280,15356,15441,15502,15810,15888,15966,16054,16286,16470,16573,16728,17088,17382,18444,18686,18781,18851,19010,19099,19196,19381,19683,19775,19852,19951,20027,20127,20227,20322,20407,20522,20678,20951,21073,21850,21933,21998,23755,24426,24505,24619,24730,24785,24896,25008,25076,25177,25277,25333,25977,26027,26110,26390,26466,26543,26610,27132,27534,27622,27725,27804,27852,28160,28246,28304,28368,28569,28765,29123,29191,30212,30298,30494,30602,31024,31107,31274,31906,32476,32578,40604,40658,40803,40969,41596,43966,44036,44112,44182,44256,44354,44434,45947", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "15122,15213,15275,15351,15436,15497,15556,15883,15961,16049,16135,16384,16568,16656,16806,17165,17467,18543,18776,18846,18938,19094,19191,19316,19458,19770,19847,19946,20022,20122,20222,20317,20402,20517,20592,20758,21068,21183,21928,21993,22756,24421,24500,24614,24725,24780,24891,25003,25071,25172,25272,25328,25416,26022,26105,26223,26461,26538,26605,26671,27175,27617,27720,27799,27847,27896,28241,28299,28363,28564,28760,28917,29186,29275,30293,30401,30597,30707,31102,31198,31349,32015,32573,32676,40653,40798,40850,41046,41660,44031,44107,44177,44251,44349,44429,44480,46021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4662,7334,7434,7617,8479,8558,13421,13513,13600,13671,13828,13909,14245,47595,47673,47742", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4657,4745,7429,7529,7699,8553,8645,13508,13595,13666,13734,13904,13989,14316,47668,47737,47859"}}]}]}