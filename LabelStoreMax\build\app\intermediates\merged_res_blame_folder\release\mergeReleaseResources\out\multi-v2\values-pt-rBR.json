{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "485,486", "startColumns": "4,4", "startOffsets": "46110,46193", "endColumns": "82,84", "endOffsets": "46188,46273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "50,73,78", "startColumns": "4,4,4", "startOffsets": "4706,7425,7804", "endColumns": "75,81,73", "endOffsets": "4777,7502,7873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,324,419,633,680,751,852,926,1146,1206,1298,1370,1427,1491,1563,1651,1971,2043,2110,2164,2221,2305,2437,2551,2609,2698,2779,2868,2934,3035,3338,3415,3493,3556,3617,3679,3740,3814,3897,3997,4098,4188,4291,4388,4463,4542,4628,4842,5053,5168,5294,5350,6002,6106,6171", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "166,319,414,628,675,746,847,921,1141,1201,1293,1365,1422,1486,1558,1646,1966,2038,2105,2159,2216,2300,2432,2546,2604,2693,2774,2863,2929,3030,3333,3410,3488,3551,3612,3674,3735,3809,3892,3992,4093,4183,4286,4383,4458,4537,4623,4837,5048,5163,5289,5345,5997,6101,6166,6221"}, "to": {"startLines": "244,245,246,248,252,253,254,255,256,257,258,274,277,279,283,284,289,295,296,304,314,317,318,319,320,321,327,330,335,337,338,339,340,343,345,346,350,354,355,357,359,371,396,397,398,399,400,418,425,426,427,428,430,431,432,449", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21099,21215,21368,21559,22598,22645,22716,22817,22891,23111,23171,25134,25403,25598,25902,25974,26336,27041,27113,27655,28586,28794,28878,29010,29124,29182,29735,29975,30401,30540,30641,30944,31021,31260,31438,31499,31806,32361,32435,32610,32816,34540,36946,37049,37146,37221,37300,38973,39653,39864,39979,40105,40228,40880,40984,42773", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "21210,21363,21458,21768,22640,22711,22812,22886,23106,23166,23258,25201,25455,25657,25969,26057,26651,27108,27175,27704,28638,28873,29005,29119,29177,29266,29811,30059,30462,30636,30939,31016,31094,31318,31494,31556,31862,32430,32513,32705,32912,34625,37044,37141,37216,37295,37381,39182,39859,39974,40100,40156,40875,40979,41044,42823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "70,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7114,7970,8069,8181", "endColumns": "114,98,111,105", "endOffsets": "7224,8064,8176,8282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "182,185,188,190,191,192,199,200,202,203,204,205,206,207,208,210,211,214,225,226,238,240,241,275,276,290,302,303,305,312,313,322,323,331,332", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16240,16490,16760,16906,16992,17063,17558,17631,17771,17833,17901,17971,18031,18092,18166,18289,18358,18583,19491,19556,20575,20732,20811,25206,25312,26656,27532,27580,27709,28455,28517,29271,29358,30064,30160", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "16301,16567,16823,16987,17058,17119,17626,17693,17828,17896,17966,18026,18087,18161,18225,18353,18416,18656,19551,19639,20650,20806,20892,25307,25398,26701,27575,27650,27768,28512,28581,29353,29442,30155,30236"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,71,72,74,85,86,146,147,149,150,153,154,158,482,483,484", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,7229,7326,7507,8427,8510,13366,13457,13623,13695,13992,14077,14413,45854,45930,45997", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,7321,7420,7588,8505,8602,13452,13539,13690,13759,14072,14162,14484,45925,45992,46105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5794", "endColumns": "144", "endOffsets": "5934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,224,307,403,489,572,654,792,887,988,1079,1203,1259,1374,1438,1609,1792,1932,2024,2130,2225,2324,2492,2602,2702,2980,3093,3232,3433,3652,3753,3876,4072,4173,4234,4300,4382,4482,4583,4660,4761,4849,4959,5172,5240,5309,5388,5515,5589,5674,5736,5815,5886,5954,6069,6156,6263,6360,6435,6517,6586,6677,6737,6842,6949,7053,7183,7243,7343,7451,7585,7656,7748,7849,7909,7972,8050,8168,8337,8606,8905,9000,9072,9157,9229,9341,9454,9541,9617,9690,9776,9864,9951,10065,10198,10266,10337,10394,10557,10627,10684,10763,10840,10920,10999,11126,11252,11366,11454,11531,11615,11686", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "127,219,302,398,484,567,649,787,882,983,1074,1198,1254,1369,1433,1604,1787,1927,2019,2125,2220,2319,2487,2597,2697,2975,3088,3227,3428,3647,3748,3871,4067,4168,4229,4295,4377,4477,4578,4655,4756,4844,4954,5167,5235,5304,5383,5510,5584,5669,5731,5810,5881,5949,6064,6151,6258,6355,6430,6512,6581,6672,6732,6837,6944,7048,7178,7238,7338,7446,7580,7651,7743,7844,7904,7967,8045,8163,8332,8601,8900,8995,9067,9152,9224,9336,9449,9536,9612,9685,9771,9859,9946,10060,10193,10261,10332,10389,10552,10622,10679,10758,10835,10915,10994,11121,11247,11361,11449,11526,11610,11681,11845"}, "to": {"startLines": "175,176,177,247,259,260,261,278,291,293,294,324,342,344,347,351,352,353,356,358,360,361,362,363,364,365,366,367,368,369,370,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,419,423,433,434,435,436,437,438,439,440,441,450,451,452,453,454,455,456,457,458,459,460,461,462,463,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15706,15783,15875,21463,23263,23349,23432,25460,26706,26849,26950,29447,31204,31323,31561,31867,32038,32221,32518,32710,32917,33012,33111,33279,33389,33489,33767,33880,34019,34220,34439,34630,34753,34949,35050,35111,35177,35259,35359,35460,35537,35638,35726,35836,36049,36117,36186,36265,36392,36466,36551,36613,36692,36763,36831,37386,37473,37580,37677,37752,37834,37903,37994,38054,38159,38266,38370,38500,38560,38660,38768,38902,39187,39498,41049,41109,41172,41250,41368,41537,41806,42105,42200,42828,42913,42985,43097,43210,43297,43373,43446,43532,43620,43707,43821,43954,44022,44172,44229,44392,44462,44519,44598,44675,44755,44834,44961,45087,45201,45289,45366,45450,45521", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "15778,15870,15953,21554,23344,23427,23509,25593,26796,26945,27036,29566,31255,31433,31620,32033,32216,32356,32605,32811,33007,33106,33274,33384,33484,33762,33875,34014,34215,34434,34535,34748,34944,35045,35106,35172,35254,35354,35455,35532,35633,35721,35831,36044,36112,36181,36260,36387,36461,36546,36608,36687,36758,36826,36941,37468,37575,37672,37747,37829,37898,37989,38049,38154,38261,38365,38495,38555,38655,38763,38897,38968,39274,39594,41104,41167,41245,41363,41532,41801,42100,42195,42267,42908,42980,43092,43205,43292,43368,43441,43527,43615,43702,43816,43949,44017,44088,44224,44387,44457,44514,44593,44670,44750,44829,44956,45082,45196,45284,45361,45445,45516,45680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,160", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,14568", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,14664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4782,4887,5035,5162,5270,5437,5567,5689,5939,6109,6217,6381,6511,6668,6825,6894,6960", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4882,5030,5157,5265,5432,5562,5684,5789,6104,6212,6376,6506,6663,6820,6889,6955,7039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "69,77,148,152,481,487,488", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7044,7717,13544,13841,45685,46278,46365", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "7109,7799,13618,13987,45849,46360,46441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,14167", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,14248"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "83,489", "startColumns": "4,4", "startOffsets": "8287,46446", "endColumns": "60,79", "endOffsets": "8343,46521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14669,14756,14864,14931,14999,15065,15139", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "14751,14859,14926,14994,15060,15134,15203"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,7593,7653,7878,8348,8607,8667,8757,8821,8892,8955,9030,9094,9148,9275,9333,9395,9449,9528,9669,9756,9832,9927,10008,10090,10229,10312,10396,10535,10622,10702,10758,10809,10875,10949,11029,11100,11183,11256,11333,11402,11476,11578,11666,11743,11836,11932,12006,12086,12183,12235,12319,12385,12472,12560,12622,12686,12749,12817,12926,13037,13141,13251,13311,13764,14253,14336,14489", "endLines": "5,33,34,35,36,37,45,46,47,75,76,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,151,156,157,159", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,7648,7712,7965,8422,8662,8752,8816,8887,8950,9025,9089,9143,9270,9328,9390,9444,9523,9664,9751,9827,9922,10003,10085,10224,10307,10391,10530,10617,10697,10753,10804,10870,10944,11024,11095,11178,11251,11328,11397,11471,11573,11661,11738,11831,11927,12001,12081,12178,12230,12314,12380,12467,12555,12617,12681,12744,12812,12921,13032,13136,13246,13306,13361,13836,14331,14408,14563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "183,193,195,196,197,201,209,212,215,219,223,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16306,17124,17266,17328,17410,17698,18230,18421,18661,18995,19347,19644", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "16383,17180,17323,17405,17469,17766,18284,18487,18731,19058,19404,19711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3222,3292,3357,4047,4712,4790,4907,5022,5077,5171,5257,5329,5419,5516,5573,5667,5718,5796,5907,5978,6048,6115,6181,6229,6313,6411,6484,6534,6581,6646,6704,6767,6959,7124,7263,7328,7414,7489,7578,7660,7737,7806,7897,7970,8075,8161,8256,8309,8425,8475,8529,8596,8668,8741,8810,8885,8975,9045,9097", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3217,3287,3352,4042,4707,4785,4902,5017,5072,5166,5252,5324,5414,5511,5568,5662,5713,5791,5902,5973,6043,6110,6176,6224,6308,6406,6479,6529,6576,6641,6699,6762,6954,7119,7258,7323,7409,7484,7573,7655,7732,7801,7892,7965,8070,8156,8251,8304,8420,8470,8524,8591,8663,8736,8805,8880,8970,9040,9092,9171"}, "to": {"startLines": "168,169,170,171,172,173,174,178,179,180,181,184,186,187,189,194,198,213,216,217,218,220,221,222,224,228,229,230,231,232,233,234,235,236,237,239,242,243,249,250,251,262,263,264,265,266,267,268,269,270,271,272,273,280,281,282,285,286,287,288,292,297,298,299,300,301,306,307,308,309,310,311,315,316,325,326,328,329,333,334,336,341,348,349,420,421,422,424,429,442,443,444,445,446,447,448,464", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15208,15282,15365,15427,15501,15584,15647,15958,16012,16088,16161,16388,16572,16674,16828,17185,17474,18492,18736,18831,18903,19063,19151,19239,19409,19716,19808,19887,19986,20060,20146,20233,20316,20399,20502,20655,20897,20999,21773,21843,21908,23514,24179,24257,24374,24489,24544,24638,24724,24796,24886,24983,25040,25662,25713,25791,26062,26133,26203,26270,26801,27180,27264,27362,27435,27485,27773,27838,27896,27959,28151,28316,28643,28708,29571,29646,29816,29898,30241,30310,30467,31099,31625,31711,39279,39332,39448,39599,40161,42272,42344,42417,42486,42561,42651,42721,44093", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "15277,15360,15422,15496,15579,15642,15701,16007,16083,16156,16235,16485,16669,16755,16901,17261,17553,18578,18826,18898,18990,19146,19234,19342,19486,19803,19882,19981,20055,20141,20228,20311,20394,20497,20570,20727,20994,21094,21838,21903,22593,24174,24252,24369,24484,24539,24633,24719,24791,24881,24978,25035,25129,25708,25786,25897,26128,26198,26265,26331,26844,27259,27357,27430,27480,27527,27833,27891,27954,28146,28311,28450,28703,28789,29641,29730,29893,29970,30305,30396,30535,31199,31706,31801,39327,39443,39493,39648,40223,42339,42412,42481,42556,42646,42716,42768,44167"}}]}]}