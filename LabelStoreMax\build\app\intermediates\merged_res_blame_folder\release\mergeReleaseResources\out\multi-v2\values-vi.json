{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8222", "endColumns": "127", "endOffsets": "8345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,197,263,352,413,479,537,613,686,749,814", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "128,192,258,347,408,474,532,608,681,744,809,874"}, "to": {"startLines": "199,209,211,212,213,217,225,228,231,235,239,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18016,18834,18982,19048,19137,19417,19953,20145,20399,20725,21065,21364", "endColumns": "77,63,65,88,60,65,57,75,72,62,64,64", "endOffsets": "18089,18893,19043,19132,19193,19478,20006,20216,20467,20783,21125,21424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,341,445,685,734,802,902,981,1207,1268,1353,1423,1476,1540,1622,1720,2069,2143,2209,2267,2325,2409,2545,2662,2724,2812,2898,2986,3059,3161,3514,3594,3674,3735,3800,3867,3932,3998,4072,4170,4270,4364,4466,4562,4634,4713,4799,5027,5258,5366,5499,5553,6376,6481,6543", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "166,336,440,680,729,797,897,976,1202,1263,1348,1418,1471,1535,1617,1715,2064,2138,2204,2262,2320,2404,2540,2657,2719,2807,2893,2981,3054,3156,3509,3589,3669,3730,3795,3862,3927,3993,4067,4165,4265,4359,4461,4557,4629,4708,4794,5022,5253,5361,5494,5548,6371,6476,6538,6596"}, "to": {"startLines": "260,261,262,264,268,269,270,271,272,273,274,290,293,295,299,300,305,311,312,320,330,334,335,336,337,338,344,347,352,354,355,356,357,360,362,363,367,371,372,374,376,388,413,414,415,416,417,435,442,443,444,445,447,448,449,466", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22838,22954,23124,23324,24408,24457,24525,24625,24704,24930,24991,26963,27226,27436,27732,27814,28183,28928,29002,29550,30508,30818,30902,31038,31155,31217,31782,32033,32499,32644,32746,33099,33179,33441,33615,33680,33992,34578,34644,34805,35010,36720,39142,39244,39340,39412,39491,41165,41854,42085,42193,42326,42450,43273,43378,45145", "endColumns": "115,169,103,239,48,67,99,78,225,60,84,69,52,63,81,97,348,73,65,57,57,83,135,116,61,87,85,87,72,101,352,79,79,60,64,66,64,65,73,97,99,93,101,95,71,78,85,227,230,107,132,53,822,104,61,57", "endOffsets": "22949,23119,23223,23559,24452,24520,24620,24699,24925,24986,25071,27028,27274,27495,27809,27907,28527,28997,29063,29603,30561,30897,31033,31150,31212,31300,31863,32116,32567,32741,33094,33174,33254,33497,33675,33742,34052,34639,34713,34898,35105,36809,39239,39335,39407,39486,39572,41388,42080,42188,42321,42375,43268,43373,43435,45198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "333", "startColumns": "4", "startOffsets": "30735", "endColumns": "82", "endOffsets": "30813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,86", "endOffsets": "138,225"}, "to": {"startLines": "502,503", "startColumns": "4,4", "startOffsets": "48561,48649", "endColumns": "87,86", "endOffsets": "48644,48731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "106,506", "startColumns": "4,4", "startOffsets": "10723,48893", "endColumns": "60,79", "endOffsets": "10779,48968"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9512,10387,10498,10612", "endColumns": "116,110,113,110", "endOffsets": "9624,10493,10607,10718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,209", "endColumns": "78,74,77", "endOffsets": "129,204,282"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4607,9835,10215", "endColumns": "78,74,77", "endOffsets": "4681,9905,10288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3499,3601,3700,3800,3903,4016,16838", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3494,3596,3695,3795,3898,4011,4127,16934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7206,7308,7470,7595,7704,7869,7999,8118,8350,8523,8630,8787,8917,9076,9225,9293,9357", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "7303,7465,7590,7699,7864,7994,8113,8217,8518,8625,8782,8912,9071,9220,9288,9352,9435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,16446", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,16526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,339,482,651,731", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "172,259,334,477,646,726,803"}, "to": {"startLines": "92,100,171,175,498,504,505", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9440,10128,15829,16122,48135,48736,48816", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "9507,10210,15899,16260,48299,48811,48888"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,192,254,324,403,459,518,570,655,736,818,916,1014,1100,1180,1264,1345,1441,1534,1604,1694,1777,1864,1971,2056,2141,2222,2323,2406,2499,2588,2673,2757,2865,2945,3031,3129,3220,3295,3356,4064,4748,4823,4938,5043,5098,5189,5278,5345,5442,5539,5595,5681,5727,5811,5913,5977,6051,6118,6184,6230,6314,6412,6493,6538,6582,6645,6707,6769,6967,7147,7275,7344,7444,7516,7611,7695,7776,7859,7955,8027,8146,8225,8321,8376,8485,8531,8584,8654,8715,8783,8857,8936,9028,9094,9145", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "114,187,249,319,398,454,513,565,650,731,813,911,1009,1095,1175,1259,1340,1436,1529,1599,1689,1772,1859,1966,2051,2136,2217,2318,2401,2494,2583,2668,2752,2860,2940,3026,3124,3215,3290,3351,4059,4743,4818,4933,5038,5093,5184,5273,5340,5437,5534,5590,5676,5722,5806,5908,5972,6046,6113,6179,6225,6309,6407,6488,6533,6577,6640,6702,6764,6962,7142,7270,7339,7439,7511,7606,7690,7771,7854,7950,8022,8141,8220,8316,8371,8480,8526,8579,8649,8710,8778,8852,8931,9023,9089,9140,9226"}, "to": {"startLines": "184,185,186,187,188,189,190,194,195,196,197,200,202,203,205,210,214,229,232,233,234,236,237,238,240,244,245,246,247,248,249,250,251,252,253,255,258,259,265,266,267,278,279,280,281,282,283,284,285,286,287,288,289,296,297,298,301,302,303,304,308,313,314,315,316,317,322,323,324,325,326,327,331,332,342,343,345,346,350,351,353,358,365,366,437,438,439,441,446,459,460,461,462,463,464,465,481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16939,17003,17076,17138,17208,17287,17343,17651,17703,17788,17869,18094,18270,18368,18530,18898,19198,20221,20472,20565,20635,20788,20871,20958,21130,21429,21514,21595,21696,21779,21872,21961,22046,22130,22238,22397,22649,22747,23564,23639,23700,25346,26030,26105,26220,26325,26380,26471,26560,26627,26724,26821,26877,27500,27546,27630,27912,27976,28050,28117,28679,29068,29152,29250,29331,29376,29675,29738,29800,29862,30060,30240,30566,30635,31615,31687,31868,31952,32320,32403,32572,33259,33817,33896,41486,41541,41650,41801,42380,44654,44715,44783,44857,44936,45028,45094,46516", "endColumns": "63,72,61,69,78,55,58,51,84,80,81,97,97,85,79,83,80,95,92,69,89,82,86,106,84,84,80,100,82,92,88,84,83,107,79,85,97,90,74,60,707,683,74,114,104,54,90,88,66,96,96,55,85,45,83,101,63,73,66,65,45,83,97,80,44,43,62,61,61,197,179,127,68,99,71,94,83,80,82,95,71,118,78,95,54,108,45,52,69,60,67,73,78,91,65,50,85", "endOffsets": "16998,17071,17133,17203,17282,17338,17397,17698,17783,17864,17946,18187,18363,18449,18605,18977,19274,20312,20560,20630,20720,20866,20953,21060,21210,21509,21590,21691,21774,21867,21956,22041,22125,22233,22313,22478,22742,22833,23634,23695,24403,26025,26100,26215,26320,26375,26466,26555,26622,26719,26816,26872,26958,27541,27625,27727,27971,28045,28112,28178,28720,29147,29245,29326,29371,29415,29733,29795,29857,30055,30235,30363,30630,30730,31682,31777,31947,32028,32398,32494,32639,33373,33891,33987,41536,41645,41691,41849,42445,44710,44778,44852,44931,45023,45089,45140,46597"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,210,341,481,591,676,762,838,924,1016,1129,1240,1333,1426,1534,1654,1733,1817,2001,2097,2203,2321,2429", "endColumns": "154,130,139,109,84,85,75,85,91,112,110,92,92,107,119,78,83,183,95,105,117,107,145", "endOffsets": "205,336,476,586,671,757,833,919,1011,1124,1235,1328,1421,1529,1649,1728,1812,1996,2092,2198,2316,2424,2570"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4686,4841,4972,5112,5222,5307,5393,5469,5555,5647,5760,5871,5964,6057,6165,6285,6364,6448,6632,6728,6834,6952,7060", "endColumns": "154,130,139,109,84,85,75,85,91,112,110,92,92,107,119,78,83,183,95,105,117,107,145", "endOffsets": "4836,4967,5107,5217,5302,5388,5464,5550,5642,5755,5866,5959,6052,6160,6280,6359,6443,6627,6723,6829,6947,7055,7201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,499,500,501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4521,9629,9735,9910,10858,10943,15654,15748,15904,15974,16265,16355,16692,48304,48381,48447", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "4516,4602,9730,9830,9997,10938,11031,15743,15824,15969,16039,16350,16441,16759,48376,48442,48556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,304,400,495,583,670,827,923,1029,1126,1257,1320,1433,1503,1680,1882,2024,2111,2218,2309,2409,2582,2693,2801,3061,3182,3319,3518,3727,3828,3941,4138,4236,4295,4360,4446,4539,4642,4714,4820,4906,5025,5233,5298,5363,5445,5561,5643,5733,5798,5874,5950,6024,6156,6244,6351,6448,6516,6591,6656,6749,6806,6915,7023,7147,7272,7333,7431,7542,7669,7744,7837,7942,8006,8063,8155,8258,8437,8693,8975,9084,9156,9243,9318,9454,9578,9674,9745,9818,9899,9979,10078,10200,10330,10403,10469,10523,10695,10766,10825,10906,10986,11064,11148,11274,11402,11512,11599,11673,11769,11836", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "127,217,299,395,490,578,665,822,918,1024,1121,1252,1315,1428,1498,1675,1877,2019,2106,2213,2304,2404,2577,2688,2796,3056,3177,3314,3513,3722,3823,3936,4133,4231,4290,4355,4441,4534,4637,4709,4815,4901,5020,5228,5293,5358,5440,5556,5638,5728,5793,5869,5945,6019,6151,6239,6346,6443,6511,6586,6651,6744,6801,6910,7018,7142,7267,7328,7426,7537,7664,7739,7832,7937,8001,8058,8150,8253,8432,8688,8970,9079,9151,9238,9313,9449,9573,9669,9740,9813,9894,9974,10073,10195,10325,10398,10464,10518,10690,10761,10820,10901,10981,11059,11143,11269,11397,11507,11594,11668,11764,11831,11997"}, "to": {"startLines": "191,192,193,263,275,276,277,294,307,309,310,341,359,361,364,368,369,370,373,375,377,378,379,380,381,382,383,384,385,386,387,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,436,440,450,451,452,453,454,455,456,457,458,467,468,469,470,471,472,473,474,475,476,477,478,479,480,482,483,484,485,486,487,488,489,490,491,492,493,494,495,496,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17402,17479,17569,23228,25076,25171,25259,27279,28583,28725,28831,31484,33378,33502,33747,34057,34234,34436,34718,34903,35110,35201,35301,35474,35585,35693,35953,36074,36211,36410,36619,36814,36927,37124,37222,37281,37346,37432,37525,37628,37700,37806,37892,38011,38219,38284,38349,38431,38547,38629,38719,38784,38860,38936,39010,39577,39665,39772,39869,39937,40012,40077,40170,40227,40336,40444,40568,40693,40754,40852,40963,41090,41393,41696,43440,43504,43561,43653,43756,43935,44191,44473,44582,45203,45290,45365,45501,45625,45721,45792,45865,45946,46026,46125,46247,46377,46450,46602,46656,46828,46899,46958,47039,47119,47197,47281,47407,47535,47645,47732,47806,47902,47969", "endColumns": "76,89,81,95,94,87,86,156,95,105,96,130,62,112,69,176,201,141,86,106,90,99,172,110,107,259,120,136,198,208,100,112,196,97,58,64,85,92,102,71,105,85,118,207,64,64,81,115,81,89,64,75,75,73,131,87,106,96,67,74,64,92,56,108,107,123,124,60,97,110,126,74,92,104,63,56,91,102,178,255,281,108,71,86,74,135,123,95,70,72,80,79,98,121,129,72,65,53,171,70,58,80,79,77,83,125,127,109,86,73,95,66,165", "endOffsets": "17474,17564,17646,23319,25166,25254,25341,27431,28674,28826,28923,31610,33436,33610,33812,34229,34431,34573,34800,35005,35196,35296,35469,35580,35688,35948,36069,36206,36405,36614,36715,36922,37119,37217,37276,37341,37427,37520,37623,37695,37801,37887,38006,38214,38279,38344,38426,38542,38624,38714,38779,38855,38931,39005,39137,39660,39767,39864,39932,40007,40072,40165,40222,40331,40439,40563,40688,40749,40847,40958,41085,41160,41481,41796,43499,43556,43648,43751,43930,44186,44468,44577,44649,45285,45360,45496,45620,45716,45787,45860,45941,46021,46120,46242,46372,46445,46511,46651,46823,46894,46953,47034,47114,47192,47276,47402,47530,47640,47727,47801,47897,47964,48130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,274,364,434,498,567,636,701,772,842,905,965,1033,1106,1179,1240,1322,1386,1471,1550,1625,1716,1823,1909,1960,2007,2090,2157,2228,2297,2391,2476,2577", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "115,193,269,359,429,493,562,631,696,767,837,900,960,1028,1101,1174,1235,1317,1381,1466,1545,1620,1711,1818,1904,1955,2002,2085,2152,2223,2292,2386,2471,2572,2670"}, "to": {"startLines": "198,201,204,206,207,208,215,216,218,219,220,221,222,223,224,226,227,230,241,242,254,256,257,291,292,306,318,319,321,328,329,339,340,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17951,18192,18454,18610,18700,18770,19279,19348,19483,19548,19619,19689,19752,19812,19880,20011,20084,20317,21215,21279,22318,22483,22558,27033,27140,28532,29420,29467,29608,30368,30439,31305,31399,32121,32222", "endColumns": "64,77,75,89,69,63,68,68,64,70,69,62,59,67,72,72,60,81,63,84,78,74,90,106,85,50,46,82,66,70,68,93,84,100,97", "endOffsets": "18011,18265,18525,18695,18765,18829,19343,19412,19543,19614,19684,19747,19807,19875,19948,20079,20140,20394,21274,21359,22392,22553,22644,27135,27221,28578,29462,29545,29670,30434,30503,31394,31479,32217,32315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,3322,4132,4231,4345,10002,10065,10293,10784,11036,11095,11181,11243,11304,11362,11426,11487,11541,11658,11715,11775,11829,11904,12031,12115,12195,12290,12374,12452,12582,12666,12744,12878,12969,13050,13101,13152,13218,13286,13362,13433,13513,13592,13667,13740,13816,13922,14011,14088,14179,14273,14347,14417,14510,14559,14640,14706,14791,14877,14939,15003,15066,15137,15236,15341,15439,15544,15599,16044,16531,16613,16764", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3082,3159,3237,3317,3397,4226,4340,4420,10060,10123,10382,10853,11090,11176,11238,11299,11357,11421,11482,11536,11653,11710,11770,11824,11899,12026,12110,12190,12285,12369,12447,12577,12661,12739,12873,12964,13045,13096,13147,13213,13281,13357,13428,13508,13587,13662,13735,13811,13917,14006,14083,14174,14268,14342,14412,14505,14554,14635,14701,14786,14872,14934,14998,15061,15132,15231,15336,15434,15539,15594,15649,16117,16608,16687,16833"}}]}]}