  Context android.content  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  packageManager android.content.Context  packageName android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  PackageInfo android.content.pm  PackageManager android.content.pm  	Signature android.content.pm  SigningInfo android.content.pm  	loadLabel "android.content.pm.ApplicationInfo  getINITIATINGPackageName $android.content.pm.InstallSourceInfo  getInitiatingPackageName $android.content.pm.InstallSourceInfo  initiatingPackageName $android.content.pm.InstallSourceInfo  setInitiatingPackageName $android.content.pm.InstallSourceInfo  applicationInfo android.content.pm.PackageInfo  firstInstallTime android.content.pm.PackageInfo  getLONGVersionCode android.content.pm.PackageInfo  getLongVersionCode android.content.pm.PackageInfo  lastUpdateTime android.content.pm.PackageInfo  longVersionCode android.content.pm.PackageInfo  setLongVersionCode android.content.pm.PackageInfo  
signatures android.content.pm.PackageInfo  signingInfo android.content.pm.PackageInfo  versionCode android.content.pm.PackageInfo  versionName android.content.pm.PackageInfo  	loadLabel "android.content.pm.PackageItemInfo  GET_SIGNATURES !android.content.pm.PackageManager  GET_SIGNING_CERTIFICATES !android.content.pm.PackageManager  NameNotFoundException !android.content.pm.PackageManager  getInstallSourceInfo !android.content.pm.PackageManager  getInstallerPackageName !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  message 7android.content.pm.PackageManager.NameNotFoundException  equals android.content.pm.Signature  toByteArray android.content.pm.Signature  apkContentsSigners android.content.pm.SigningInfo  getAPKContentsSigners android.content.pm.SigningInfo  getApkContentsSigners android.content.pm.SigningInfo  getSIGNINGCertificateHistory android.content.pm.SigningInfo  getSigningCertificateHistory android.content.pm.SigningInfo  hasMultipleSigners android.content.pm.SigningInfo  setApkContentsSigners android.content.pm.SigningInfo  setSigningCertificateHistory android.content.pm.SigningInfo  signingCertificateHistory android.content.pm.SigningInfo  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  Build %dev.fluttercommunity.plus.packageinfo  	ByteArray %dev.fluttercommunity.plus.packageinfo  CHANNEL_NAME %dev.fluttercommunity.plus.packageinfo  	CharArray %dev.fluttercommunity.plus.packageinfo  HashMap %dev.fluttercommunity.plus.packageinfo  Int %dev.fluttercommunity.plus.packageinfo  Long %dev.fluttercommunity.plus.packageinfo  
MessageDigest %dev.fluttercommunity.plus.packageinfo  
MethodChannel %dev.fluttercommunity.plus.packageinfo  NoSuchAlgorithmException %dev.fluttercommunity.plus.packageinfo  PackageInfoPlugin %dev.fluttercommunity.plus.packageinfo  PackageManager %dev.fluttercommunity.plus.packageinfo  String %dev.fluttercommunity.plus.packageinfo  Suppress %dev.fluttercommunity.plus.packageinfo  Throws %dev.fluttercommunity.plus.packageinfo  also %dev.fluttercommunity.plus.packageinfo  applicationContext %dev.fluttercommunity.plus.packageinfo  apply %dev.fluttercommunity.plus.packageinfo  charArrayOf %dev.fluttercommunity.plus.packageinfo  first %dev.fluttercommunity.plus.packageinfo  getLongVersionCode %dev.fluttercommunity.plus.packageinfo  indices %dev.fluttercommunity.plus.packageinfo  invoke %dev.fluttercommunity.plus.packageinfo  
isNullOrEmpty %dev.fluttercommunity.plus.packageinfo  Build 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  	ByteArray 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  CHANNEL_NAME 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  	CharArray 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Context 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  FlutterPluginBinding 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  HashMap 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Int 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Long 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
MessageDigest 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
MethodCall 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
MethodChannel 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  NoSuchAlgorithmException 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  PackageInfo 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  PackageManager 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  String 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Suppress 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Throws 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  also 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  applicationContext 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  apply 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
bytesToHex 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  charArrayOf 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  first 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getALSO 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getAPPLY 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getAlso 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getApply 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getBuildSignature 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getCHARArrayOf 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getCharArrayOf 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getFIRST 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getFirst 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getISNullOrEmpty 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getInstallerPackageName 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getIsNullOrEmpty 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  getLongVersionCode 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  indices 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  invoke 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
isNullOrEmpty 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  
methodChannel 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  signatureToSha256 7dev.fluttercommunity.plus.packageinfo.PackageInfoPlugin  Build Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  	ByteArray Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  CHANNEL_NAME Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  	CharArray Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  Context Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  FlutterPluginBinding Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  HashMap Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  Int Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  Long Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
MessageDigest Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
MethodCall Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
MethodChannel Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  NoSuchAlgorithmException Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  PackageInfo Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  PackageManager Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  String Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  Suppress Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  Throws Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  also Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  applicationContext Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  apply Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  charArrayOf Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  first Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getALSO Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getAPPLY Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getAlso Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getApply Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getCHARArrayOf Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getCharArrayOf Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getFIRST Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getFirst Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getISNullOrEmpty Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getIsNullOrEmpty Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  getLongVersionCode Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  indices Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  invoke Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
isNullOrEmpty Adev.fluttercommunity.plus.packageinfo.PackageInfoPlugin.Companion  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Build 	java.lang  CHANNEL_NAME 	java.lang  	CharArray 	java.lang  HashMap 	java.lang  
MessageDigest 	java.lang  
MethodChannel 	java.lang  NoSuchAlgorithmException 	java.lang  PackageManager 	java.lang  String 	java.lang  also 	java.lang  applicationContext 	java.lang  apply 	java.lang  charArrayOf 	java.lang  first 	java.lang  getLongVersionCode 	java.lang  indices 	java.lang  invoke 	java.lang  
isNullOrEmpty 	java.lang  
MessageDigest 
java.security  NoSuchAlgorithmException 
java.security  digest java.security.MessageDigest  getInstance java.security.MessageDigest  update java.security.MessageDigest  digest java.security.MessageDigestSpi  update java.security.MessageDigestSpi  HashMap 	java.util  also java.util.AbstractMap  apply java.util.AbstractMap  put java.util.AbstractMap  also java.util.HashMap  applicationContext java.util.HashMap  apply java.util.HashMap  getALSO java.util.HashMap  getAPPLICATIONContext java.util.HashMap  getAPPLY java.util.HashMap  getAlso java.util.HashMap  getApplicationContext java.util.HashMap  getApply java.util.HashMap  getGETLongVersionCode java.util.HashMap  getGetLongVersionCode java.util.HashMap  getLongVersionCode java.util.HashMap  put java.util.HashMap  Any kotlin  Array kotlin  Boolean kotlin  Build kotlin  Byte kotlin  	ByteArray kotlin  CHANNEL_NAME kotlin  Char kotlin  	CharArray kotlin  	Function1 kotlin  HashMap kotlin  Int kotlin  Long kotlin  
MessageDigest kotlin  
MethodChannel kotlin  NoSuchAlgorithmException kotlin  Nothing kotlin  PackageManager kotlin  String kotlin  Suppress kotlin  Throws kotlin  also kotlin  applicationContext kotlin  apply kotlin  charArrayOf kotlin  first kotlin  getLongVersionCode kotlin  indices kotlin  invoke kotlin  
isNullOrEmpty kotlin  getFIRST kotlin.Array  getFirst kotlin.Array  getISNullOrEmpty kotlin.Array  getIsNullOrEmpty kotlin.Array  
isNullOrEmpty kotlin.Array  
getINDICES kotlin.ByteArray  
getIndices kotlin.ByteArray  Build kotlin.annotation  CHANNEL_NAME kotlin.annotation  	CharArray kotlin.annotation  HashMap kotlin.annotation  
MessageDigest kotlin.annotation  
MethodChannel kotlin.annotation  NoSuchAlgorithmException kotlin.annotation  PackageManager kotlin.annotation  String kotlin.annotation  Throws kotlin.annotation  also kotlin.annotation  applicationContext kotlin.annotation  apply kotlin.annotation  charArrayOf kotlin.annotation  first kotlin.annotation  getLongVersionCode kotlin.annotation  indices kotlin.annotation  invoke kotlin.annotation  
isNullOrEmpty kotlin.annotation  Build kotlin.collections  CHANNEL_NAME kotlin.collections  	CharArray kotlin.collections  HashMap kotlin.collections  
MessageDigest kotlin.collections  
MethodChannel kotlin.collections  NoSuchAlgorithmException kotlin.collections  PackageManager kotlin.collections  String kotlin.collections  Throws kotlin.collections  also kotlin.collections  applicationContext kotlin.collections  apply kotlin.collections  charArrayOf kotlin.collections  first kotlin.collections  getLongVersionCode kotlin.collections  indices kotlin.collections  invoke kotlin.collections  
isNullOrEmpty kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  Build kotlin.comparisons  CHANNEL_NAME kotlin.comparisons  	CharArray kotlin.comparisons  HashMap kotlin.comparisons  
MessageDigest kotlin.comparisons  
MethodChannel kotlin.comparisons  NoSuchAlgorithmException kotlin.comparisons  PackageManager kotlin.comparisons  String kotlin.comparisons  Throws kotlin.comparisons  also kotlin.comparisons  applicationContext kotlin.comparisons  apply kotlin.comparisons  charArrayOf kotlin.comparisons  first kotlin.comparisons  getLongVersionCode kotlin.comparisons  indices kotlin.comparisons  invoke kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  Build 	kotlin.io  CHANNEL_NAME 	kotlin.io  	CharArray 	kotlin.io  HashMap 	kotlin.io  
MessageDigest 	kotlin.io  
MethodChannel 	kotlin.io  NoSuchAlgorithmException 	kotlin.io  PackageManager 	kotlin.io  String 	kotlin.io  Throws 	kotlin.io  also 	kotlin.io  applicationContext 	kotlin.io  apply 	kotlin.io  charArrayOf 	kotlin.io  first 	kotlin.io  getLongVersionCode 	kotlin.io  indices 	kotlin.io  invoke 	kotlin.io  
isNullOrEmpty 	kotlin.io  Build 
kotlin.jvm  CHANNEL_NAME 
kotlin.jvm  	CharArray 
kotlin.jvm  HashMap 
kotlin.jvm  
MessageDigest 
kotlin.jvm  
MethodChannel 
kotlin.jvm  NoSuchAlgorithmException 
kotlin.jvm  PackageManager 
kotlin.jvm  String 
kotlin.jvm  Throws 
kotlin.jvm  also 
kotlin.jvm  applicationContext 
kotlin.jvm  apply 
kotlin.jvm  charArrayOf 
kotlin.jvm  first 
kotlin.jvm  getLongVersionCode 
kotlin.jvm  indices 
kotlin.jvm  invoke 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  Build 
kotlin.ranges  CHANNEL_NAME 
kotlin.ranges  	CharArray 
kotlin.ranges  HashMap 
kotlin.ranges  IntRange 
kotlin.ranges  
MessageDigest 
kotlin.ranges  
MethodChannel 
kotlin.ranges  NoSuchAlgorithmException 
kotlin.ranges  PackageManager 
kotlin.ranges  String 
kotlin.ranges  Throws 
kotlin.ranges  also 
kotlin.ranges  applicationContext 
kotlin.ranges  apply 
kotlin.ranges  charArrayOf 
kotlin.ranges  first 
kotlin.ranges  getLongVersionCode 
kotlin.ranges  indices 
kotlin.ranges  invoke 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  Build kotlin.sequences  CHANNEL_NAME kotlin.sequences  	CharArray kotlin.sequences  HashMap kotlin.sequences  
MessageDigest kotlin.sequences  
MethodChannel kotlin.sequences  NoSuchAlgorithmException kotlin.sequences  PackageManager kotlin.sequences  String kotlin.sequences  Throws kotlin.sequences  also kotlin.sequences  applicationContext kotlin.sequences  apply kotlin.sequences  charArrayOf kotlin.sequences  first kotlin.sequences  getLongVersionCode kotlin.sequences  indices kotlin.sequences  invoke kotlin.sequences  
isNullOrEmpty kotlin.sequences  Build kotlin.text  CHANNEL_NAME kotlin.text  	CharArray kotlin.text  HashMap kotlin.text  
MessageDigest kotlin.text  
MethodChannel kotlin.text  NoSuchAlgorithmException kotlin.text  PackageManager kotlin.text  String kotlin.text  Throws kotlin.text  also kotlin.text  applicationContext kotlin.text  apply kotlin.text  charArrayOf kotlin.text  first kotlin.text  getLongVersionCode kotlin.text  indices kotlin.text  invoke kotlin.text  
isNullOrEmpty kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             