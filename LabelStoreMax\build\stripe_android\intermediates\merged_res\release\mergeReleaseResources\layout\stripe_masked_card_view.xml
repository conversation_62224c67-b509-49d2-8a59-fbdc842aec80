<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/brand_icon"
        android:layout_height="@dimen/stripe_masked_card_icon_height"
        android:layout_width="@dimen/stripe_masked_card_icon_width"
        android:layout_gravity="center_vertical|start"
        app:srcCompat="@drawable/stripe_ic_unknown" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/details"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="@dimen/stripe_list_row_start_padding"
        android:textAppearance="@android:style/TextAppearance.DeviceDefault.Medium"
        android:layout_gravity="center_vertical|start" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/check_icon"
        android:layout_width="@dimen/stripe_masked_card_icon_width"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="invisible"
        app:srcCompat="@drawable/stripe_ic_checkmark" />

</merge>
