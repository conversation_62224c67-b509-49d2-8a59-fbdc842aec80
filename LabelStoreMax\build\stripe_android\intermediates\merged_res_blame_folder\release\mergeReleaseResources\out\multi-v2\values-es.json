{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,13940", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,14018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7135,7915,8016,8131", "endColumns": "106,100,114,104", "endOffsets": "7237,8011,8126,8231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,327,422,628,674,746,849,924,1165,1227,1320,1396,1453,1517,1591,1681,1978,2051,2118,2186,2245,2333,2468,2580,2638,2727,2809,2912,2982,3082,3403,3480,3558,3639,3714,3776,3837,3909,3995,4105,4211,4301,4410,4504,4580,4659,4745,4927,5132,5241,5367,5429,6200,6311,6376", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "164,322,417,623,669,741,844,919,1160,1222,1315,1391,1448,1512,1586,1676,1973,2046,2113,2181,2240,2328,2463,2575,2633,2722,2804,2907,2977,3077,3398,3475,3553,3634,3709,3771,3832,3904,3990,4100,4206,4296,4405,4499,4575,4654,4740,4922,5127,5236,5362,5424,6195,6306,6371,6429"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21074,21188,21346,21535,22596,22642,22714,22817,22892,23133,23195,25252,25536,25727,26032,26106,26480,27155,27228,27799,28755,29057,29145,29280,29392,29450,30046,30316,30797,30948,31048,31369,31446,31692,31895,31970,32278,32856,32928,33113,33323,35093,37540,37649,37743,37819,37898,39613,40292,40497,40606,40732,40870,41641,41752,43564", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "21183,21341,21436,21736,22637,22709,22812,22887,23128,23190,23283,25323,25588,25786,26101,26191,26772,27223,27290,27862,28809,29140,29275,29387,29445,29534,30123,30414,30862,31043,31364,31441,31519,31768,31965,32027,32334,32923,33009,33218,33424,35178,37644,37738,37814,37893,37979,39790,40492,40601,40727,40789,41636,41747,41812,43617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "46820,46920", "endColumns": "99,101", "endOffsets": "46915,47017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8236,47022", "endColumns": "60,77", "endOffsets": "8292,47095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4798,4906,5069,5200,5308,5469,5602,5724,5994,6186,6295,6460,6592,6757,6914,6981,7050", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4901,5064,5195,5303,5464,5597,5719,5829,6181,6290,6455,6587,6752,6909,6976,7045,7130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4638,7242,7340,7526,8378,8457,13358,13450,13537,13610,13763,13849,14182,46547,46629,46699", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "4633,4715,7335,7438,7610,8452,8545,13445,13532,13605,13675,13844,13935,14254,46624,46694,46815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,14336", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,14432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,352,437,497,556,614,708,789,883,996,1109,1197,1275,1359,1443,1544,1639,1711,1803,1891,1979,2087,2169,2261,2340,2439,2517,2615,2709,2800,2898,3016,3092,3184,3287,3383,3459,3527,4238,4929,5008,5138,5245,5300,5398,5490,5575,5685,5798,5857,5943,5994,6074,6184,6262,6335,6402,6468,6516,6597,6703,6776,6822,6872,6943,7001,7065,7265,7412,7557,7629,7715,7799,7894,7984,8082,8160,8253,8334,8434,8519,8616,8671,8791,8842,8904,8980,9050,9129,9199,9270,9364,9439,9492", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "125,209,271,347,432,492,551,609,703,784,878,991,1104,1192,1270,1354,1438,1539,1634,1706,1798,1886,1974,2082,2164,2256,2335,2434,2512,2610,2704,2795,2893,3011,3087,3179,3282,3378,3454,3522,4233,4924,5003,5133,5240,5295,5393,5485,5570,5680,5793,5852,5938,5989,6069,6179,6257,6330,6397,6463,6511,6592,6698,6771,6817,6867,6938,6996,7060,7260,7407,7552,7624,7710,7794,7889,7979,8077,8155,8248,8329,8429,8514,8611,8666,8786,8837,8899,8975,9045,9124,9194,9265,9359,9434,9487,9559"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14977,15052,15136,15198,15274,15359,15419,15721,15779,15873,15954,16208,16414,16527,16683,17048,17339,18372,18623,18718,18790,18950,19038,19126,19296,19604,19696,19775,19874,19952,20050,20144,20235,20333,20451,20604,20875,20978,21741,21817,21885,23547,24238,24317,24447,24554,24609,24707,24799,24884,24994,25107,25166,25791,25842,25922,26196,26274,26347,26414,26921,27295,27376,27482,27555,27601,27931,28002,28060,28124,28324,28471,28814,28886,29867,29951,30128,30218,30626,30704,30867,31524,32096,32181,39894,39949,40069,40230,40794,43052,43122,43201,43271,43342,43436,43511,44932", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "15047,15131,15193,15269,15354,15414,15473,15774,15868,15949,16043,16316,16522,16610,16756,17127,17418,18468,18713,18785,18877,19033,19121,19229,19373,19691,19770,19869,19947,20045,20139,20230,20328,20446,20522,20691,20973,21069,21812,21880,22591,24233,24312,24442,24549,24604,24702,24794,24879,24989,25102,25161,25247,25837,25917,26027,26269,26342,26409,26475,26964,27371,27477,27550,27596,27646,27997,28055,28119,28319,28466,28611,28881,28967,29946,30041,30213,30311,30699,30792,30943,31619,32176,32273,39944,40064,40115,40287,40865,43117,43196,43266,43337,43431,43506,43559,44999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,219,298,392,484,568,651,785,879,973,1065,1193,1261,1383,1447,1619,1808,1964,2063,2163,2260,2360,2561,2666,2767,3038,3153,3296,3503,3729,3827,3954,4147,4241,4302,4368,4449,4555,4664,4746,4846,4940,5045,5263,5331,5402,5478,5603,5675,5759,5834,5928,5999,6067,6184,6272,6392,6491,6568,6663,6732,6820,6881,6987,7098,7198,7337,7406,7503,7607,7742,7813,7912,8022,8080,8136,8222,8320,8498,8780,9078,9176,9257,9347,9414,9531,9637,9722,9810,9883,9966,10055,10152,10280,10425,10496,10567,10620,10784,10849,10914,10995,11068,11148,11227,11372,11494,11604,11694,11782,11867,11943", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "125,214,293,387,479,563,646,780,874,968,1060,1188,1256,1378,1442,1614,1803,1959,2058,2158,2255,2355,2556,2661,2762,3033,3148,3291,3498,3724,3822,3949,4142,4236,4297,4363,4444,4550,4659,4741,4841,4935,5040,5258,5326,5397,5473,5598,5670,5754,5829,5923,5994,6062,6179,6267,6387,6486,6563,6658,6727,6815,6876,6982,7093,7193,7332,7401,7498,7602,7737,7808,7907,8017,8075,8131,8217,8315,8493,8775,9073,9171,9252,9342,9409,9526,9632,9717,9805,9878,9961,10050,10147,10275,10420,10491,10562,10615,10779,10844,10909,10990,11063,11143,11222,11367,11489,11599,11689,11777,11862,11938,12105"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15478,15553,15642,21441,23288,23380,23464,25593,26827,26969,27063,29739,31624,31773,32032,32339,32511,32700,33014,33223,33429,33526,33626,33827,33932,34033,34304,34419,34562,34769,34995,35183,35310,35503,35597,35658,35724,35805,35911,36020,36102,36202,36296,36401,36619,36687,36758,36834,36959,37031,37115,37190,37284,37355,37423,37984,38072,38192,38291,38368,38463,38532,38620,38681,38787,38898,38998,39137,39206,39303,39407,39542,39795,40120,41817,41875,41931,42017,42115,42293,42575,42873,42971,43622,43712,43779,43896,44002,44087,44175,44248,44331,44420,44517,44645,44790,44861,45004,45057,45221,45286,45351,45432,45505,45585,45664,45809,45931,46041,46131,46219,46304,46380", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "15548,15637,15716,21530,23375,23459,23542,25722,26916,27058,27150,29862,31687,31890,32091,32506,32695,32851,33108,33318,33521,33621,33822,33927,34028,34299,34414,34557,34764,34990,35088,35305,35498,35592,35653,35719,35800,35906,36015,36097,36197,36291,36396,36614,36682,36753,36829,36954,37026,37110,37185,37279,37350,37418,37535,38067,38187,38286,38363,38458,38527,38615,38676,38782,38893,38993,39132,39201,39298,39402,39537,39608,39889,40225,41870,41926,42012,42110,42288,42570,42868,42966,43047,43707,43774,43891,43997,44082,44170,44243,44326,44415,44512,44640,44785,44856,44927,45052,45216,45281,45346,45427,45500,45580,45659,45804,45926,46036,46126,46214,46299,46375,46542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "28972", "endColumns": "84", "endOffsets": "29052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5834", "endColumns": "159", "endOffsets": "5989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,246,312,379,445,527", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "137,241,307,374,440,522,590"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14437,14524,14628,14694,14761,14827,14909", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "14519,14623,14689,14756,14822,14904,14972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,209,271,352,416,491,552,633,708,776,838", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "143,204,266,347,411,486,547,628,703,771,833,905"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16115,16987,17132,17194,17275,17563,18098,18291,18548,18882,19234,19532", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "16203,17043,17189,17270,17334,17633,18154,18367,18618,18945,19291,19599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,77", "endOffsets": "128,211,289"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4720,7443,7742", "endColumns": "77,82,77", "endOffsets": "4793,7521,7815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,7615,7677,7820,8297,8550,8613,8702,8766,8835,8898,8972,9036,9093,9211,9269,9331,9388,9468,9607,9696,9772,9867,9948,10030,10171,10252,10332,10483,10573,10653,10709,10765,10831,10910,10992,11063,11152,11226,11303,11373,11452,11552,11636,11720,11812,11912,11986,12067,12169,12222,12307,12374,12467,12556,12618,12682,12745,12813,12926,13033,13137,13238,13298,13680,14023,14106,14259", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,7672,7737,7910,8373,8608,8697,8761,8830,8893,8967,9031,9088,9206,9264,9326,9383,9463,9602,9691,9767,9862,9943,10025,10166,10247,10327,10478,10568,10648,10704,10760,10826,10905,10987,11058,11147,11221,11298,11368,11447,11547,11631,11715,11807,11907,11981,12062,12164,12217,12302,12369,12462,12551,12613,12677,12740,12808,12921,13028,13132,13233,13293,13353,13758,14101,14177,14331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,215,283,376,448,509,582,649,711,779,850,910,971,1045,1109,1178,1241,1316,1389,1470,1547,1633,1726,1843,1934,1984,2044,2132,2196,2266,2335,2446,2535,2639", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "117,210,278,371,443,504,577,644,706,774,845,905,966,1040,1104,1173,1236,1311,1384,1465,1542,1628,1721,1838,1929,1979,2039,2127,2191,2261,2330,2441,2530,2634,2737"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16048,16321,16615,16761,16854,16926,17423,17496,17638,17700,17768,17839,17899,17960,18034,18159,18228,18473,19378,19451,20527,20696,20782,25328,25445,26777,27651,27711,27867,28616,28686,29539,29650,30419,30523", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "16110,16409,16678,16849,16921,16982,17491,17558,17695,17763,17834,17894,17955,18029,18093,18223,18286,18543,19446,19527,20599,20777,20870,25440,25531,26822,27706,27794,27926,28681,28750,29645,29734,30518,30621"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,13940", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,14018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7135,7915,8016,8131", "endColumns": "106,100,114,104", "endOffsets": "7237,8011,8126,8231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,327,422,628,674,746,849,924,1165,1227,1320,1396,1453,1517,1591,1681,1978,2051,2118,2186,2245,2333,2468,2580,2638,2727,2809,2912,2982,3082,3403,3480,3558,3639,3714,3776,3837,3909,3995,4105,4211,4301,4410,4504,4580,4659,4745,4927,5132,5241,5367,5429,6200,6311,6376", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "164,322,417,623,669,741,844,919,1160,1222,1315,1391,1448,1512,1586,1676,1973,2046,2113,2181,2240,2328,2463,2575,2633,2722,2804,2907,2977,3077,3398,3475,3553,3634,3709,3771,3832,3904,3990,4100,4206,4296,4405,4499,4575,4654,4740,4922,5127,5236,5362,5424,6195,6306,6371,6429"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21074,21188,21346,21535,22596,22642,22714,22817,22892,23133,23195,25252,25536,25727,26032,26106,26480,27155,27228,27799,28755,29057,29145,29280,29392,29450,30046,30316,30797,30948,31048,31369,31446,31692,31895,31970,32278,32856,32928,33113,33323,35093,37540,37649,37743,37819,37898,39613,40292,40497,40606,40732,40870,41641,41752,43564", "endColumns": "113,157,94,205,45,71,102,74,240,61,92,75,56,63,73,89,296,72,66,67,58,87,134,111,57,88,81,102,69,99,320,76,77,80,74,61,60,71,85,109,105,89,108,93,75,78,85,181,204,108,125,61,770,110,64,57", "endOffsets": "21183,21341,21436,21736,22637,22709,22812,22887,23128,23190,23283,25323,25588,25786,26101,26191,26772,27223,27290,27862,28809,29140,29275,29387,29445,29534,30123,30414,30862,31043,31364,31441,31519,31768,31965,32027,32334,32923,33009,33218,33424,35178,37644,37738,37814,37893,37979,39790,40492,40601,40727,40789,41636,41747,41812,43617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "46820,46920", "endColumns": "99,101", "endOffsets": "46915,47017"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "8236,47022", "endColumns": "60,77", "endOffsets": "8292,47095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4798,4906,5069,5200,5308,5469,5602,5724,5994,6186,6295,6460,6592,6757,6914,6981,7050", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "4901,5064,5195,5303,5464,5597,5719,5829,6181,6290,6455,6587,6752,6909,6976,7045,7130"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,484,573,652,745,837,924,997,1067,1153,1244,1321,1403,1473", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "196,278,376,479,568,647,740,832,919,992,1062,1148,1239,1316,1398,1468,1589"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4638,7242,7340,7526,8378,8457,13358,13450,13537,13610,13763,13849,14182,46547,46629,46699", "endColumns": "95,81,97,102,88,78,92,91,86,72,69,85,90,76,81,69,120", "endOffsets": "4633,4715,7335,7438,7610,8452,8545,13445,13532,13605,13675,13844,13935,14254,46624,46694,46815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,14336", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,14432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,214,276,352,437,497,556,614,708,789,883,996,1109,1197,1275,1359,1443,1544,1639,1711,1803,1891,1979,2087,2169,2261,2340,2439,2517,2615,2709,2800,2898,3016,3092,3184,3287,3383,3459,3527,4238,4929,5008,5138,5245,5300,5398,5490,5575,5685,5798,5857,5943,5994,6074,6184,6262,6335,6402,6468,6516,6597,6703,6776,6822,6872,6943,7001,7065,7265,7412,7557,7629,7715,7799,7894,7984,8082,8160,8253,8334,8434,8519,8616,8671,8791,8842,8904,8980,9050,9129,9199,9270,9364,9439,9492", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "125,209,271,347,432,492,551,609,703,784,878,991,1104,1192,1270,1354,1438,1539,1634,1706,1798,1886,1974,2082,2164,2256,2335,2434,2512,2610,2704,2795,2893,3011,3087,3179,3282,3378,3454,3522,4233,4924,5003,5133,5240,5295,5393,5485,5570,5680,5793,5852,5938,5989,6069,6179,6257,6330,6397,6463,6511,6592,6698,6771,6817,6867,6938,6996,7060,7260,7407,7552,7624,7710,7794,7889,7979,8077,8155,8248,8329,8429,8514,8611,8666,8786,8837,8899,8975,9045,9124,9194,9265,9359,9434,9487,9559"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14977,15052,15136,15198,15274,15359,15419,15721,15779,15873,15954,16208,16414,16527,16683,17048,17339,18372,18623,18718,18790,18950,19038,19126,19296,19604,19696,19775,19874,19952,20050,20144,20235,20333,20451,20604,20875,20978,21741,21817,21885,23547,24238,24317,24447,24554,24609,24707,24799,24884,24994,25107,25166,25791,25842,25922,26196,26274,26347,26414,26921,27295,27376,27482,27555,27601,27931,28002,28060,28124,28324,28471,28814,28886,29867,29951,30128,30218,30626,30704,30867,31524,32096,32181,39894,39949,40069,40230,40794,43052,43122,43201,43271,43342,43436,43511,44932", "endColumns": "74,83,61,75,84,59,58,57,93,80,93,112,112,87,77,83,83,100,94,71,91,87,87,107,81,91,78,98,77,97,93,90,97,117,75,91,102,95,75,67,710,690,78,129,106,54,97,91,84,109,112,58,85,50,79,109,77,72,66,65,47,80,105,72,45,49,70,57,63,199,146,144,71,85,83,94,89,97,77,92,80,99,84,96,54,119,50,61,75,69,78,69,70,93,74,52,71", "endOffsets": "15047,15131,15193,15269,15354,15414,15473,15774,15868,15949,16043,16316,16522,16610,16756,17127,17418,18468,18713,18785,18877,19033,19121,19229,19373,19691,19770,19869,19947,20045,20139,20230,20328,20446,20522,20691,20973,21069,21812,21880,22591,24233,24312,24442,24549,24604,24702,24794,24879,24989,25102,25161,25247,25837,25917,26027,26269,26342,26409,26475,26964,27371,27477,27550,27596,27646,27997,28055,28119,28319,28466,28611,28881,28967,29946,30041,30213,30311,30699,30792,30943,31619,32176,32273,39944,40064,40115,40287,40865,43117,43196,43266,43337,43431,43506,43559,44999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,219,298,392,484,568,651,785,879,973,1065,1193,1261,1383,1447,1619,1808,1964,2063,2163,2260,2360,2561,2666,2767,3038,3153,3296,3503,3729,3827,3954,4147,4241,4302,4368,4449,4555,4664,4746,4846,4940,5045,5263,5331,5402,5478,5603,5675,5759,5834,5928,5999,6067,6184,6272,6392,6491,6568,6663,6732,6820,6881,6987,7098,7198,7337,7406,7503,7607,7742,7813,7912,8022,8080,8136,8222,8320,8498,8780,9078,9176,9257,9347,9414,9531,9637,9722,9810,9883,9966,10055,10152,10280,10425,10496,10567,10620,10784,10849,10914,10995,11068,11148,11227,11372,11494,11604,11694,11782,11867,11943", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "125,214,293,387,479,563,646,780,874,968,1060,1188,1256,1378,1442,1614,1803,1959,2058,2158,2255,2355,2556,2661,2762,3033,3148,3291,3498,3724,3822,3949,4142,4236,4297,4363,4444,4550,4659,4741,4841,4935,5040,5258,5326,5397,5473,5598,5670,5754,5829,5923,5994,6062,6179,6267,6387,6486,6563,6658,6727,6815,6876,6982,7093,7193,7332,7401,7498,7602,7737,7808,7907,8017,8075,8131,8217,8315,8493,8775,9073,9171,9252,9342,9409,9526,9632,9717,9805,9878,9961,10050,10147,10275,10420,10491,10562,10615,10779,10844,10909,10990,11063,11143,11222,11367,11489,11599,11689,11777,11862,11938,12105"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15478,15553,15642,21441,23288,23380,23464,25593,26827,26969,27063,29739,31624,31773,32032,32339,32511,32700,33014,33223,33429,33526,33626,33827,33932,34033,34304,34419,34562,34769,34995,35183,35310,35503,35597,35658,35724,35805,35911,36020,36102,36202,36296,36401,36619,36687,36758,36834,36959,37031,37115,37190,37284,37355,37423,37984,38072,38192,38291,38368,38463,38532,38620,38681,38787,38898,38998,39137,39206,39303,39407,39542,39795,40120,41817,41875,41931,42017,42115,42293,42575,42873,42971,43622,43712,43779,43896,44002,44087,44175,44248,44331,44420,44517,44645,44790,44861,45004,45057,45221,45286,45351,45432,45505,45585,45664,45809,45931,46041,46131,46219,46304,46380", "endColumns": "74,88,78,93,91,83,82,133,93,93,91,127,67,121,63,171,188,155,98,99,96,99,200,104,100,270,114,142,206,225,97,126,192,93,60,65,80,105,108,81,99,93,104,217,67,70,75,124,71,83,74,93,70,67,116,87,119,98,76,94,68,87,60,105,110,99,138,68,96,103,134,70,98,109,57,55,85,97,177,281,297,97,80,89,66,116,105,84,87,72,82,88,96,127,144,70,70,52,163,64,64,80,72,79,78,144,121,109,89,87,84,75,166", "endOffsets": "15548,15637,15716,21530,23375,23459,23542,25722,26916,27058,27150,29862,31687,31890,32091,32506,32695,32851,33108,33318,33521,33621,33822,33927,34028,34299,34414,34557,34764,34990,35088,35305,35498,35592,35653,35719,35800,35906,36015,36097,36197,36291,36396,36614,36682,36753,36829,36954,37026,37110,37185,37279,37350,37418,37535,38067,38187,38286,38363,38458,38527,38615,38676,38782,38893,38993,39132,39201,39298,39402,39537,39608,39889,40225,41870,41926,42012,42110,42288,42570,42868,42966,43047,43707,43774,43891,43997,44082,44170,44243,44326,44415,44512,44640,44785,44856,44927,45052,45216,45281,45346,45427,45500,45580,45659,45804,45926,46036,46126,46214,46299,46375,46542"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "28972", "endColumns": "84", "endOffsets": "29052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5834", "endColumns": "159", "endOffsets": "5989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,246,312,379,445,527", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "137,241,307,374,440,522,590"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14437,14524,14628,14694,14761,14827,14909", "endColumns": "86,103,65,66,65,81,67", "endOffsets": "14519,14623,14689,14756,14822,14904,14972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,148,209,271,352,416,491,552,633,708,776,838", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "143,204,266,347,411,486,547,628,703,771,833,905"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16115,16987,17132,17194,17275,17563,18098,18291,18548,18882,19234,19532", "endColumns": "92,60,61,80,63,74,60,80,74,67,61,71", "endOffsets": "16203,17043,17189,17270,17334,17633,18154,18367,18618,18945,19291,19599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,216", "endColumns": "77,82,77", "endOffsets": "128,211,289"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4720,7443,7742", "endColumns": "77,82,77", "endOffsets": "4793,7521,7815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1091,1156,1251,1332,1395,1484,1548,1617,1680,1754,1818,1875,1993,2051,2113,2170,2250,2389,2478,2554,2649,2730,2812,2953,3034,3114,3265,3355,3435,3491,3547,3613,3692,3774,3845,3934,4008,4085,4155,4234,4334,4418,4502,4594,4694,4768,4849,4951,5004,5089,5156,5249,5338,5400,5464,5527,5595,5708,5815,5919,6020,6080,6140,6223,6306,6382", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "273,354,433,520,621,717,821,943,1024,1086,1151,1246,1327,1390,1479,1543,1612,1675,1749,1813,1870,1988,2046,2108,2165,2245,2384,2473,2549,2644,2725,2807,2948,3029,3109,3260,3350,3430,3486,3542,3608,3687,3769,3840,3929,4003,4080,4150,4229,4329,4413,4497,4589,4689,4763,4844,4946,4999,5084,5151,5244,5333,5395,5459,5522,5590,5703,5810,5914,6015,6075,6135,6218,6301,6377,6454"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,7615,7677,7820,8297,8550,8613,8702,8766,8835,8898,8972,9036,9093,9211,9269,9331,9388,9468,9607,9696,9772,9867,9948,10030,10171,10252,10332,10483,10573,10653,10709,10765,10831,10910,10992,11063,11152,11226,11303,11373,11452,11552,11636,11720,11812,11912,11986,12067,12169,12222,12307,12374,12467,12556,12618,12682,12745,12813,12926,13033,13137,13238,13298,13680,14023,14106,14259", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,78,86,100,95,103,121,80,61,64,94,80,62,88,63,68,62,73,63,56,117,57,61,56,79,138,88,75,94,80,81,140,80,79,150,89,79,55,55,65,78,81,70,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82,82,75,76", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,7672,7737,7910,8373,8608,8697,8761,8830,8893,8967,9031,9088,9206,9264,9326,9383,9463,9602,9691,9767,9862,9943,10025,10166,10247,10327,10478,10568,10648,10704,10760,10826,10905,10987,11058,11147,11221,11298,11368,11447,11547,11631,11715,11807,11907,11981,12062,12164,12217,12302,12369,12462,12551,12613,12677,12740,12808,12921,13028,13132,13233,13293,13353,13758,14101,14177,14331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,215,283,376,448,509,582,649,711,779,850,910,971,1045,1109,1178,1241,1316,1389,1470,1547,1633,1726,1843,1934,1984,2044,2132,2196,2266,2335,2446,2535,2639", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "117,210,278,371,443,504,577,644,706,774,845,905,966,1040,1104,1173,1236,1311,1384,1465,1542,1628,1721,1838,1929,1979,2039,2127,2191,2261,2330,2441,2530,2634,2737"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16048,16321,16615,16761,16854,16926,17423,17496,17638,17700,17768,17839,17899,17960,18034,18159,18228,18473,19378,19451,20527,20696,20782,25328,25445,26777,27651,27711,27867,28616,28686,29539,29650,30419,30523", "endColumns": "66,92,67,92,71,60,72,66,61,67,70,59,60,73,63,68,62,74,72,80,76,85,92,116,90,49,59,87,63,69,68,110,88,103,102", "endOffsets": "16110,16409,16678,16849,16921,16982,17491,17558,17695,17763,17834,17894,17955,18029,18093,18223,18286,18543,19446,19527,20599,20777,20870,25440,25531,26822,27706,27794,27926,28681,28750,29645,29734,30518,30621"}}]}]}