{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4869,5053,5191,5300,5468,5606,5728,6015,6185,6293,6478,6615,6787,6959,7030,7098", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "4864,5048,5186,5295,5463,5601,5723,5833,6180,6288,6473,6610,6782,6954,7025,7093,7181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,338,419,481,549,603,678,763,843,940,1039,1124,1209,1293,1380,1478,1576,1647,1742,1834,1920,2030,2115,2210,2288,2390,2464,2555,2647,2734,2818,2923,2991,3069,3185,3295,3374,3437,4132,4807,4880,4975,5080,5135,5223,5312,5376,5465,5561,5617,5705,5753,5837,5951,6026,6096,6163,6229,6278,6356,6450,6525,6570,6622,6688,6746,6808,6995,7166,7296,7361,7448,7518,7610,7690,7776,7847,7938,8010,8119,8206,8297,8352,8483,8537,8595,8668,8738,8809,8874,8943,9033,9104,9156", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "118,195,261,333,414,476,544,598,673,758,838,935,1034,1119,1204,1288,1375,1473,1571,1642,1737,1829,1915,2025,2110,2205,2283,2385,2459,2550,2642,2729,2813,2918,2986,3064,3180,3290,3369,3432,4127,4802,4875,4970,5075,5130,5218,5307,5371,5460,5556,5612,5700,5748,5832,5946,6021,6091,6158,6224,6273,6351,6445,6520,6565,6617,6683,6741,6803,6990,7161,7291,7356,7443,7513,7605,7685,7771,7842,7933,8005,8114,8201,8292,8347,8478,8532,8590,8663,8733,8804,8869,8938,9028,9099,9151,9225"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14365,14433,14510,14576,14648,14729,14791,15096,15150,15225,15310,15526,15696,15795,15949,16317,16613,17642,17893,17991,18062,18225,18317,18403,18574,18878,18973,19051,19153,19227,19318,19410,19497,19581,19686,19832,20081,20197,21042,21121,21184,22805,23480,23553,23648,23753,23808,23896,23985,24049,24138,24234,24290,24894,24942,25026,25308,25383,25453,25520,26056,26430,26508,26602,26677,26722,27015,27081,27139,27201,27388,27559,27886,27951,28874,28944,29113,29193,29554,29625,29792,30436,30996,31083,38571,38626,38757,38918,39500,41684,41754,41825,41890,41959,42049,42120,43549", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "14428,14505,14571,14643,14724,14786,14854,15145,15220,15305,15385,15618,15790,15875,16029,16396,16695,17735,17986,18057,18152,18312,18398,18508,18654,18968,19046,19148,19222,19313,19405,19492,19576,19681,19749,19905,20192,20302,21116,21179,21874,23475,23548,23643,23748,23803,23891,23980,24044,24133,24229,24285,24373,24937,25021,25135,25378,25448,25515,25581,26100,26503,26597,26672,26717,26769,27076,27134,27196,27383,27554,27684,27946,28033,28939,29031,29188,29274,29620,29711,29859,30540,31078,31169,38621,38752,38806,38971,39568,41749,41820,41885,41954,42044,42115,42167,43618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,374,477,688,735,801,890,960,1219,1283,1371,1437,1491,1555,1631,1723,2039,2118,2185,2238,2291,2369,2492,2605,2662,2738,2815,2899,2975,3083,3392,3470,3547,3618,3678,3745,3805,3880,3962,4059,4158,4249,4343,4441,4516,4595,4683,4882,5089,5202,5343,5406,6105,6212,6276", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "184,369,472,683,730,796,885,955,1214,1278,1366,1432,1486,1550,1626,1718,2034,2113,2180,2233,2286,2364,2487,2600,2657,2733,2810,2894,2970,3078,3387,3465,3542,3613,3673,3740,3800,3875,3957,4054,4153,4244,4338,4436,4511,4590,4678,4877,5084,5197,5338,5401,6100,6207,6271,6333"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20307,20441,20626,20831,21879,21926,21992,22081,22151,22410,22474,24378,24632,24830,25140,25216,25586,26284,26363,26898,27833,28128,28206,28329,28442,28499,29036,29279,29716,29864,29972,30281,30359,30609,30804,30864,31174,31757,31832,32006,32201,33865,36267,36361,36459,36534,36613,38277,38976,39183,39296,39437,39573,40272,40379,42172", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "20436,20621,20724,21037,21921,21987,22076,22146,22405,22469,22557,24439,24681,24889,25211,25303,25897,26358,26425,26946,27881,28201,28324,28437,28494,28570,29108,29358,29787,29967,30276,30354,30431,30675,30859,30926,31229,31827,31909,32098,32295,33951,36356,36454,36529,36608,36696,38471,39178,39291,39432,39495,40267,40374,40438,42229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "45380,45469", "endColumns": "88,96", "endOffsets": "45464,45561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,214,292,394,480,559,637,781,883,978,1062,1190,1254,1378,1443,1623,1833,1966,2058,2156,2247,2343,2518,2618,2720,2966,3065,3189,3404,3625,3721,3835,4027,4121,4183,4251,4333,4424,4516,4592,4687,4776,4876,5105,5179,5246,5317,5428,5518,5606,5683,5769,5843,5925,6032,6121,6231,6329,6404,6491,6565,6654,6714,6812,6912,7015,7151,7213,7307,7408,7534,7608,7703,7810,7874,7939,8024,8140,8328,8598,8886,8982,9051,9141,9215,9335,9454,9545,9628,9699,9782,9865,9955,10068,10224,10295,10366,10435,10607,10675,10741,10827,10899,10977,11053,11174,11283,11380,11468,11548,11636,11708", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "124,209,287,389,475,554,632,776,878,973,1057,1185,1249,1373,1438,1618,1828,1961,2053,2151,2242,2338,2513,2613,2715,2961,3060,3184,3399,3620,3716,3830,4022,4116,4178,4246,4328,4419,4511,4587,4682,4771,4871,5100,5174,5241,5312,5423,5513,5601,5678,5764,5838,5920,6027,6116,6226,6324,6399,6486,6560,6649,6709,6807,6907,7010,7146,7208,7302,7403,7529,7603,7698,7805,7869,7934,8019,8135,8323,8593,8881,8977,9046,9136,9210,9330,9449,9540,9623,9694,9777,9860,9950,10063,10219,10290,10361,10430,10602,10670,10736,10822,10894,10972,11048,11169,11278,11375,11463,11543,11631,11703,11852"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14859,14933,15018,20729,22562,22648,22727,24686,25954,26105,26200,28746,30545,30680,30931,31234,31414,31624,31914,32103,32300,32391,32487,32662,32762,32864,33110,33209,33333,33548,33769,33956,34070,34262,34356,34418,34486,34568,34659,34751,34827,34922,35011,35111,35340,35414,35481,35552,35663,35753,35841,35918,36004,36078,36160,36701,36790,36900,36998,37073,37160,37234,37323,37383,37481,37581,37684,37820,37882,37976,38077,38203,38476,38811,40443,40507,40572,40657,40773,40961,41231,41519,41615,42234,42324,42398,42518,42637,42728,42811,42882,42965,43048,43138,43251,43407,43478,43623,43692,43864,43932,43998,44084,44156,44234,44310,44431,44540,44637,44725,44805,44893,44965", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "14928,15013,15091,20826,22643,22722,22800,24825,26051,26195,26279,28869,30604,30799,30991,31409,31619,31752,32001,32196,32386,32482,32657,32757,32859,33105,33204,33328,33543,33764,33860,34065,34257,34351,34413,34481,34563,34654,34746,34822,34917,35006,35106,35335,35409,35476,35547,35658,35748,35836,35913,35999,36073,36155,36262,36785,36895,36993,37068,37155,37229,37318,37378,37476,37576,37679,37815,37877,37971,38072,38198,38272,38566,38913,40502,40567,40652,40768,40956,41226,41514,41610,41679,42319,42393,42513,42632,42723,42806,42877,42960,43043,43133,43246,43402,43473,43544,43687,43859,43927,43993,44079,44151,44229,44305,44426,44535,44632,44720,44800,44888,44960,45109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7186,7947,8048,8163", "endColumns": "95,100,114,103", "endOffsets": "7277,8043,8158,8262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4495,4590,7282,7379,7558,8403,8485,13311,13400,13487,13551,13700,13783,14117,45114,45193,45259", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "4585,4673,7374,7473,7640,8480,8576,13395,13482,13546,13610,13778,13866,14186,45188,45254,45375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "28038", "endColumns": "89", "endOffsets": "28123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3568,3670,3772,3873,3976,4083,14264", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3563,3665,3767,3868,3971,4078,4188,14360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5838", "endColumns": "176", "endOffsets": "6010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,189,258,345,420,481,547,613,678,751,818,887,950,1024,1088,1157,1221,1300,1365,1448,1526,1608,1697,1805,1885,1937,1985,2061,2125,2196,2269,2361,2440,2538", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "111,184,253,340,415,476,542,608,673,746,813,882,945,1019,1083,1152,1216,1295,1360,1443,1521,1603,1692,1800,1880,1932,1980,2056,2120,2191,2264,2356,2435,2533,2626"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15390,15623,15880,16034,16121,16196,16700,16766,16902,16967,17040,17107,17176,17239,17313,17435,17504,17740,18659,18724,19754,19910,19992,24444,24552,25902,26774,26822,26951,27689,27760,28575,28667,29363,29461", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "15446,15691,15944,16116,16191,16252,16761,16827,16962,17035,17102,17171,17234,17308,17372,17499,17563,17814,18719,18802,19827,19987,20076,24547,24627,25949,26817,26893,27010,27755,27828,28662,28741,29456,29549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3067,3148,3224,3301,3391,4193,4292,4412,7645,7708,7848,8328,8581,8640,8750,8812,8881,8939,9011,9072,9127,9230,9287,9347,9402,9483,9603,9686,9764,9860,9946,10034,10169,10252,10332,10472,10566,10648,10701,10752,10818,10894,10976,11047,11131,11208,11283,11362,11439,11544,11640,11717,11809,11906,11980,12065,12162,12214,12297,12364,12452,12539,12601,12665,12728,12794,12892,12998,13092,13199,13256,13615,13955,14040,14191", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3143,3219,3296,3386,3466,4287,4407,4490,7703,7767,7942,8398,8635,8745,8807,8876,8934,9006,9067,9122,9225,9282,9342,9397,9478,9598,9681,9759,9855,9941,10029,10164,10247,10327,10467,10561,10643,10696,10747,10813,10889,10971,11042,11126,11203,11278,11357,11434,11539,11635,11712,11804,11901,11975,12060,12157,12209,12292,12359,12447,12534,12596,12660,12723,12789,12887,12993,13087,13194,13251,13306,13695,14035,14112,14259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,190,254,339,402,472,530,604,678,746,807", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "125,185,249,334,397,467,525,599,673,741,802,873"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15451,16257,16401,16465,16550,16832,17377,17568,17819,18157,18513,18807", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "15521,16312,16460,16545,16608,16897,17430,17637,17888,18220,18569,18873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,76", "endOffsets": "258,335"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "8267,45566", "endColumns": "60,80", "endOffsets": "8323,45642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,215", "endColumns": "79,79,75", "endOffsets": "130,210,286"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4678,7478,7772", "endColumns": "79,79,75", "endOffsets": "4753,7553,7843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,13871", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,13950"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4758,4869,5053,5191,5300,5468,5606,5728,6015,6185,6293,6478,6615,6787,6959,7030,7098", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "4864,5048,5186,5295,5463,5601,5723,5833,6180,6288,6473,6610,6782,6954,7025,7093,7181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,266,338,419,481,549,603,678,763,843,940,1039,1124,1209,1293,1380,1478,1576,1647,1742,1834,1920,2030,2115,2210,2288,2390,2464,2555,2647,2734,2818,2923,2991,3069,3185,3295,3374,3437,4132,4807,4880,4975,5080,5135,5223,5312,5376,5465,5561,5617,5705,5753,5837,5951,6026,6096,6163,6229,6278,6356,6450,6525,6570,6622,6688,6746,6808,6995,7166,7296,7361,7448,7518,7610,7690,7776,7847,7938,8010,8119,8206,8297,8352,8483,8537,8595,8668,8738,8809,8874,8943,9033,9104,9156", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "118,195,261,333,414,476,544,598,673,758,838,935,1034,1119,1204,1288,1375,1473,1571,1642,1737,1829,1915,2025,2110,2205,2283,2385,2459,2550,2642,2729,2813,2918,2986,3064,3180,3290,3369,3432,4127,4802,4875,4970,5075,5130,5218,5307,5371,5460,5556,5612,5700,5748,5832,5946,6021,6091,6158,6224,6273,6351,6445,6520,6565,6617,6683,6741,6803,6990,7161,7291,7356,7443,7513,7605,7685,7771,7842,7933,8005,8114,8201,8292,8347,8478,8532,8590,8663,8733,8804,8869,8938,9028,9099,9151,9225"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14365,14433,14510,14576,14648,14729,14791,15096,15150,15225,15310,15526,15696,15795,15949,16317,16613,17642,17893,17991,18062,18225,18317,18403,18574,18878,18973,19051,19153,19227,19318,19410,19497,19581,19686,19832,20081,20197,21042,21121,21184,22805,23480,23553,23648,23753,23808,23896,23985,24049,24138,24234,24290,24894,24942,25026,25308,25383,25453,25520,26056,26430,26508,26602,26677,26722,27015,27081,27139,27201,27388,27559,27886,27951,28874,28944,29113,29193,29554,29625,29792,30436,30996,31083,38571,38626,38757,38918,39500,41684,41754,41825,41890,41959,42049,42120,43549", "endColumns": "67,76,65,71,80,61,67,53,74,84,79,96,98,84,84,83,86,97,97,70,94,91,85,109,84,94,77,101,73,90,91,86,83,104,67,77,115,109,78,62,694,674,72,94,104,54,87,88,63,88,95,55,87,47,83,113,74,69,66,65,48,77,93,74,44,51,65,57,61,186,170,129,64,86,69,91,79,85,70,90,71,108,86,90,54,130,53,57,72,69,70,64,68,89,70,51,73", "endOffsets": "14428,14505,14571,14643,14724,14786,14854,15145,15220,15305,15385,15618,15790,15875,16029,16396,16695,17735,17986,18057,18152,18312,18398,18508,18654,18968,19046,19148,19222,19313,19405,19492,19576,19681,19749,19905,20192,20302,21116,21179,21874,23475,23548,23643,23748,23803,23891,23980,24044,24133,24229,24285,24373,24937,25021,25135,25378,25448,25515,25581,26100,26503,26597,26672,26717,26769,27076,27134,27196,27383,27554,27684,27946,28033,28939,29031,29188,29274,29620,29711,29859,30540,31078,31169,38621,38752,38806,38971,39568,41749,41820,41885,41954,42044,42115,42167,43618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,189,374,477,688,735,801,890,960,1219,1283,1371,1437,1491,1555,1631,1723,2039,2118,2185,2238,2291,2369,2492,2605,2662,2738,2815,2899,2975,3083,3392,3470,3547,3618,3678,3745,3805,3880,3962,4059,4158,4249,4343,4441,4516,4595,4683,4882,5089,5202,5343,5406,6105,6212,6276", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "184,369,472,683,730,796,885,955,1214,1278,1366,1432,1486,1550,1626,1718,2034,2113,2180,2233,2286,2364,2487,2600,2657,2733,2810,2894,2970,3078,3387,3465,3542,3613,3673,3740,3800,3875,3957,4054,4153,4244,4338,4436,4511,4590,4678,4877,5084,5197,5338,5401,6100,6207,6271,6333"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20307,20441,20626,20831,21879,21926,21992,22081,22151,22410,22474,24378,24632,24830,25140,25216,25586,26284,26363,26898,27833,28128,28206,28329,28442,28499,29036,29279,29716,29864,29972,30281,30359,30609,30804,30864,31174,31757,31832,32006,32201,33865,36267,36361,36459,36534,36613,38277,38976,39183,39296,39437,39573,40272,40379,42172", "endColumns": "133,184,102,210,46,65,88,69,258,63,87,65,53,63,75,91,315,78,66,52,52,77,122,112,56,75,76,83,75,107,308,77,76,70,59,66,59,74,81,96,98,90,93,97,74,78,87,198,206,112,140,62,698,106,63,61", "endOffsets": "20436,20621,20724,21037,21921,21987,22076,22146,22405,22469,22557,24439,24681,24889,25211,25303,25897,26358,26425,26946,27881,28201,28324,28437,28494,28570,29108,29358,29787,29967,30276,30354,30431,30675,30859,30926,31229,31827,31909,32098,32295,33951,36356,36454,36529,36608,36696,38471,39178,39291,39432,39495,40267,40374,40438,42229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,96", "endOffsets": "139,236"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "45380,45469", "endColumns": "88,96", "endOffsets": "45464,45561"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,214,292,394,480,559,637,781,883,978,1062,1190,1254,1378,1443,1623,1833,1966,2058,2156,2247,2343,2518,2618,2720,2966,3065,3189,3404,3625,3721,3835,4027,4121,4183,4251,4333,4424,4516,4592,4687,4776,4876,5105,5179,5246,5317,5428,5518,5606,5683,5769,5843,5925,6032,6121,6231,6329,6404,6491,6565,6654,6714,6812,6912,7015,7151,7213,7307,7408,7534,7608,7703,7810,7874,7939,8024,8140,8328,8598,8886,8982,9051,9141,9215,9335,9454,9545,9628,9699,9782,9865,9955,10068,10224,10295,10366,10435,10607,10675,10741,10827,10899,10977,11053,11174,11283,11380,11468,11548,11636,11708", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "124,209,287,389,475,554,632,776,878,973,1057,1185,1249,1373,1438,1618,1828,1961,2053,2151,2242,2338,2513,2613,2715,2961,3060,3184,3399,3620,3716,3830,4022,4116,4178,4246,4328,4419,4511,4587,4682,4771,4871,5100,5174,5241,5312,5423,5513,5601,5678,5764,5838,5920,6027,6116,6226,6324,6399,6486,6560,6649,6709,6807,6907,7010,7146,7208,7302,7403,7529,7603,7698,7805,7869,7934,8019,8135,8323,8593,8881,8977,9046,9136,9210,9330,9449,9540,9623,9694,9777,9860,9950,10063,10219,10290,10361,10430,10602,10670,10736,10822,10894,10972,11048,11169,11278,11375,11463,11543,11631,11703,11852"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14859,14933,15018,20729,22562,22648,22727,24686,25954,26105,26200,28746,30545,30680,30931,31234,31414,31624,31914,32103,32300,32391,32487,32662,32762,32864,33110,33209,33333,33548,33769,33956,34070,34262,34356,34418,34486,34568,34659,34751,34827,34922,35011,35111,35340,35414,35481,35552,35663,35753,35841,35918,36004,36078,36160,36701,36790,36900,36998,37073,37160,37234,37323,37383,37481,37581,37684,37820,37882,37976,38077,38203,38476,38811,40443,40507,40572,40657,40773,40961,41231,41519,41615,42234,42324,42398,42518,42637,42728,42811,42882,42965,43048,43138,43251,43407,43478,43623,43692,43864,43932,43998,44084,44156,44234,44310,44431,44540,44637,44725,44805,44893,44965", "endColumns": "73,84,77,101,85,78,77,143,101,94,83,127,63,123,64,179,209,132,91,97,90,95,174,99,101,245,98,123,214,220,95,113,191,93,61,67,81,90,91,75,94,88,99,228,73,66,70,110,89,87,76,85,73,81,106,88,109,97,74,86,73,88,59,97,99,102,135,61,93,100,125,73,94,106,63,64,84,115,187,269,287,95,68,89,73,119,118,90,82,70,82,82,89,112,155,70,70,68,171,67,65,85,71,77,75,120,108,96,87,79,87,71,148", "endOffsets": "14928,15013,15091,20826,22643,22722,22800,24825,26051,26195,26279,28869,30604,30799,30991,31409,31619,31752,32001,32196,32386,32482,32657,32757,32859,33105,33204,33328,33543,33764,33860,34065,34257,34351,34413,34481,34563,34654,34746,34822,34917,35006,35106,35335,35409,35476,35547,35658,35748,35836,35913,35999,36073,36155,36262,36785,36895,36993,37068,37155,37229,37318,37378,37476,37576,37679,37815,37877,37971,38072,38198,38272,38566,38913,40502,40567,40652,40768,40956,41226,41514,41610,41679,42319,42393,42513,42632,42723,42806,42877,42960,43043,43133,43246,43402,43473,43544,43687,43859,43927,43993,44079,44151,44229,44305,44426,44535,44632,44720,44800,44888,44960,45109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7186,7947,8048,8163", "endColumns": "95,100,114,103", "endOffsets": "7277,8043,8158,8262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4495,4590,7282,7379,7558,8403,8485,13311,13400,13487,13551,13700,13783,14117,45114,45193,45259", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "4585,4673,7374,7473,7640,8480,8576,13395,13482,13546,13610,13778,13866,14186,45188,45254,45375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "28038", "endColumns": "89", "endOffsets": "28123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3568,3670,3772,3873,3976,4083,14264", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3563,3665,3767,3868,3971,4078,4188,14360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5838", "endColumns": "176", "endOffsets": "6010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,116,189,258,345,420,481,547,613,678,751,818,887,950,1024,1088,1157,1221,1300,1365,1448,1526,1608,1697,1805,1885,1937,1985,2061,2125,2196,2269,2361,2440,2538", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "111,184,253,340,415,476,542,608,673,746,813,882,945,1019,1083,1152,1216,1295,1360,1443,1521,1603,1692,1800,1880,1932,1980,2056,2120,2191,2264,2356,2435,2533,2626"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15390,15623,15880,16034,16121,16196,16700,16766,16902,16967,17040,17107,17176,17239,17313,17435,17504,17740,18659,18724,19754,19910,19992,24444,24552,25902,26774,26822,26951,27689,27760,28575,28667,29363,29461", "endColumns": "60,72,68,86,74,60,65,65,64,72,66,68,62,73,63,68,63,78,64,82,77,81,88,107,79,51,47,75,63,70,72,91,78,97,92", "endOffsets": "15446,15691,15944,16116,16191,16252,16761,16827,16962,17035,17102,17171,17234,17308,17372,17499,17563,17814,18719,18802,19827,19987,20076,24547,24627,25949,26817,26893,27010,27755,27828,28662,28741,29456,29549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,344,420,497,587,667,766,886,969,1032,1096,1195,1270,1329,1439,1501,1570,1628,1700,1761,1816,1919,1976,2036,2091,2172,2292,2375,2453,2549,2635,2723,2858,2941,3021,3161,3255,3337,3390,3441,3507,3583,3665,3736,3820,3897,3972,4051,4128,4233,4329,4406,4498,4595,4669,4754,4851,4903,4986,5053,5141,5228,5290,5354,5417,5483,5581,5687,5781,5888,5945,6000,6085,6170,6247", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "258,339,415,492,582,662,761,881,964,1027,1091,1190,1265,1324,1434,1496,1565,1623,1695,1756,1811,1914,1971,2031,2086,2167,2287,2370,2448,2544,2630,2718,2853,2936,3016,3156,3250,3332,3385,3436,3502,3578,3660,3731,3815,3892,3967,4046,4123,4228,4324,4401,4493,4590,4664,4749,4846,4898,4981,5048,5136,5223,5285,5349,5412,5478,5576,5682,5776,5883,5940,5995,6080,6165,6242,6315"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3067,3148,3224,3301,3391,4193,4292,4412,7645,7708,7848,8328,8581,8640,8750,8812,8881,8939,9011,9072,9127,9230,9287,9347,9402,9483,9603,9686,9764,9860,9946,10034,10169,10252,10332,10472,10566,10648,10701,10752,10818,10894,10976,11047,11131,11208,11283,11362,11439,11544,11640,11717,11809,11906,11980,12065,12162,12214,12297,12364,12452,12539,12601,12665,12728,12794,12892,12998,13092,13199,13256,13615,13955,14040,14191", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,75,76,89,79,98,119,82,62,63,98,74,58,109,61,68,57,71,60,54,102,56,59,54,80,119,82,77,95,85,87,134,82,79,139,93,81,52,50,65,75,81,70,83,76,74,78,76,104,95,76,91,96,73,84,96,51,82,66,87,86,61,63,62,65,97,105,93,106,56,54,84,84,76,72", "endOffsets": "308,3143,3219,3296,3386,3466,4287,4407,4490,7703,7767,7942,8398,8635,8745,8807,8876,8934,9006,9067,9122,9225,9282,9342,9397,9478,9598,9681,9759,9855,9941,10029,10164,10247,10327,10467,10561,10643,10696,10747,10813,10889,10971,11042,11126,11203,11278,11357,11434,11539,11635,11712,11804,11901,11975,12060,12157,12209,12292,12359,12447,12534,12596,12660,12723,12789,12887,12993,13087,13194,13251,13306,13695,14035,14112,14259"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,190,254,339,402,472,530,604,678,746,807", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "125,185,249,334,397,467,525,599,673,741,802,873"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15451,16257,16401,16465,16550,16832,17377,17568,17819,18157,18513,18807", "endColumns": "74,59,63,84,62,69,57,73,73,67,60,70", "endOffsets": "15521,16312,16460,16545,16608,16897,17430,17637,17888,18220,18569,18873"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,76", "endOffsets": "258,335"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "8267,45566", "endColumns": "60,80", "endOffsets": "8323,45642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,215", "endColumns": "79,79,75", "endOffsets": "130,210,286"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4678,7478,7772", "endColumns": "79,79,75", "endOffsets": "4753,7553,7843"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,305,420,504,619,742,819,894,985,1078,1173,1267,1367,1460,1555,1650,1741,1832,1915,2025,2135,2235,2346,2455,2574,2756,2859", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "208,300,415,499,614,737,814,889,980,1073,1168,1262,1362,1455,1550,1645,1736,1827,1910,2020,2130,2230,2341,2450,2569,2751,2854,2938"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,421,513,628,712,827,950,1027,1102,1193,1286,1381,1475,1575,1668,1763,1858,1949,2040,2123,2233,2343,2443,2554,2663,2782,2964,13871", "endColumns": "107,91,114,83,114,122,76,74,90,92,94,93,99,92,94,94,90,90,82,109,109,99,110,108,118,181,102,83", "endOffsets": "416,508,623,707,822,945,1022,1097,1188,1281,1376,1470,1570,1663,1758,1853,1944,2035,2118,2228,2338,2438,2549,2658,2777,2959,3062,13950"}}]}]}