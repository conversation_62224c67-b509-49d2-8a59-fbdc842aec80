{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "92,100,171,175,504,510,511", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8388,8976,14018,14283,38756,39338,39417", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "8449,9052,14083,14397,38919,39412,39488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,505,506,507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4126,4202,8537,8625,8785,9612,9686,13866,13944,14088,14151,14402,14475,14777,38924,38999,39064", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4197,4271,8620,8711,8858,9681,9758,13939,14013,14146,14209,14470,14545,14839,38994,39059,39175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "8454,9206,9298,9399", "endColumns": "82,91,100,92", "endOffsets": "8532,9293,9394,9487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,279,356,468,512,570,645,709,827,882,954,1010,1063,1127,1192,1272,1416,1476,1536,1587,1638,1704,1788,1865,1920,1991,2058,2125,2185,2260,2413,2479,2551,2605,2663,2721,2779,2840,2906,2996,3073,3150,3240,3324,3394,3473,3558,3659,3768,3860,3954,4003,4239,4314,4371", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "139,274,351,463,507,565,640,704,822,877,949,1005,1058,1122,1187,1267,1411,1471,1531,1582,1633,1699,1783,1860,1915,1986,2053,2120,2180,2255,2408,2474,2546,2600,2658,2716,2774,2835,2901,2991,3068,3145,3235,3319,3389,3468,3553,3654,3763,3855,3949,3998,4234,4309,4366,4421"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,340,341,342,343,344,350,353,358,360,361,362,363,366,368,369,373,377,378,380,382,394,419,420,421,422,423,441,448,449,450,451,453,454,455,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20598,20687,20822,20968,21531,21575,21633,21708,21772,21890,21945,23310,23501,23632,23893,23958,24294,24739,24799,25258,25917,26091,26157,26241,26318,26373,26796,26986,27325,27443,27518,27671,27737,27934,28060,28118,28381,28774,28835,28986,29158,30409,32296,32386,32470,32540,32619,34013,34474,34583,34675,34769,34874,35110,35185,36487", "endColumns": "88,134,76,111,43,57,74,63,117,54,71,55,52,63,64,79,143,59,59,50,50,65,83,76,54,70,66,66,59,74,152,65,71,53,57,57,57,60,65,89,76,76,89,83,69,78,84,100,108,91,93,48,235,74,56,54", "endOffsets": "20682,20817,20894,21075,21570,21628,21703,21767,21885,21940,22012,23361,23549,23691,23953,24033,24433,24794,24854,25304,25963,26152,26236,26313,26368,26439,26858,27048,27380,27513,27666,27732,27804,27983,28113,28171,28434,28830,28896,29071,29230,30481,32381,32465,32535,32614,32699,34109,34578,34670,34764,34813,35105,35180,35237,36537"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,136", "endColumns": "80,76", "endOffsets": "131,208"}, "to": {"startLines": "508,509", "startColumns": "4,4", "startOffsets": "39180,39261", "endColumns": "80,76", "endOffsets": "39256,39333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,183,244,305,374,428,488,536,601,667,735,819,903,976,1045,1115,1186,1266,1345,1408,1484,1557,1627,1710,1780,1856,1926,2009,2074,2149,2222,2291,2360,2438,2498,2565,2657,2742,2805,2866,3193,3510,3575,3659,3739,3794,3870,3942,4000,4071,4144,4199,4269,4314,4384,4466,4523,4588,4655,4722,4766,4830,4907,4971,5014,5057,5112,5170,5228,5319,5411,5488,5548,5611,5670,5745,5808,5868,5930,6001,6059,6133,6202,6279,6328,6401,6446,6496,6552,6608,6669,6728,6789,6860,6919,6964", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "110,178,239,300,369,423,483,531,596,662,730,814,898,971,1040,1110,1181,1261,1340,1403,1479,1552,1622,1705,1775,1851,1921,2004,2069,2144,2217,2286,2355,2433,2493,2560,2652,2737,2800,2861,3188,3505,3570,3654,3734,3789,3865,3937,3995,4066,4139,4194,4264,4309,4379,4461,4518,4583,4650,4717,4761,4825,4902,4966,5009,5052,5107,5165,5223,5314,5406,5483,5543,5606,5665,5740,5803,5863,5925,5996,6054,6128,6197,6274,6323,6396,6441,6491,6547,6603,6664,6723,6784,6855,6914,6959,7021"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,348,349,351,352,356,357,359,364,371,372,443,444,445,447,452,465,466,467,468,469,470,471,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15489,15549,15617,15678,15739,15808,15862,16108,16156,16221,16287,16486,16641,16725,16862,17187,17448,18391,18604,18683,18746,18882,18955,19025,19165,19427,19503,19573,19656,19721,19796,19869,19938,20007,20085,20218,20421,20513,21080,21143,21204,22234,22551,22616,22700,22780,22835,22911,22983,23041,23112,23185,23240,23696,23741,23811,24038,24095,24160,24227,24552,24859,24923,25000,25064,25107,25371,25426,25484,25542,25633,25725,25968,26028,26662,26721,26863,26926,27192,27254,27385,27809,28235,28304,34177,34226,34299,34424,34818,36075,36131,36192,36251,36312,36383,36442,37535", "endColumns": "59,67,60,60,68,53,59,47,64,65,67,83,83,72,68,69,70,79,78,62,75,72,69,82,69,75,69,82,64,74,72,68,68,77,59,66,91,84,62,60,326,316,64,83,79,54,75,71,57,70,72,54,69,44,69,81,56,64,66,66,43,63,76,63,42,42,54,57,57,90,91,76,59,62,58,74,62,59,61,70,57,73,68,76,48,72,44,49,55,55,60,58,60,70,58,44,61", "endOffsets": "15544,15612,15673,15734,15803,15857,15917,16151,16216,16282,16350,16565,16720,16793,16926,17252,17514,18466,18678,18741,18817,18950,19020,19103,19230,19498,19568,19651,19716,19791,19864,19933,20002,20080,20140,20280,20508,20593,21138,21199,21526,22546,22611,22695,22775,22830,22906,22978,23036,23107,23180,23235,23305,23736,23806,23888,24090,24155,24222,24289,24591,24918,24995,25059,25102,25145,25421,25479,25537,25628,25720,25797,26023,26086,26716,26791,26921,26981,27249,27320,27438,27878,28299,28376,34221,34294,34339,34469,34869,36126,36187,36246,36307,36378,36437,36482,37592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1301,1397,1492,1586,1682,1774,1866,1958,2036,2132,2227,2322,2419,2515,2613,2764,14550", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1296,1392,1487,1581,1677,1769,1861,1953,2031,2127,2222,2317,2414,2510,2608,2759,2853,14624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3195,3287,3386,3480,3574,3667,3760,14914", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3282,3381,3475,3569,3662,3755,3851,15010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6562,6663,6791,6906,7008,7115,7231,7333,7534,7644,7745,7874,7989,8096,8204,8259,8316", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "6658,6786,6901,7003,7110,7226,7328,7421,7639,7740,7869,7984,8091,8199,8254,8311,8383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,289,400,509,589,672,748,833,918,1026,1128,1214,1300,1391,1500,1578,1657,1780,1873,1969,2075,2169", "endColumns": "130,102,110,108,79,82,75,84,84,107,101,85,85,90,108,77,78,122,92,95,105,93,99", "endOffsets": "181,284,395,504,584,667,743,828,913,1021,1123,1209,1295,1386,1495,1573,1652,1775,1868,1964,2070,2164,2264"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4348,4479,4582,4693,4802,4882,4965,5041,5126,5211,5319,5421,5507,5593,5684,5793,5871,5950,6073,6166,6262,6368,6462", "endColumns": "130,102,110,108,79,82,75,84,84,107,101,85,85,90,108,77,78,122,92,95,105,93,99", "endOffsets": "4474,4577,4688,4797,4877,4960,5036,5121,5206,5314,5416,5502,5588,5679,5788,5866,5945,6068,6161,6257,6363,6457,6557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,183,243,316,374,436,493,560,626,686,743", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "121,178,238,311,369,431,488,555,621,681,738,801"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16415,17130,17257,17317,17390,17643,18144,18324,18538,18822,19108,19364", "endColumns": "70,56,59,72,57,61,56,66,65,59,56,62", "endOffsets": "16481,17182,17312,17385,17443,17700,18196,18386,18599,18877,19160,19422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,186,250,323,388,449,512,573,633,701,765,826,884,950,1012,1077,1135,1202,1261,1331,1404,1469,1540,1612,1675,1720,1766,1828,1890,1943,2005,2077,2144,2214", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "110,181,245,318,383,444,507,568,628,696,760,821,879,945,1007,1072,1130,1197,1256,1326,1399,1464,1535,1607,1670,1715,1761,1823,1885,1938,2000,2072,2139,2209,2278"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,345,346,354,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16355,16570,16798,16931,17004,17069,17519,17582,17705,17765,17833,17897,17958,18016,18082,18201,18266,18471,19235,19294,20145,20285,20350,23366,23438,24438,25150,25196,25309,25802,25855,26444,26516,27053,27123", "endColumns": "59,70,63,72,64,60,62,60,59,67,63,60,57,65,61,64,57,66,58,69,72,64,70,71,62,44,45,61,61,52,61,71,66,69,68", "endOffsets": "16410,16636,16857,16999,17064,17125,17577,17638,17760,17828,17892,17953,18011,18077,18139,18261,18319,18533,19289,19359,20213,20345,20416,23433,23496,24478,25191,25253,25366,25850,25912,26511,26578,27118,27187"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2858,2921,2982,3049,3118,3856,3946,4053,8863,8914,9128,9553,9763,9821,9899,9960,10017,10073,10132,10190,10244,10330,10386,10444,10498,10563,10656,10730,10802,10882,10956,11034,11154,11217,11280,11379,11456,11530,11580,11631,11697,11761,11829,11900,11972,12033,12104,12171,12231,12319,12399,12462,12545,12630,12704,12769,12845,12893,12967,13031,13107,13185,13247,13311,13374,13440,13520,13600,13676,13757,13811,14214,14629,14704,14844", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "292,2916,2977,3044,3113,3190,3941,4048,4121,8909,8971,9201,9607,9816,9894,9955,10012,10068,10127,10185,10239,10325,10381,10439,10493,10558,10651,10725,10797,10877,10951,11029,11149,11212,11275,11374,11451,11525,11575,11626,11692,11756,11824,11895,11967,12028,12099,12166,12226,12314,12394,12457,12540,12625,12699,12764,12840,12888,12962,13026,13102,13180,13242,13306,13369,13435,13515,13595,13671,13752,13806,13861,14278,14699,14772,14909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,196", "endColumns": "71,68,70", "endOffsets": "122,191,262"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4276,8716,9057", "endColumns": "71,68,70", "endOffsets": "4343,8780,9123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "7426", "endColumns": "107", "endOffsets": "7529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,67", "endOffsets": "262,330"}, "to": {"startLines": "106,512", "startColumns": "4,4", "startOffsets": "9492,39493", "endColumns": "60,71", "endOffsets": "9548,39560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,130,214,279,345,405,467", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "125,209,274,340,400,462,524"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "15015,15090,15174,15239,15305,15365,15427", "endColumns": "74,83,64,65,59,61,61", "endOffsets": "15085,15169,15234,15300,15360,15422,15484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,180,241,310,381,454,527,605,674,745,817,896,947,1019,1078,1196,1318,1413,1498,1580,1664,1750,1854,1942,2028,2147,2232,2331,2499,2666,2754,2844,2946,3025,3082,3141,3216,3294,3371,3437,3515,3592,3677,3798,3861,3923,3984,4074,4144,4217,4276,4345,4413,4476,4564,4645,4736,4817,4882,4953,5017,5088,5144,5227,5303,5387,5486,5544,5621,5705,5806,5873,5936,6016,6066,6117,6175,6244,6362,6527,6710,6789,6849,6914,6973,7054,7139,7212,7280,7345,7414,7477,7552,7630,7714,7779,7842,7892,8005,8070,8123,8191,8253,8331,8397,8482,8568,8646,8717,8784,8847,8907", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "109,175,236,305,376,449,522,600,669,740,812,891,942,1014,1073,1191,1313,1408,1493,1575,1659,1745,1849,1937,2023,2142,2227,2326,2494,2661,2749,2839,2941,3020,3077,3136,3211,3289,3366,3432,3510,3587,3672,3793,3856,3918,3979,4069,4139,4212,4271,4340,4408,4471,4559,4640,4731,4812,4877,4948,5012,5083,5139,5222,5298,5382,5481,5539,5616,5700,5801,5868,5931,6011,6061,6112,6170,6239,6357,6522,6705,6784,6844,6909,6968,7049,7134,7207,7275,7340,7409,7472,7547,7625,7709,7774,7837,7887,8000,8065,8118,8186,8248,8326,8392,8477,8563,8641,8712,8779,8842,8902,8996"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,347,365,367,370,374,375,376,379,381,383,384,385,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,442,446,456,457,458,459,460,461,462,463,464,473,474,475,476,477,478,479,480,481,482,483,484,485,486,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15922,15981,16047,20899,22017,22088,22161,23554,24483,24596,24667,26583,27883,27988,28176,28439,28557,28679,28901,29076,29235,29319,29405,29509,29597,29683,29802,29887,29986,30154,30321,30486,30576,30678,30757,30814,30873,30948,31026,31103,31169,31247,31324,31409,31530,31593,31655,31716,31806,31876,31949,32008,32077,32145,32208,32704,32785,32876,32957,33022,33093,33157,33228,33284,33367,33443,33527,33626,33684,33761,33845,33946,34114,34344,35242,35292,35343,35401,35470,35588,35753,35936,36015,36542,36607,36666,36747,36832,36905,36973,37038,37107,37170,37245,37323,37407,37472,37597,37647,37760,37825,37878,37946,38008,38086,38152,38237,38323,38401,38472,38539,38602,38662", "endColumns": "58,65,60,68,70,72,72,77,68,70,71,78,50,71,58,117,121,94,84,81,83,85,103,87,85,118,84,98,167,166,87,89,101,78,56,58,74,77,76,65,77,76,84,120,62,61,60,89,69,72,58,68,67,62,87,80,90,80,64,70,63,70,55,82,75,83,98,57,76,83,100,66,62,79,49,50,57,68,117,164,182,78,59,64,58,80,84,72,67,64,68,62,74,77,83,64,62,49,112,64,52,67,61,77,65,84,85,77,70,66,62,59,93", "endOffsets": "15976,16042,16103,20963,22083,22156,22229,23627,24547,24662,24734,26657,27929,28055,28230,28552,28674,28769,28981,29153,29314,29400,29504,29592,29678,29797,29882,29981,30149,30316,30404,30571,30673,30752,30809,30868,30943,31021,31098,31164,31242,31319,31404,31525,31588,31650,31711,31801,31871,31944,32003,32072,32140,32203,32291,32780,32871,32952,33017,33088,33152,33223,33279,33362,33438,33522,33621,33679,33756,33840,33941,34008,34172,34419,35287,35338,35396,35465,35583,35748,35931,36010,36070,36602,36661,36742,36827,36900,36968,37033,37102,37165,37240,37318,37402,37467,37530,37642,37755,37820,37873,37941,38003,38081,38147,38232,38318,38396,38467,38534,38597,38657,38751"}}]}]}