{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-nl/values-nl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,85", "endOffsets": "136,222"}, "to": {"startLines": "509,510", "startColumns": "4,4", "startOffsets": "49461,49547", "endColumns": "85,85", "endOffsets": "49542,49628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,437,542,649,734,838,958,1036,1112,1204,1298,1393,1487,1587,1681,1777,1872,1964,2056,2138,2249,2352,2451,2566,2680,2783,2938,16652", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "432,537,644,729,833,953,1031,1107,1199,1293,1388,1482,1582,1676,1772,1867,1959,2051,2133,2244,2347,2446,2561,2675,2778,2933,3036,16730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "31432", "endColumns": "87", "endOffsets": "31515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3478,3580,3682,3782,3882,3989,4093,17043", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "3575,3677,3777,3877,3984,4088,4207,17139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,223,305,395,482,563,643,780,870,971,1061,1190,1251,1379,1445,1645,1864,2020,2112,2213,2306,2400,2581,2678,2777,3030,3142,3280,3488,3714,3806,3935,4157,4252,4314,4381,4464,4560,4661,4741,4835,4922,5024,5232,5302,5378,5454,5590,5666,5756,5822,5911,5983,6053,6169,6256,6365,6464,6548,6639,6712,6802,6863,6970,7078,7187,7321,7386,7481,7581,7717,7789,7879,7987,8047,8109,8188,8295,8468,8777,9107,9205,9282,9378,9452,9563,9681,9769,9851,9924,10012,10095,10187,10308,10439,10506,10584,10644,10837,10906,10966,11047,11119,11197,11284,11430,11562,11685,11779,11860,11949,12025", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "125,218,300,390,477,558,638,775,865,966,1056,1185,1246,1374,1440,1640,1859,2015,2107,2208,2301,2395,2576,2673,2772,3025,3137,3275,3483,3709,3801,3930,4152,4247,4309,4376,4459,4555,4656,4736,4830,4917,5019,5227,5297,5373,5449,5585,5661,5751,5817,5906,5978,6048,6164,6251,6360,6459,6543,6634,6707,6797,6858,6965,7073,7182,7316,7381,7476,7576,7712,7784,7874,7982,8042,8104,8183,8290,8463,8772,9102,9200,9277,9373,9447,9558,9676,9764,9846,9919,10007,10090,10182,10303,10434,10501,10579,10639,10832,10901,10961,11042,11114,11192,11279,11425,11557,11680,11774,11855,11944,12020,12178"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,348,366,368,371,375,376,377,380,382,384,385,386,387,388,389,390,391,392,393,394,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,443,447,457,458,459,460,461,462,463,464,465,474,475,476,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18168,18243,18336,23896,25824,25911,25992,28051,29337,29476,29577,32191,34018,34147,34399,34707,34907,35126,35445,35635,35839,35932,36026,36207,36304,36403,36656,36768,36906,37114,37340,37518,37647,37869,37964,38026,38093,38176,38272,38373,38453,38547,38634,38736,38944,39014,39090,39166,39302,39378,39468,39534,39623,39695,39765,40319,40406,40515,40614,40698,40789,40862,40952,41013,41120,41228,41337,41471,41536,41631,41731,41867,42158,42495,44185,44245,44307,44386,44493,44666,44975,45305,45403,46039,46135,46209,46320,46438,46526,46608,46681,46769,46852,46944,47065,47196,47263,47419,47479,47672,47741,47801,47882,47954,48032,48119,48265,48397,48520,48614,48695,48784,48860", "endColumns": "74,92,81,89,86,80,79,136,89,100,89,128,60,127,65,199,218,155,91,100,92,93,180,96,98,252,111,137,207,225,91,128,221,94,61,66,82,95,100,79,93,86,101,207,69,75,75,135,75,89,65,88,71,69,115,86,108,98,83,90,72,89,60,106,107,108,133,64,94,99,135,71,89,107,59,61,78,106,172,308,329,97,76,95,73,110,117,87,81,72,87,82,91,120,130,66,77,59,192,68,59,80,71,77,86,145,131,122,93,80,88,75,157", "endOffsets": "18238,18331,18413,23981,25906,25987,26067,28183,29422,29572,29662,32315,34074,34270,34460,34902,35121,35277,35532,35731,35927,36021,36202,36299,36398,36651,36763,36901,37109,37335,37427,37642,37864,37959,38021,38088,38171,38267,38368,38448,38542,38629,38731,38939,39009,39085,39161,39297,39373,39463,39529,39618,39690,39760,39876,40401,40510,40609,40693,40784,40857,40947,41008,41115,41223,41332,41466,41531,41626,41726,41862,41934,42243,42598,44240,44302,44381,44488,44661,44970,45300,45398,45475,46130,46204,46315,46433,46521,46603,46676,46764,46847,46939,47060,47191,47258,47336,47474,47667,47736,47796,47877,47949,48027,48114,48260,48392,48515,48609,48690,48779,48855,49013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9685,10531,10632,10743", "endColumns": "102,100,110,98", "endOffsets": "9783,10627,10738,10837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,137,217", "endColumns": "81,79,78", "endOffsets": "132,212,291"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4687,9984,10363", "endColumns": "81,79,78", "endOffsets": "4764,10059,10437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,280,377,476,561,637,733,820,909,974,1039,1120,1203,1280,1364,1434", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "192,275,372,471,556,632,728,815,904,969,1034,1115,1198,1275,1359,1429,1549"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4512,4604,9788,9885,10064,10982,11058,15874,15961,16131,16196,16488,16569,16894,49187,49271,49341", "endColumns": "91,82,96,98,84,75,95,86,88,64,64,80,82,76,83,69,119", "endOffsets": "4599,4682,9880,9979,10144,11053,11149,15956,16045,16191,16256,16564,16647,16966,49266,49336,49456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3041,3127,3209,3286,3384,4212,4309,4431,10149,10209,10442,10903,11154,11217,11310,11372,11438,11496,11569,11633,11689,11811,11868,11930,11986,12062,12196,12281,12360,12458,12544,12630,12768,12849,12928,13052,13142,13219,13276,13327,13393,13471,13554,13625,13701,13776,13855,13928,13999,14108,14202,14280,14369,14459,14533,14614,14701,14754,14833,14900,14981,15065,15127,15191,15254,15325,15433,15545,15647,15758,15819,16261,16735,16818,16971", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "314,3122,3204,3281,3379,3473,4304,4426,4507,10204,10268,10526,10977,11212,11305,11367,11433,11491,11564,11628,11684,11806,11863,11925,11981,12057,12191,12276,12355,12453,12539,12625,12763,12844,12923,13047,13137,13214,13271,13322,13388,13466,13549,13620,13696,13771,13850,13923,13994,14103,14197,14275,14364,14454,14528,14609,14696,14749,14828,14895,14976,15060,15122,15186,15249,15320,15428,15540,15642,15753,15814,15869,16337,16813,16889,17038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,192,254,335,402,476,535,612,682,750,811", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "126,187,249,330,397,471,530,607,677,745,806,873"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18772,19566,19709,19771,19852,20146,20687,20876,21127,21443,21793,22087", "endColumns": "75,60,61,80,66,73,58,76,69,67,60,66", "endOffsets": "18843,19622,19766,19847,19914,20215,20741,20948,21192,21506,21849,22149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,246,313,380,447,524", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "139,241,308,375,442,519,596"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17144,17233,17335,17402,17469,17536,17613", "endColumns": "88,101,66,66,66,76,76", "endOffsets": "17228,17330,17397,17464,17531,17608,17685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,325,423,658,704,774,873,945,1213,1273,1362,1426,1481,1545,1624,1719,2056,2127,2193,2246,2299,2391,2524,2643,2700,2784,2863,2945,3012,3108,3434,3511,3589,3657,3717,3781,3841,3914,4004,4102,4205,4291,4389,4489,4563,4642,4729,4948,5180,5298,5428,5493,6232,6334,6398", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "160,320,418,653,699,769,868,940,1208,1268,1357,1421,1476,1540,1619,1714,2051,2122,2188,2241,2294,2386,2519,2638,2695,2779,2858,2940,3007,3103,3429,3506,3584,3652,3712,3776,3836,3909,3999,4097,4200,4286,4384,4484,4558,4637,4724,4943,5175,5293,5423,5488,6227,6329,6393,6448"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,341,342,343,344,345,351,354,359,361,362,363,364,367,369,370,374,378,379,381,383,395,420,421,422,423,424,442,449,450,451,452,454,455,456,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23528,23638,23798,23986,25120,25166,25236,25335,25407,25675,25735,27742,27996,28188,28498,28577,28949,29667,29738,30297,31232,31520,31612,31745,31864,31921,32505,32761,33197,33338,33434,33760,33837,34079,34275,34335,34647,35282,35355,35537,35736,37432,39881,39979,40079,40153,40232,41939,42660,42892,43010,43140,43280,44019,44121,45984", "endColumns": "109,159,97,234,45,69,98,71,267,59,88,63,54,63,78,94,336,70,65,52,52,91,132,118,56,83,78,81,66,95,325,76,77,67,59,63,59,72,89,97,102,85,97,99,73,78,86,218,231,117,129,64,738,101,63,54", "endOffsets": "23633,23793,23891,24216,25161,25231,25330,25402,25670,25730,25819,27801,28046,28247,28572,28667,29281,29733,29799,30345,31280,31607,31740,31859,31916,32000,32579,32838,33259,33429,33755,33832,33910,34142,34330,34394,34702,35350,35440,35630,35834,37513,39974,40074,40148,40227,40314,42153,42887,43005,43135,43200,44014,44116,44180,46034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,267,348,494,663,743", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "172,262,343,489,658,738,815"}, "to": {"startLines": "92,100,171,175,505,511,512", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9613,10273,16050,16342,49018,49633,49713", "endColumns": "71,89,80,145,168,79,76", "endOffsets": "9680,10358,16126,16483,49182,49708,49785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "106,513", "startColumns": "4,4", "startOffsets": "10842,49790", "endColumns": "60,75", "endOffsets": "10898,49861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,212,347,495,606,695,788,861,953,1045,1158,1268,1360,1452,1553,1669,1754,1836,2031,2125,2233,2346,2457", "endColumns": "156,134,147,110,88,92,72,91,91,112,109,91,91,100,115,84,81,194,93,107,112,110,143", "endOffsets": "207,342,490,601,690,783,856,948,1040,1153,1263,1355,1447,1548,1664,1749,1831,2026,2120,2228,2341,2452,2596"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4769,4926,5061,5209,5320,5409,5502,5575,5667,5759,5872,5982,6074,6166,6267,6383,6468,6550,6745,6839,6947,7060,7171", "endColumns": "156,134,147,110,88,92,72,91,91,112,109,91,91,100,115,84,81,194,93,107,112,110,143", "endOffsets": "4921,5056,5204,5315,5404,5497,5570,5662,5754,5867,5977,6069,6161,6262,6378,6463,6545,6740,6834,6942,7055,7166,7310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,333,411,469,533,588,663,743,824,921,1018,1103,1182,1264,1352,1450,1541,1608,1696,1785,1871,1978,2060,2148,2222,2317,2390,2481,2569,2653,2735,2835,2904,2979,3089,3191,3263,3328,4090,4828,4905,5022,5123,5178,5281,5379,5444,5533,5622,5679,5760,5812,5892,6006,6077,6150,6217,6283,6332,6415,6512,6596,6643,6692,6762,6820,6886,7078,7245,7375,7440,7522,7607,7707,7791,7884,7959,8045,8119,8222,8309,8404,8457,8597,8651,8708,8783,8855,8930,8997,9067,9160,9235,9287", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "119,197,259,328,406,464,528,583,658,738,819,916,1013,1098,1177,1259,1347,1445,1536,1603,1691,1780,1866,1973,2055,2143,2217,2312,2385,2476,2564,2648,2730,2830,2899,2974,3084,3186,3258,3323,4085,4823,4900,5017,5118,5173,5276,5374,5439,5528,5617,5674,5755,5807,5887,6001,6072,6145,6212,6278,6327,6410,6507,6591,6638,6687,6757,6815,6881,7073,7240,7370,7435,7517,7602,7702,7786,7879,7954,8040,8114,8217,8304,8399,8452,8592,8646,8703,8778,8850,8925,8992,9062,9155,9230,9282,9360"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,349,350,352,353,357,358,360,365,372,373,444,445,446,448,453,466,467,468,469,470,471,472,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17690,17759,17837,17899,17968,18046,18104,18418,18473,18548,18628,18848,19021,19118,19271,19627,19919,20953,21197,21288,21355,21511,21600,21686,21854,22154,22242,22316,22411,22484,22575,22663,22747,22829,22929,23075,23316,23426,24221,24293,24358,26072,26810,26887,27004,27105,27160,27263,27361,27426,27515,27604,27661,28252,28304,28384,28672,28743,28816,28883,29427,29804,29887,29984,30068,30115,30414,30484,30542,30608,30800,30967,31285,31350,32320,32405,32584,32668,33036,33111,33264,33915,34465,34552,42248,42301,42441,42603,43205,45480,45552,45627,45694,45764,45857,45932,47341", "endColumns": "68,77,61,68,77,57,63,54,74,79,80,96,96,84,78,81,87,97,90,66,87,88,85,106,81,87,73,94,72,90,87,83,81,99,68,74,109,101,71,64,761,737,76,116,100,54,102,97,64,88,88,56,80,51,79,113,70,72,66,65,48,82,96,83,46,48,69,57,65,191,166,129,64,81,84,99,83,92,74,85,73,102,86,94,52,139,53,56,74,71,74,66,69,92,74,51,77", "endOffsets": "17754,17832,17894,17963,18041,18099,18163,18468,18543,18623,18704,18940,19113,19198,19345,19704,20002,21046,21283,21350,21438,21595,21681,21788,21931,22237,22311,22406,22479,22570,22658,22742,22824,22924,22993,23145,23421,23523,24288,24353,25115,26805,26882,26999,27100,27155,27258,27356,27421,27510,27599,27656,27737,28299,28379,28493,28738,28811,28878,28944,29471,29882,29979,30063,30110,30159,30479,30537,30603,30795,30962,31092,31345,31427,32400,32500,32663,32756,33106,33192,33333,34013,34547,34642,42296,42436,42490,42655,43275,45547,45622,45689,45759,45852,45927,45979,47414"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8336", "endColumns": "142", "endOffsets": "8474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,194,262,346,417,478,550,617,680,748,818,883,946,1020,1084,1151,1214,1290,1355,1441,1518,1598,1684,1791,1874,1925,1978,2058,2122,2187,2257,2358,2443,2540", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "113,189,257,341,412,473,545,612,675,743,813,878,941,1015,1079,1146,1209,1285,1350,1436,1513,1593,1679,1786,1869,1920,1973,2053,2117,2182,2252,2353,2438,2535,2631"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,346,347,355,356", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18709,18945,19203,19350,19434,19505,20007,20079,20220,20283,20351,20421,20486,20549,20623,20746,20813,21051,21936,22001,22998,23150,23230,27806,27913,29286,30164,30217,30350,31097,31162,32005,32106,32843,32940", "endColumns": "62,75,67,83,70,60,71,66,62,67,69,64,62,73,63,66,62,75,64,85,76,79,85,106,82,50,52,79,63,64,69,100,84,96,95", "endOffsets": "18767,19016,19266,19429,19500,19561,20074,20141,20278,20346,20416,20481,20544,20618,20682,20808,20871,21122,21996,22082,23070,23225,23311,27908,27991,29332,30212,30292,30409,31157,31227,32101,32186,32935,33031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7315,7423,7574,7702,7813,7980,8107,8230,8479,8657,8763,8932,9058,9221,9403,9471,9534", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "7418,7569,7697,7808,7975,8102,8225,8331,8652,8758,8927,9053,9216,9398,9466,9529,9608"}}]}]}