-- Merging decision tree log ---
application
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:18:5-50:19
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-8:19
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:41:5-70:19
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:41:5-70:19
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:7:5-81:19
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:7:5-81:19
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:14:5-71:19
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:14:5-71:19
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0428afdadcc879d83fdc5d5287f30b42\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0428afdadcc879d83fdc5d5287f30b42\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\478ca9e2052d57875e7fb8ad0b98e590\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\478ca9e2052d57875e7fb8ad0b98e590\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:20:5-75:19
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:20:5-75:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:19:5-40:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:21:5-51:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29c0f483e0f861c9dab1a091d39b0fa\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29c0f483e0f861c9dab1a091d39b0fa\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a721dc70fb89cd95c8670b4e603fce\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a721dc70fb89cd95c8670b4e603fce\transformed\jetified-glide-4.12.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\155924c14390b784033d6aa42402c6d2\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\155924c14390b784033d6aa42402c6d2\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ddf3879ea069ad2d4320356823a093\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ddf3879ea069ad2d4320356823a093\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08ca0f06aa4b5b90742b6c98019f8970\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08ca0f06aa4b5b90742b6c98019f8970\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c42102cef3cc1994f43698022646b4b6\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c42102cef3cc1994f43698022646b4b6\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e23c6d593650b610a20e7c8db49fffbf\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e23c6d593650b610a20e7c8db49fffbf\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cad27e220c550149644496c5e1e613\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cad27e220c550149644496c5e1e613\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:5:5-6:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d895c867e9f70e08b569510e2232512\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d895c867e9f70e08b569510e2232512\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59fe94685badf63753d4e21ffd54cd46\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59fe94685badf63753d4e21ffd54cd46\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:14:5-15:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8dea44fce8ea7b9a78a583e94d8d4be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8dea44fce8ea7b9a78a583e94d8d4be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea1f4559ecc9748c6d48d324c360116\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea1f4559ecc9748c6d48d324c360116\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:18-44
	android:name
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml
manifest
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:1-51:12
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml:1:1-9:12
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-30:12
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:fluttertoast] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5697cc6e711b9e504ce788bb3009bd69\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:2:1-7:12
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:2:1-72:12
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce166f25e9d97c81bb8f7fb2b30e4802\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:2:1-83:12
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:2:1-73:12
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b5ea8fdc12b06a07ad5b55d131d3c0\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4269eea105874c32d4a61f687b8bce2f\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcc44dfa36f91f15b254ddd6585e626\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37479f386f88fe8fb00ca5629060d0f7\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0428afdadcc879d83fdc5d5287f30b42\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\478ca9e2052d57875e7fb8ad0b98e590\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd80454f8567ef6b6a35dcbc879f0cdf\transformed\webkit-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:2:1-77:12
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:9:1-42:12
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b0b83164639c97a93dd4de213a79e6b\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4294a87ae4a4cd5a2ff97e422e1e035\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6f4d17baeefaf6f89069f9800733096\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f374d5614d224cb5b3407598867637a8\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc186507977822d18a2a35c8a5f1532\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58c938b4008a8437bd5102d5b661f61b\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2d2541478bfd0ad054cfe9c2a0516e9\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb4591ce912292879a316e755b2fcbb2\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c28a36dc3e5840e533e10d22bd1f1b5d\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f0d996a262b7c3a7ee3410ae702e6a4\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0e48d822931cae5ebd7ecf9ef0f8678\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1684633928d7b637357034c42f2d1dcc\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\532fa401603d72fd366ed8a6ada2a852\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e4942f31fedd7a30bdb4acf285c426a\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\389e1c0fa99fcbc403c98921b33462a1\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccc6808e7ebaa65c9d6fdcfe51b4f635\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e71fe2906ad0b4626483522ed649823\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\615fe9b0284043665050576a70382668\transformed\jetified-material3-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a48374d1670f15c85e0740ec6bde038\transformed\jetified-material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453cb03900b10f049053c0ab749e1f20\transformed\jetified-material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db42fa3bcb631e31685f05f248244b52\transformed\jetified-material-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\127a1f7114e5defb962ac1b49edc2e9f\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4627cbae6730189e4684b58a6bb26f77\transformed\jetified-animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\392950781226571353c38337c270b895\transformed\jetified-animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a50597f18023257f7cd50564747b48\transformed\jetified-foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3339fb9ba8563db6c20567a8e88316b1\transformed\jetified-foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce1f6ac9a2fadef8222999c5d984e3a5\transformed\jetified-ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b060f5ef24c4eca5b865a80d3d57d4be\transformed\jetified-ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29d5b6eed37958d080c023694358570d\transformed\jetified-ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bad8c1b2483ccc57af4d9481850a0c78\transformed\jetified-ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6987a55f5778e4434e5b2fdd5d7a807d\transformed\jetified-ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa99d3d3314a13bc621d7d07b3a8804\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:9:1-53:12
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29c0f483e0f861c9dab1a091d39b0fa\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3f59018ccdc8e8d37fe358ed01ca2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0285a2f108ba12282787b3371a910a5b\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd58c1c841dca07aaf427066d1d44cd\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d511b649e7a492c232c547cb4866ae91\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f042bf793e880740ecf2ce2f5f5e85d\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a721dc70fb89cd95c8670b4e603fce\transformed\jetified-glide-4.12.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\155924c14390b784033d6aa42402c6d2\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f39c999c1c6c2b94691782dbbd2443d\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e92b26e6092e881b549443ccbcf2e32\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\240e3f1e5055ee3bc9f35ef9c67922a7\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f1e1a05d7f43db3a8fb665929a845\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e22e4877551e751d62335d5f3b6bfd\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa022ad47a118dc717a32b86f04ff7af\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bea573ba2f3844155440c03340204e9a\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ddf3879ea069ad2d4320356823a093\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08ca0f06aa4b5b90742b6c98019f8970\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c42102cef3cc1994f43698022646b4b6\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e23c6d593650b610a20e7c8db49fffbf\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\386efa628c1895600efe51d9a6910eb7\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cad27e220c550149644496c5e1e613\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ee9ce7b43123aece3c2f5f61b29775\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a477cf9f8a5631dda21ffbf31820155\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50768c25852f44e4db4d086443ad11e3\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011eed59a39dfad65b3de3c6d83ac5c5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c18a50a54612352fc30a1ed7ce8ed21\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe1e8285f981c383fb37c300e901bf3\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac27722eed305b4a6ed8226df044229\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1b320c90cabbd70c6ed8034aa360b9\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2b0beeea041a401446e6152ba22bb3\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c4abf1beb30c227c67fc9fb4d151f1f\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d1a570904efc0c94943aa1b4e95699a\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eedefc32728cb67d843d3f687bea3be7\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\683621327d18465b26af7856cdcf5906\transformed\jetified-runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe2a103bd9485c585e169a132c84fe5\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df1377329b74ec34a0bfe87d79195d99\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483719ee356090a2c0b973f11cbcbe5e\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1749122a4709f1609c9a6d273cf6f38\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d895c867e9f70e08b569510e2232512\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\defe789f7d80945d9bbe8d7b41977d63\transformed\fragment-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acd02b501d1a35f200cb7a8c2cfc65bd\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33253b4c4702e41111e7271f75651bc0\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e478d82ddfb302e3035dc5aee683a84d\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5c8478f363d792d0d05486db0c3ad9e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ac1f6bf6cedc267b50b07c43bbe1a8a\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ce948366a2a7d2b5a0a2cd115c63e21\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c051fd24282ffdee7568495f6eade7\transformed\jetified-ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9b965b795f19d6a1458aed62232877c\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0bf099005b7d2bf32d932dee0be327d\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc5fbf0c019971191d06a59f571dfc32\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81919c478a47198f873713e2563520b9\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba5f553b60a419c5addc2ac9e0ee46cf\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59fe94685badf63753d4e21ffd54cd46\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:9:1-17:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57640b9e51086638a451d6ac09c004f7\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fcff2358be7e7b1bad44d7ae26d43d7\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec956983044ca4de0784d205236c9224\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\369184edb4d2b0e21746786d0d863fe8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7827d81ef19cb0592d690812adb1e9a4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bfd38d60c82b75a85439ece8e7206b7\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdd21a5f97cdb08a59d117ebadacb493\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c2f2fb90581b56297420deec184a81\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a16e68e2d12a488445ac845222928\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54cd009b9907ff958b54d31b1bb701ee\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d92ddef166c30ee961f4cf30ea4bf8c\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82b75b5070513e80e352b0e3de8b5cd\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cadba644d51df111f448e2fa19df96b6\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2956abb8ede496ae9dd8f03d119e44\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8dea44fce8ea7b9a78a583e94d8d4be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea1f4559ecc9748c6d48d324c360116\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a89a9265beed5c8ffd8236eac8ef3b\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3a9bcfaf63091265a95cf2e77ef5021\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa353e407a744b82d6c3b62218cf99e8\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6252e94016b0dd91312f7407abaed581\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5266ad872eeeb29c947cc56749f6dc19\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9ac13a2c8358617f56b214c134de460\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc02eeb55f88c2edbf11dd2edab5bb6d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d91d771b12df43c3a382e760c294e97\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ca9093d08152f5ea09b24d63d0fe841\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7da21d95d3b96bad7c3951f833bf9750\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cf10244bf77ddb27582e66442c66db6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a592547d0ea4b81244892f5bec8517d6\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:2:1-21:12
	package
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:3:5-29
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:8:5-67
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:8:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6f4d17baeefaf6f89069f9800733096\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:7:5-79
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6f4d17baeefaf6f89069f9800733096\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
	android:name
		ADDED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
uses-sdk
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_timezone] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:stripe_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\stripe_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:razorpay_flutter] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\razorpay_flutter\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\fluttertoast\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sign_in_with_apple\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5697cc6e711b9e504ce788bb3009bd69\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:checkout:1.6.41] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5697cc6e711b9e504ce788bb3009bd69\transformed\jetified-checkout-1.6.41\AndroidManifest.xml:5:5-44
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:6:5-44
MERGED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:6:5-44
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce166f25e9d97c81bb8f7fb2b30e4802\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-android:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce166f25e9d97c81bb8f7fb2b30e4802\transformed\jetified-stripe-android-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b5ea8fdc12b06a07ad5b55d131d3c0\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-appcompat:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0b5ea8fdc12b06a07ad5b55d131d3c0\transformed\jetified-accompanist-themeadapter-appcompat-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4269eea105874c32d4a61f687b8bce2f\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4269eea105874c32d4a61f687b8bce2f\transformed\jetified-accompanist-themeadapter-material-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcc44dfa36f91f15b254ddd6585e626\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-material3:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4fcc44dfa36f91f15b254ddd6585e626\transformed\jetified-accompanist-themeadapter-material3-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37479f386f88fe8fb00ca5629060d0f7\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-themeadapter-core:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\37479f386f88fe8fb00ca5629060d0f7\transformed\jetified-accompanist-themeadapter-core-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0428afdadcc879d83fdc5d5287f30b42\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0428afdadcc879d83fdc5d5287f30b42\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_facebook_auth] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_facebook_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\66d7cd215e20ecf226f37a20eb89ac2a\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\478ca9e2052d57875e7fb8ad0b98e590\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\478ca9e2052d57875e7fb8ad0b98e590\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_maps_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geocoding_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\google_sign_in_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\sqflite_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd80454f8567ef6b6a35dcbc879f0cdf\transformed\webkit-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dd80454f8567ef6b6a35dcbc879f0cdf\transformed\webkit-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:13:5-44
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b0b83164639c97a93dd4de213a79e6b\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b0b83164639c97a93dd4de213a79e6b\transformed\jetified-stripe-ui-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4294a87ae4a4cd5a2ff97e422e1e035\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:payments-model:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4294a87ae4a4cd5a2ff97e422e1e035\transformed\jetified-payments-model-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6f4d17baeefaf6f89069f9800733096\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:stripe-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6f4d17baeefaf6f89069f9800733096\transformed\jetified-stripe-core-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f374d5614d224cb5b3407598867637a8\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f374d5614d224cb5b3407598867637a8\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc186507977822d18a2a35c8a5f1532\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbc186507977822d18a2a35c8a5f1532\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58c938b4008a8437bd5102d5b661f61b\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58c938b4008a8437bd5102d5b661f61b\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2d2541478bfd0ad054cfe9c2a0516e9\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2d2541478bfd0ad054cfe9c2a0516e9\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb4591ce912292879a316e755b2fcbb2\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb4591ce912292879a316e755b2fcbb2\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c28a36dc3e5840e533e10d22bd1f1b5d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c28a36dc3e5840e533e10d22bd1f1b5d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f0d996a262b7c3a7ee3410ae702e6a4\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f0d996a262b7c3a7ee3410ae702e6a4\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0e48d822931cae5ebd7ecf9ef0f8678\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0e48d822931cae5ebd7ecf9ef0f8678\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1684633928d7b637357034c42f2d1dcc\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1684633928d7b637357034c42f2d1dcc\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\532fa401603d72fd366ed8a6ada2a852\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\532fa401603d72fd366ed8a6ada2a852\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e4942f31fedd7a30bdb4acf285c426a\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e4942f31fedd7a30bdb4acf285c426a\transformed\jetified-navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\389e1c0fa99fcbc403c98921b33462a1\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\389e1c0fa99fcbc403c98921b33462a1\transformed\jetified-activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccc6808e7ebaa65c9d6fdcfe51b4f635\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccc6808e7ebaa65c9d6fdcfe51b4f635\transformed\jetified-accompanist-systemuicontroller-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e71fe2906ad0b4626483522ed649823\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.pay.button:compose-pay-button:0.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e71fe2906ad0b4626483522ed649823\transformed\jetified-compose-pay-button-0.1.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\615fe9b0284043665050576a70382668\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\615fe9b0284043665050576a70382668\transformed\jetified-material3-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a48374d1670f15c85e0740ec6bde038\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a48374d1670f15c85e0740ec6bde038\transformed\jetified-material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453cb03900b10f049053c0ab749e1f20\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\453cb03900b10f049053c0ab749e1f20\transformed\jetified-material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db42fa3bcb631e31685f05f248244b52\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db42fa3bcb631e31685f05f248244b52\transformed\jetified-material-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\127a1f7114e5defb962ac1b49edc2e9f\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-flowlayout:0.34.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\127a1f7114e5defb962ac1b49edc2e9f\transformed\jetified-accompanist-flowlayout-0.34.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4627cbae6730189e4684b58a6bb26f77\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4627cbae6730189e4684b58a6bb26f77\transformed\jetified-animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\392950781226571353c38337c270b895\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\392950781226571353c38337c270b895\transformed\jetified-animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a50597f18023257f7cd50564747b48\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\19a50597f18023257f7cd50564747b48\transformed\jetified-foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3339fb9ba8563db6c20567a8e88316b1\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3339fb9ba8563db6c20567a8e88316b1\transformed\jetified-foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce1f6ac9a2fadef8222999c5d984e3a5\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce1f6ac9a2fadef8222999c5d984e3a5\transformed\jetified-ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b060f5ef24c4eca5b865a80d3d57d4be\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b060f5ef24c4eca5b865a80d3d57d4be\transformed\jetified-ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29d5b6eed37958d080c023694358570d\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29d5b6eed37958d080c023694358570d\transformed\jetified-ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bad8c1b2483ccc57af4d9481850a0c78\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bad8c1b2483ccc57af4d9481850a0c78\transformed\jetified-ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6987a55f5778e4434e5b2fdd5d7a807d\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6987a55f5778e4434e5b2fdd5d7a807d\transformed\jetified-ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa99d3d3314a13bc621d7d07b3a8804\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fa99d3d3314a13bc621d7d07b3a8804\transformed\jetified-ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29c0f483e0f861c9dab1a091d39b0fa\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e29c0f483e0f861c9dab1a091d39b0fa\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3f59018ccdc8e8d37fe358ed01ca2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2cb3f59018ccdc8e8d37fe358ed01ca2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0285a2f108ba12282787b3371a910a5b\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0285a2f108ba12282787b3371a910a5b\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd58c1c841dca07aaf427066d1d44cd\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd58c1c841dca07aaf427066d1d44cd\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d511b649e7a492c232c547cb4866ae91\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d511b649e7a492c232c547cb4866ae91\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f042bf793e880740ecf2ce2f5f5e85d\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-wallet:19.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9f042bf793e880740ecf2ce2f5f5e85d\transformed\jetified-play-services-wallet-19.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a721dc70fb89cd95c8670b4e603fce\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\56a721dc70fb89cd95c8670b4e603fce\transformed\jetified-glide-4.12.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\155924c14390b784033d6aa42402c6d2\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\155924c14390b784033d6aa42402c6d2\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f39c999c1c6c2b94691782dbbd2443d\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:hcaptcha:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f39c999c1c6c2b94691782dbbd2443d\transformed\jetified-hcaptcha-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e92b26e6092e881b549443ccbcf2e32\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6e92b26e6092e881b549443ccbcf2e32\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\240e3f1e5055ee3bc9f35ef9c67922a7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\240e3f1e5055ee3bc9f35ef9c67922a7\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f1e1a05d7f43db3a8fb665929a845\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\621f1e1a05d7f43db3a8fb665929a845\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e22e4877551e751d62335d5f3b6bfd\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99e22e4877551e751d62335d5f3b6bfd\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa022ad47a118dc717a32b86f04ff7af\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fa022ad47a118dc717a32b86f04ff7af\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bea573ba2f3844155440c03340204e9a\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bea573ba2f3844155440c03340204e9a\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ddf3879ea069ad2d4320356823a093\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03ddf3879ea069ad2d4320356823a093\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08ca0f06aa4b5b90742b6c98019f8970\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-identity:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08ca0f06aa4b5b90742b6c98019f8970\transformed\jetified-play-services-identity-18.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c42102cef3cc1994f43698022646b4b6\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c42102cef3cc1994f43698022646b4b6\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e23c6d593650b610a20e7c8db49fffbf\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e23c6d593650b610a20e7c8db49fffbf\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\386efa628c1895600efe51d9a6910eb7\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.stripe:attestation:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\386efa628c1895600efe51d9a6910eb7\transformed\jetified-attestation-21.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cad27e220c550149644496c5e1e613\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c2cad27e220c550149644496c5e1e613\transformed\jetified-integrity-1.4.0\AndroidManifest.xml:4:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ee9ce7b43123aece3c2f5f61b29775\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4ee9ce7b43123aece3c2f5f61b29775\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a477cf9f8a5631dda21ffbf31820155\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a477cf9f8a5631dda21ffbf31820155\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50768c25852f44e4db4d086443ad11e3\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50768c25852f44e4db4d086443ad11e3\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011eed59a39dfad65b3de3c6d83ac5c5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\011eed59a39dfad65b3de3c6d83ac5c5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c18a50a54612352fc30a1ed7ce8ed21\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c18a50a54612352fc30a1ed7ce8ed21\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe1e8285f981c383fb37c300e901bf3\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe1e8285f981c383fb37c300e901bf3\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac27722eed305b4a6ed8226df044229\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac27722eed305b4a6ed8226df044229\transformed\jetified-lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1b320c90cabbd70c6ed8034aa360b9\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4c1b320c90cabbd70c6ed8034aa360b9\transformed\jetified-lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2b0beeea041a401446e6152ba22bb3\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2b0beeea041a401446e6152ba22bb3\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c4abf1beb30c227c67fc9fb4d151f1f\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6c4abf1beb30c227c67fc9fb4d151f1f\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d1a570904efc0c94943aa1b4e95699a\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d1a570904efc0c94943aa1b4e95699a\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eedefc32728cb67d843d3f687bea3be7\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eedefc32728cb67d843d3f687bea3be7\transformed\jetified-runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\683621327d18465b26af7856cdcf5906\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\683621327d18465b26af7856cdcf5906\transformed\jetified-runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe2a103bd9485c585e169a132c84fe5\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fbe2a103bd9485c585e169a132c84fe5\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df1377329b74ec34a0bfe87d79195d99\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df1377329b74ec34a0bfe87d79195d99\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483719ee356090a2c0b973f11cbcbe5e\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\483719ee356090a2c0b973f11cbcbe5e\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1749122a4709f1609c9a6d273cf6f38\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b1749122a4709f1609c9a6d273cf6f38\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d895c867e9f70e08b569510e2232512\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d895c867e9f70e08b569510e2232512\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\defe789f7d80945d9bbe8d7b41977d63\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\defe789f7d80945d9bbe8d7b41977d63\transformed\fragment-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acd02b501d1a35f200cb7a8c2cfc65bd\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acd02b501d1a35f200cb7a8c2cfc65bd\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33253b4c4702e41111e7271f75651bc0\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\33253b4c4702e41111e7271f75651bc0\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e478d82ddfb302e3035dc5aee683a84d\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e478d82ddfb302e3035dc5aee683a84d\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5c8478f363d792d0d05486db0c3ad9e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c5c8478f363d792d0d05486db0c3ad9e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ac1f6bf6cedc267b50b07c43bbe1a8a\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ac1f6bf6cedc267b50b07c43bbe1a8a\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ce948366a2a7d2b5a0a2cd115c63e21\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ce948366a2a7d2b5a0a2cd115c63e21\transformed\jetified-lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c051fd24282ffdee7568495f6eade7\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\22c051fd24282ffdee7568495f6eade7\transformed\jetified-ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9b965b795f19d6a1458aed62232877c\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-viewbinding:1.6.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b9b965b795f19d6a1458aed62232877c\transformed\jetified-ui-viewbinding-1.6.8\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0bf099005b7d2bf32d932dee0be327d\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a0bf099005b7d2bf32d932dee0be327d\transformed\jetified-fragment-ktx-1.8.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc5fbf0c019971191d06a59f571dfc32\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc5fbf0c019971191d06a59f571dfc32\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81919c478a47198f873713e2563520b9\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\81919c478a47198f873713e2563520b9\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba5f553b60a419c5addc2ac9e0ee46cf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba5f553b60a419c5addc2ac9e0ee46cf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59fe94685badf63753d4e21ffd54cd46\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [com.facebook.android:facebook-bolts:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59fe94685badf63753d4e21ffd54cd46\transformed\jetified-facebook-bolts-18.0.3\AndroidManifest.xml:12:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57640b9e51086638a451d6ac09c004f7\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57640b9e51086638a451d6ac09c004f7\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fcff2358be7e7b1bad44d7ae26d43d7\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fcff2358be7e7b1bad44d7ae26d43d7\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec956983044ca4de0784d205236c9224\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec956983044ca4de0784d205236c9224\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\369184edb4d2b0e21746786d0d863fe8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\369184edb4d2b0e21746786d0d863fe8\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7827d81ef19cb0592d690812adb1e9a4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7827d81ef19cb0592d690812adb1e9a4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bfd38d60c82b75a85439ece8e7206b7\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bfd38d60c82b75a85439ece8e7206b7\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdd21a5f97cdb08a59d117ebadacb493\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cdd21a5f97cdb08a59d117ebadacb493\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c2f2fb90581b56297420deec184a81\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b8c2f2fb90581b56297420deec184a81\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a16e68e2d12a488445ac845222928\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b04a16e68e2d12a488445ac845222928\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54cd009b9907ff958b54d31b1bb701ee\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54cd009b9907ff958b54d31b1bb701ee\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d92ddef166c30ee961f4cf30ea4bf8c\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d92ddef166c30ee961f4cf30ea4bf8c\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82b75b5070513e80e352b0e3de8b5cd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e82b75b5070513e80e352b0e3de8b5cd\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cadba644d51df111f448e2fa19df96b6\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cadba644d51df111f448e2fa19df96b6\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2956abb8ede496ae9dd8f03d119e44\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f2956abb8ede496ae9dd8f03d119e44\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8dea44fce8ea7b9a78a583e94d8d4be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8dea44fce8ea7b9a78a583e94d8d4be\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea1f4559ecc9748c6d48d324c360116\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cea1f4559ecc9748c6d48d324c360116\transformed\jetified-gifdecoder-4.12.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a89a9265beed5c8ffd8236eac8ef3b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\21a89a9265beed5c8ffd8236eac8ef3b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3a9bcfaf63091265a95cf2e77ef5021\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3a9bcfaf63091265a95cf2e77ef5021\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa353e407a744b82d6c3b62218cf99e8\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa353e407a744b82d6c3b62218cf99e8\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6252e94016b0dd91312f7407abaed581\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6252e94016b0dd91312f7407abaed581\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5266ad872eeeb29c947cc56749f6dc19\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5266ad872eeeb29c947cc56749f6dc19\transformed\jetified-viewbinding-8.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9ac13a2c8358617f56b214c134de460\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9ac13a2c8358617f56b214c134de460\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc02eeb55f88c2edbf11dd2edab5bb6d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bc02eeb55f88c2edbf11dd2edab5bb6d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d91d771b12df43c3a382e760c294e97\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8d91d771b12df43c3a382e760c294e97\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ca9093d08152f5ea09b24d63d0fe841\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ca9093d08152f5ea09b24d63d0fe841\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7da21d95d3b96bad7c3951f833bf9750\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7da21d95d3b96bad7c3951f833bf9750\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cf10244bf77ddb27582e66442c66db6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cf10244bf77ddb27582e66442c66db6\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a592547d0ea4b81244892f5bec8517d6\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a592547d0ea4b81244892f5bec8517d6\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\debug\AndroidManifest.xml
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-86
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-87
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-81
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-83
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-92
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-84
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-83
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:5-91
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:5-92
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:5-93
	android:name
		ADDED from [:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:22-90
queries
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:10:5-39:15
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:8:5-12:15
MERGED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:8:5-12:15
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-11:15
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
MERGED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:10:5-18:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:15:5-17:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.VIEW+data:mimeType:*/*+data:scheme:*
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
action#android.intent.action.VIEW
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:12:21-62
data
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:14:13-16:38
	android:scheme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:16:17-35
	android:mimeType
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:15:17-39
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:host:pay+data:mimeType:*/*+data:scheme:upi
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
category#android.intent.category.BROWSABLE
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:13-74
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:21:23-71
intent#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
action#android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:29:21-62
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
action#android.intent.action.SEND
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
intent#action:name:rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
action#rzp.device_token.share
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
activity#com.razorpay.CheckoutActivity
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
	android:configChanges
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
intent-filter#action:name:android.intent.action.MAIN
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
provider#androidx.startup.InitializationProvider
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\********************************\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:56:13-31
	android:authorities
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
meta-data#com.razorpay.RazorpayInitializer
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
	android:value
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
activity#com.razorpay.MagicXActivity
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
	android:exported
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
	android:theme
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
meta-data#com.razorpay.plugin.googlepay_all
ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
	android:value
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
	android:name
		ADDED from [com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\866ef319e22d157f920149813f317a57\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
activity#com.stripe.android.paymentsheet.PaymentSheetActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
activity#com.stripe.android.paymentsheet.PaymentOptionsActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
activity#com.stripe.android.customersheet.CustomerSheetActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
activity#com.stripe.android.paymentsheet.addresselement.AddressElementActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
activity#com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
activity#com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
activity#com.stripe.android.paymentsheet.ui.SepaMandateActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
activity#com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
activity#com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
activity#com.stripe.android.paymentelement.embedded.form.FormActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
activity#com.stripe.android.paymentelement.embedded.manage.ManageActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
activity#com.stripe.android.link.LinkActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
	android:label
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
	android:windowSoftInputMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
	android:configChanges
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
activity#com.stripe.android.link.LinkForegroundActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
	android:launchMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
	android:configChanges
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
activity#com.stripe.android.link.LinkRedirectHandlerActivity
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
	android:launchMode
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
	android:autoRemoveFromRecents
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
	android:exported
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
	android:theme
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/${applicationId}+data:scheme:link-popup
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:complete+data:path:/com.velvete.ly+data:scheme:link-popup
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
category#android.intent.category.DEFAULT
ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:17-76
	android:name
		ADDED from [com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3bb70f56b8dc56c3bbe3100934d6f376\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:72:27-73
activity#com.stripe.android.ui.core.cardscan.CardScanActivity
ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
	android:exported
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
	android:name
		ADDED from [com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acce8c8b13ab9e6d00fa6b8e118f6c81\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
package#com.android.chrome
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
activity#com.stripe.android.view.PaymentAuthWebViewActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
activity#com.stripe.android.view.PaymentRelayActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
activity#com.stripe.android.payments.StripeBrowserLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
	android:launchMode
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
activity#com.stripe.android.payments.StripeBrowserProxyReturnActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
	android:launchMode
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/${applicationId}+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:payment_return_url+data:path:/com.velvete.ly+data:scheme:stripesdk
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
activity#com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
activity#com.stripe.android.googlepaylauncher.GooglePayLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
activity#com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
activity#com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
activity#com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity
ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
	android:exported
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
	android:theme
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
	android:name
		ADDED from [com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e06335898ad203880805ce7e55c35780\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
activity#com.stripe.android.stripe3ds2.views.ChallengeActivity
ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
	android:exported
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
	android:name
		ADDED from [com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99b6584c27c5573f4f81620b81ed317a\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
	android:launchMode
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/${applicationId}/cancel+data:path:/${applicationId}/success+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}+data:pathPrefix:/${applicationId}/authentication_return+data:pathPrefix:/${applicationId}/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:auth-redirect+data:host:link-accounts+data:host:link-accounts+data:host:link-accounts+data:host:link-native-accounts+data:host:native-redirect+data:path:/com.velvete.ly/cancel+data:path:/com.velvete.ly/success+data:pathPrefix:/com.velvete.ly+data:pathPrefix:/com.velvete.ly+data:pathPrefix:/com.velvete.ly/authentication_return+data:pathPrefix:/com.velvete.ly/authentication_return+data:scheme:stripe+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth+data:scheme:stripe-auth
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
activity#com.stripe.android.financialconnections.FinancialConnectionsSheetActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
activity#com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity
ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
	android:windowSoftInputMode
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
	android:exported
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
	android:name
		ADDED from [com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59f60e2aca0e952bec7a981b9c478940\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
package#com.facebook.katana
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
activity#com.facebook.FacebookActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
	android:configChanges
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:22:13-96
	android:theme
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:21:13-57
activity#com.facebook.CustomTabMainActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
activity#com.facebook.CustomTabActivity
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
	tools:node
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:28:13-31
	android:exported
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:27:13-36
	android:name
		ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:26:13-58
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.${applicationId}+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cct.com.velvete.ly+data:scheme:fbconnect
ADDED from [com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a32e9f9fc153aca81162380d39809829\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd9e4976a86f55ae0d3d388f4aa7e507\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
provider#com.facebook.internal.FacebookInitProvider
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
	android:authorities
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
receiver#com.facebook.CurrentAccessTokenExpirationBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
action#com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
receiver#com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
	android:exported
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
intent-filter#action:name:com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
action#com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED
ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
	android:name
		ADDED from [com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8428d340826a2ecb5d0375e974f117c5\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8beb1feee60daee62f76be5ecc6164f4\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbcb75a73f66d705e28a4829e35e990\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6b102572f4ada38af6d6f6f1d1af3625\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77ebf4142fa027c086a4d858e94a84c8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c494075105c7d38451d28b9f5f2569b7\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b81e3f26a824d5a58f9eb5d919a7090f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13328f712c1aa74217220757d3c27359\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31d22eaa13acb5b9bbb4751973d8a364\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b342d485a8761ff928efa735d2678814\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f41aa76d7063089ff55a3e0975b4f2d\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0e29c6239aca0abc7c7e2b3fad09ee1f\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c380338078d1c911945794b3c19d0ab\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aa5dbd9b9d8a0831bb43e53990788679\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b34c44ac5de3b8d6462e1da41dbd5c6\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\12d357b4eb02ba5de8901b7231fb47c9\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c41292a57d6e36c690b8e5c7dfb17bc3\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d309ce6da8202eea067704764da46cc\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4210a4eb28eb81963a550e3f4270e1a9\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
meta-data#aia-compat-api-min-version
ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
	android:value
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
	android:name
		ADDED from [com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bed4caf01f5fe1c0bdad1af7b8236638\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34af1e865ceef285b8d7bdfc4e1469aa\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
