<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.stripe.hcaptcha.webview.HCaptchaWebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/loadingContainer"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_centerInParent="true"
        android:padding="20dp"
        android:background="@android:color/white"
        android:clickable="true"
        android:focusable="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="150dp"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/stripe_ic_hcaptcha_logo"
            android:contentDescription="@string/stripe_hcaptcha_logo_description" />

        <ProgressBar
            android:layout_gravity="center"
            android:indeterminate="true"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <TextView
            android:layout_gravity="center"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/stripe_hcaptcha_loading_text" />
    </LinearLayout>

</RelativeLayout>
