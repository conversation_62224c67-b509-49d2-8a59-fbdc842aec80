  R android  id 	android.R  message android.R.id  Activity android.app  
runOnUiThread android.app.Activity  Context android.content  LAYOUT_INFLATER_SERVICE android.content.Context  assets android.content.Context  	getASSETS android.content.Context  	getAssets android.content.Context  getDrawable android.content.Context  getSystemService android.content.Context  
runOnUiThread android.content.Context  	setAssets android.content.Context  
runOnUiThread android.content.ContextWrapper  AssetManager android.content.res  
PorterDuff android.graphics  Typeface android.graphics  Mode android.graphics.PorterDuff  SRC_IN  android.graphics.PorterDuff.Mode  createFromAsset android.graphics.Typeface  Drawable android.graphics.drawable  setColorFilter "android.graphics.drawable.Drawable  Build 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  Gravity android.view  LayoutInflater android.view  View android.view  
runOnUiThread  android.view.ContextThemeWrapper  BOTTOM android.view.Gravity  CENTER android.view.Gravity  TOP android.view.Gravity  inflate android.view.LayoutInflater  findViewById android.view.View  setTextColor android.view.View  TextView android.widget  Toast android.widget  
background android.widget.TextView  
getBACKGROUND android.widget.TextView  
getBackground android.widget.TextView  getTEXT android.widget.TextView  getTEXTSize android.widget.TextView  getTYPEFACE android.widget.TextView  getText android.widget.TextView  getTextSize android.widget.TextView  getTypeface android.widget.TextView  
setBackground android.widget.TextView  setText android.widget.TextView  setTextColor android.widget.TextView  setTextSize android.widget.TextView  setTypeface android.widget.TextView  text android.widget.TextView  textSize android.widget.TextView  typeface android.widget.TextView  Callback android.widget.Toast  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  addCallback android.widget.Toast  cancel android.widget.Toast  duration android.widget.Toast  equals android.widget.Toast  getDURATION android.widget.Toast  getDuration android.widget.Toast  getVIEW android.widget.Toast  getView android.widget.Toast  makeText android.widget.Toast  setDuration android.widget.Toast  
setGravity android.widget.Toast  setView android.widget.Toast  show android.widget.Toast  view android.widget.Toast  mToast android.widget.Toast.Callback  
onToastHidden android.widget.Toast.Callback  
ContextCompat androidx.core.content  getDrawable #androidx.core.content.ContextCompat  FlutterInjector 
io.flutter  
flutterLoader io.flutter.FlutterInjector  instance io.flutter.FlutterInjector  getLookupKeyForAsset 0io.flutter.embedding.engine.loader.FlutterLoader  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Any *io.github.ponnamkarthik.toast.fluttertoast  Build *io.github.ponnamkarthik.toast.fluttertoast  Context *io.github.ponnamkarthik.toast.fluttertoast  
ContextCompat *io.github.ponnamkarthik.toast.fluttertoast  	Exception *io.github.ponnamkarthik.toast.fluttertoast  FlutterInjector *io.github.ponnamkarthik.toast.fluttertoast  FlutterToastPlugin *io.github.ponnamkarthik.toast.fluttertoast  Gravity *io.github.ponnamkarthik.toast.fluttertoast  Int *io.github.ponnamkarthik.toast.fluttertoast  Log *io.github.ponnamkarthik.toast.fluttertoast  MethodCallHandlerImpl *io.github.ponnamkarthik.toast.fluttertoast  
MethodChannel *io.github.ponnamkarthik.toast.fluttertoast  Number *io.github.ponnamkarthik.toast.fluttertoast  
PorterDuff *io.github.ponnamkarthik.toast.fluttertoast  R *io.github.ponnamkarthik.toast.fluttertoast  String *io.github.ponnamkarthik.toast.fluttertoast  Toast *io.github.ponnamkarthik.toast.fluttertoast  Typeface *io.github.ponnamkarthik.toast.fluttertoast  android *io.github.ponnamkarthik.toast.fluttertoast  mToast *io.github.ponnamkarthik.toast.fluttertoast  toString *io.github.ponnamkarthik.toast.fluttertoast  BinaryMessenger =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  Context =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  
FlutterPlugin =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  MethodCallHandlerImpl =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  
MethodChannel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  channel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  setupChannel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  teardownChannel =io.github.ponnamkarthik.toast.fluttertoast.FlutterToastPlugin  Activity @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Any @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  AssetManager @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Build @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Context @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
ContextCompat @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Drawable @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  	Exception @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  FlutterInjector @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Gravity @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Int @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  LayoutInflater @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Log @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
MethodCall @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
MethodChannel @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Number @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
PorterDuff @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  R @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  String @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  TextView @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Toast @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  Typeface @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  android @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  context @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
getANDROID @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  
getAndroid @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  getTOString @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  getToString @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  mToast @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  toString @io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl  	getMToast `io.github.ponnamkarthik.toast.fluttertoast.MethodCallHandlerImpl.onMethodCall.<no name provided>  drawable ,io.github.ponnamkarthik.toast.fluttertoast.R  id ,io.github.ponnamkarthik.toast.fluttertoast.R  layout ,io.github.ponnamkarthik.toast.fluttertoast.R  corner 5io.github.ponnamkarthik.toast.fluttertoast.R.drawable  text /io.github.ponnamkarthik.toast.fluttertoast.R.id  toast_custom 3io.github.ponnamkarthik.toast.fluttertoast.R.layout  Build 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  FlutterInjector 	java.lang  Gravity 	java.lang  Log 	java.lang  MethodCallHandlerImpl 	java.lang  
MethodChannel 	java.lang  
PorterDuff 	java.lang  R 	java.lang  Toast 	java.lang  Typeface 	java.lang  android 	java.lang  mToast 	java.lang  toString 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  Any kotlin  Boolean kotlin  Build kotlin  CharSequence kotlin  Context kotlin  
ContextCompat kotlin  	Exception kotlin  Float kotlin  FlutterInjector kotlin  	Function0 kotlin  Gravity kotlin  Int kotlin  Log kotlin  MethodCallHandlerImpl kotlin  
MethodChannel kotlin  Nothing kotlin  Number kotlin  
PorterDuff kotlin  R kotlin  String kotlin  Toast kotlin  Typeface kotlin  Unit kotlin  android kotlin  mToast kotlin  toString kotlin  getTOString 
kotlin.Any  getToString 
kotlin.Any  Build kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  	Exception kotlin.annotation  FlutterInjector kotlin.annotation  Gravity kotlin.annotation  Log kotlin.annotation  MethodCallHandlerImpl kotlin.annotation  
MethodChannel kotlin.annotation  
PorterDuff kotlin.annotation  R kotlin.annotation  Toast kotlin.annotation  Typeface kotlin.annotation  android kotlin.annotation  mToast kotlin.annotation  toString kotlin.annotation  Build kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  	Exception kotlin.collections  FlutterInjector kotlin.collections  Gravity kotlin.collections  Log kotlin.collections  MethodCallHandlerImpl kotlin.collections  
MethodChannel kotlin.collections  
PorterDuff kotlin.collections  R kotlin.collections  Toast kotlin.collections  Typeface kotlin.collections  android kotlin.collections  mToast kotlin.collections  toString kotlin.collections  Build kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  	Exception kotlin.comparisons  FlutterInjector kotlin.comparisons  Gravity kotlin.comparisons  Log kotlin.comparisons  MethodCallHandlerImpl kotlin.comparisons  
MethodChannel kotlin.comparisons  
PorterDuff kotlin.comparisons  R kotlin.comparisons  Toast kotlin.comparisons  Typeface kotlin.comparisons  android kotlin.comparisons  mToast kotlin.comparisons  toString kotlin.comparisons  Build 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  	Exception 	kotlin.io  FlutterInjector 	kotlin.io  Gravity 	kotlin.io  Log 	kotlin.io  MethodCallHandlerImpl 	kotlin.io  
MethodChannel 	kotlin.io  
PorterDuff 	kotlin.io  R 	kotlin.io  Toast 	kotlin.io  Typeface 	kotlin.io  android 	kotlin.io  mToast 	kotlin.io  toString 	kotlin.io  Build 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  	Exception 
kotlin.jvm  FlutterInjector 
kotlin.jvm  Gravity 
kotlin.jvm  Log 
kotlin.jvm  MethodCallHandlerImpl 
kotlin.jvm  
MethodChannel 
kotlin.jvm  
PorterDuff 
kotlin.jvm  R 
kotlin.jvm  Toast 
kotlin.jvm  Typeface 
kotlin.jvm  android 
kotlin.jvm  mToast 
kotlin.jvm  toString 
kotlin.jvm  Build 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  	Exception 
kotlin.ranges  FlutterInjector 
kotlin.ranges  Gravity 
kotlin.ranges  Log 
kotlin.ranges  MethodCallHandlerImpl 
kotlin.ranges  
MethodChannel 
kotlin.ranges  
PorterDuff 
kotlin.ranges  R 
kotlin.ranges  Toast 
kotlin.ranges  Typeface 
kotlin.ranges  android 
kotlin.ranges  mToast 
kotlin.ranges  toString 
kotlin.ranges  Build kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  	Exception kotlin.sequences  FlutterInjector kotlin.sequences  Gravity kotlin.sequences  Log kotlin.sequences  MethodCallHandlerImpl kotlin.sequences  
MethodChannel kotlin.sequences  
PorterDuff kotlin.sequences  R kotlin.sequences  Toast kotlin.sequences  Typeface kotlin.sequences  android kotlin.sequences  mToast kotlin.sequences  toString kotlin.sequences  Build kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  	Exception kotlin.text  FlutterInjector kotlin.text  Gravity kotlin.text  Log kotlin.text  MethodCallHandlerImpl kotlin.text  
MethodChannel kotlin.text  
PorterDuff kotlin.text  R kotlin.text  Toast kotlin.text  Typeface kotlin.text  android kotlin.text  mToast kotlin.text  toString kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       