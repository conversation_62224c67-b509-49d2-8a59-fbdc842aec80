[{"merged": "com.flutter.stripe.stripe_android-release-91:/drawable-anydpi-v21/ic_call_answer_video_low.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/drawable-anydpi-v21/ic_call_answer_video_low.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/drawable-anydpi-v21/ic_call_decline.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/drawable-anydpi-v21/ic_call_decline.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/drawable-anydpi-v21/ic_call_decline_low.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/drawable-anydpi-v21/ic_call_decline_low.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/drawable-anydpi-v21/ic_call_answer.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/drawable-anydpi-v21/ic_call_answer.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/drawable-anydpi-v21/ic_call_answer_video.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/drawable-anydpi-v21/ic_call_answer_video.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/drawable-anydpi-v21/ic_call_answer_low.xml", "source": "com.flutter.stripe.stripe_android-core-1.13.1-32:/drawable-anydpi-v21/ic_call_answer_low.xml"}]