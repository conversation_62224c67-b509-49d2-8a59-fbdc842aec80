<dependencies>
  <compile
      roots=":@@:firebase_core::release,com.google.firebase:firebase-messaging:24.1.1@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.activity:activity:1.8.1@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.core:core-ktx:1.13.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.lifecycle:lifecycle-common:2.7.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.savedstate:savedstate:1.2.1@aar,androidx.annotation:annotation-jvm:1.8.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar,org.jetbrains:annotations:23.0.0@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar">
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.activity:activity:1.8.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
  </compile>
  <package
      roots=":@@:firebase_core::release,com.google.firebase:firebase-messaging:24.1.1@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar,io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar,com.google.firebase:firebase-installations:18.0.0@aar,com.google.firebase:firebase-common-ktx:21.0.0@aar,com.google.firebase:firebase-common:21.0.0@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-cloud-messaging:17.2.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,androidx.window:window:1.2.0@aar,androidx.window:window-java:1.2.0@aar,androidx.loader:loader:1.0.0@aar,androidx.activity:activity:1.8.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar,com.google.android.gms:play-services-tasks:18.1.0@aar,com.google.android.gms:play-services-basement:18.3.0@aar,androidx.fragment:fragment:1.7.1@aar,androidx.fragment:fragment:1.7.1@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,com.google.firebase:firebase-components:18.0.0@aar,com.google.firebase:firebase-datatransport:18.2.0@aar,com.google.android.datatransport:transport-backend-cct:3.1.9@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.android.datatransport:transport-runtime:3.1.9@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.window.extensions.core:core:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.0@jar,com.getkeepsafe.relinker:relinker:1.4.5@aar,com.google.errorprone:error_prone_annotations:2.26.0@jar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar,org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar,com.google.firebase:firebase-annotations:16.2.0@jar,org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar,org.jetbrains:annotations:23.0.0@jar,javax.inject:javax.inject:1@jar,com.google.guava:listenablefuture:1.0@jar">
    <dependency
        name=":@@:firebase_core::release"
        simpleName="artifacts::firebase_core"/>
    <dependency
        name="com.google.firebase:firebase-messaging:24.1.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.1.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="io.flutter:flutter_embedding_release:1.0.0-72f2b18bb094f92f62a3113a8075240ebb59affa@jar"
        simpleName="io.flutter:flutter_embedding_release"/>
    <dependency
        name="com.google.firebase:firebase-installations:18.0.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:21.0.0@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.2.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="androidx.window:window:1.2.0@aar"
        simpleName="androidx.window:window"/>
    <dependency
        name="androidx.window:window-java:1.2.0@aar"
        simpleName="androidx.window:window-java"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.activity:activity:1.8.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.3.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.7.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.firebase:firebase-components:18.0.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.2.0@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.9@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.window.extensions.core:core:1.0.0@aar"
        simpleName="androidx.window.extensions.core:core"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
        simpleName="com.getkeepsafe.relinker:relinker"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.26.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-common"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
  </package>
</dependencies>
