{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3435,3530,3632,3729,3826,3932,4050,13879", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3525,3627,3724,3821,3927,4045,4160,13975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5726", "endColumns": "131", "endOffsets": "5853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,351,420,481,552,618,680,748,818,878,940,1013,1077,1154,1217,1290,1353,1444,1519,1601,1693,1791,1876,1923,1970,2046,2110,2176,2245,2349,2436,2534", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "114,195,262,346,415,476,547,613,675,743,813,873,935,1008,1072,1149,1212,1285,1348,1439,1514,1596,1688,1786,1871,1918,1965,2041,2105,2171,2240,2344,2431,2529,2626"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15013,15259,15525,15670,15754,15823,16319,16390,16528,16590,16658,16728,16788,16850,16923,17046,17123,17358,18275,18338,19375,19536,19618,24233,24331,25661,26537,26584,26713,27456,27522,28363,28467,29187,29285", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "15072,15335,15587,15749,15818,15879,16385,16451,16585,16653,16723,16783,16845,16918,16982,17118,17181,17426,18333,18424,19445,19613,19705,24326,24411,25703,26579,26655,26772,27517,27586,28462,28549,29280,29377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,195,259,346,409,481,540,616,686,753,822", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "131,190,254,341,404,476,535,611,681,748,817,884"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15077,15884,16022,16086,16173,16456,16987,17186,17431,17745,18117,18429", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "15153,15938,16081,16168,16231,16523,17041,17257,17496,17807,18181,18491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3105,3181,3260,3350,4165,4271,4387,7377,7441,7583,8046,8285,8344,8431,8493,8555,8615,8681,8743,8797,8909,8966,9027,9081,9153,9279,9365,9443,9536,9622,9706,9845,9926,10007,10142,10232,10314,10367,10419,10485,10557,10641,10712,10792,10867,10943,11016,11091,11189,11274,11349,11441,11535,11609,11682,11776,11828,11910,11979,12064,12151,12213,12277,12340,12412,12515,12620,12715,12818,12875,13240,13570,13651,13801", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "314,3100,3176,3255,3345,3430,4266,4382,4465,7436,7501,7672,8106,8339,8426,8488,8550,8610,8676,8738,8792,8904,8961,9022,9076,9148,9274,9360,9438,9531,9617,9701,9840,9921,10002,10137,10227,10309,10362,10414,10480,10552,10636,10707,10787,10862,10938,11011,11086,11184,11269,11344,11436,11530,11604,11677,11771,11823,11905,11974,12059,12146,12208,12272,12335,12407,12510,12615,12710,12813,12870,12926,13315,13646,13724,13874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,205", "endColumns": "77,71,76", "endOffsets": "128,200,277"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4642,7219,7506", "endColumns": "77,71,76", "endOffsets": "4715,7286,7578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4720,4827,4991,5117,5223,5378,5505,5620,5858,6024,6129,6293,6419,6574,6718,6782,6842", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4822,4986,5112,5218,5373,5500,5615,5721,6019,6124,6288,6414,6569,6713,6777,6837,6916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,13485", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,13565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,341,427,485,544,600,676,759,839,940,1041,1125,1203,1282,1365,1461,1551,1618,1705,1792,1891,2010,2099,2186,2264,2364,2440,2533,2623,2709,2793,2905,2978,3064,3182,3293,3368,3432,4171,4886,4963,5085,5189,5244,5344,5439,5505,5597,5690,5747,5831,5882,5966,6065,6133,6204,6271,6337,6384,6465,6560,6635,6683,6728,6805,6863,6929,7113,7287,7407,7472,7555,7633,7731,7816,7901,7978,8072,8148,8254,8341,8430,8483,8612,8660,8714,8779,8854,8922,8990,9064,9152,9220,9271", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "119,197,259,336,422,480,539,595,671,754,834,935,1036,1120,1198,1277,1360,1456,1546,1613,1700,1787,1886,2005,2094,2181,2259,2359,2435,2528,2618,2704,2788,2900,2973,3059,3177,3288,3363,3427,4166,4881,4958,5080,5184,5239,5339,5434,5500,5592,5685,5742,5826,5877,5961,6060,6128,6199,6266,6332,6379,6460,6555,6630,6678,6723,6800,6858,6924,7108,7282,7402,7467,7550,7628,7726,7811,7896,7973,8067,8143,8249,8336,8425,8478,8607,8655,8709,8774,8849,8917,8985,9059,9147,9215,9266,9351"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13980,14049,14127,14189,14266,14352,14410,14718,14774,14850,14933,15158,15340,15441,15592,15943,16236,17262,17501,17591,17658,17812,17899,17998,18186,18496,18583,18661,18761,18837,18930,19020,19106,19190,19302,19450,19710,19828,20672,20747,20811,22507,23222,23299,23421,23525,23580,23680,23775,23841,23933,24026,24083,24678,24729,24813,25080,25148,25219,25286,25808,26193,26274,26369,26444,26492,26777,26854,26912,26978,27162,27336,27644,27709,28674,28752,28931,29016,29382,29459,29632,30283,30814,30901,38552,38605,38734,38893,39491,41720,41795,41863,41931,42005,42093,42161,43582", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "14044,14122,14184,14261,14347,14405,14464,14769,14845,14928,15008,15254,15436,15520,15665,16017,16314,17353,17586,17653,17740,17894,17993,18112,18270,18578,18656,18756,18832,18925,19015,19101,19185,19297,19370,19531,19823,19934,20742,20806,21545,23217,23294,23416,23520,23575,23675,23770,23836,23928,24021,24078,24162,24724,24808,24907,25143,25214,25281,25347,25850,26269,26364,26439,26487,26532,26849,26907,26973,27157,27331,27451,27704,27787,28747,28845,29011,29096,29454,29548,29703,30384,30896,30985,38600,38729,38777,38942,39551,41790,41858,41926,42000,42088,42156,42207,43662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,344,447,688,736,806,907,980,1240,1300,1391,1457,1512,1576,1652,1744,2053,2125,2192,2245,2298,2388,2521,2629,2686,2772,2853,2939,3018,3124,3437,3516,3593,3657,3717,3779,3839,3912,3994,4094,4189,4288,4388,4482,4556,4635,4720,4949,5187,5303,5435,5493,6247,6351,6413", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "170,339,442,683,731,801,902,975,1235,1295,1386,1452,1507,1571,1647,1739,2048,2120,2187,2240,2293,2383,2516,2624,2681,2767,2848,2934,3013,3119,3432,3511,3588,3652,3712,3774,3834,3907,3989,4089,4184,4283,4383,4477,4551,4630,4715,4944,5182,5298,5430,5488,6242,6346,6408,6473"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19939,20059,20228,20431,21550,21598,21668,21769,21842,22102,22162,24167,24416,24614,24912,24988,25352,26054,26126,26660,27591,27889,27979,28112,28220,28277,28850,29101,29553,29708,29814,30127,30206,30449,30625,30685,30990,31625,31698,31872,32081,33778,36233,36333,36427,36501,36580,38231,38947,39185,39301,39433,39556,40310,40414,42212", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "20054,20223,20326,20667,21593,21663,21764,21837,22097,22157,22248,24228,24466,24673,24983,25075,25656,26121,26188,26708,27639,27974,28107,28215,28272,28358,28926,29182,29627,29809,30122,30201,30278,30508,30680,30742,31045,31693,31775,31967,32171,33872,36328,36422,36496,36575,36660,38455,39180,39296,39428,39486,40305,40409,40471,42272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "45447,45533", "endColumns": "85,89", "endOffsets": "45528,45618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4560,7021,7119,7291,8111,8194,12931,13018,13103,13173,13320,13402,13729,45184,45262,45328", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "4555,4637,7114,7214,7372,8189,8280,13013,13098,13168,13235,13397,13480,13796,45257,45323,45442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "7985,45623", "endColumns": "60,77", "endOffsets": "8041,45696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "27792", "endColumns": "96", "endOffsets": "27884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6921,7677,7775,7884", "endColumns": "99,97,108,100", "endOffsets": "7016,7770,7879,7980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,222,304,404,495,577,658,801,901,1007,1100,1220,1280,1392,1459,1669,1901,2034,2126,2235,2328,2427,2605,2713,2806,3084,3192,3320,3528,3740,3837,3949,4166,4260,4320,4390,4478,4579,4671,4744,4840,4924,5030,5266,5332,5400,5478,5594,5678,5767,5835,5916,5987,6063,6193,6280,6384,6478,6550,6629,6696,6784,6844,6948,7068,7175,7304,7367,7461,7565,7688,7759,7851,7962,8022,8090,8168,8272,8450,8728,9032,9132,9206,9296,9369,9487,9604,9689,9761,9832,9918,10003,10089,10228,10371,10439,10511,10575,10763,10834,10892,10968,11043,11120,11201,11319,11438,11543,11630,11708,11797,11866", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "126,217,299,399,490,572,653,796,896,1002,1095,1215,1275,1387,1454,1664,1896,2029,2121,2230,2323,2422,2600,2708,2801,3079,3187,3315,3523,3735,3832,3944,4161,4255,4315,4385,4473,4574,4666,4739,4835,4919,5025,5261,5327,5395,5473,5589,5673,5762,5830,5911,5982,6058,6188,6275,6379,6473,6545,6624,6691,6779,6839,6943,7063,7170,7299,7362,7456,7560,7683,7754,7846,7957,8017,8085,8163,8267,8445,8723,9027,9127,9201,9291,9364,9482,9599,9684,9756,9827,9913,9998,10084,10223,10366,10434,10506,10570,10758,10829,10887,10963,11038,11115,11196,11314,11433,11538,11625,11703,11792,11861,12023"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14469,14545,14636,20331,22253,22344,22426,24471,25708,25855,25961,28554,30389,30513,30747,31050,31260,31492,31780,31972,32176,32269,32368,32546,32654,32747,33025,33133,33261,33469,33681,33877,33989,34206,34300,34360,34430,34518,34619,34711,34784,34880,34964,35070,35306,35372,35440,35518,35634,35718,35807,35875,35956,36027,36103,36665,36752,36856,36950,37022,37101,37168,37256,37316,37420,37540,37647,37776,37839,37933,38037,38160,38460,38782,40476,40536,40604,40682,40786,40964,41242,41546,41646,42277,42367,42440,42558,42675,42760,42832,42903,42989,43074,43160,43299,43442,43510,43667,43731,43919,43990,44048,44124,44199,44276,44357,44475,44594,44699,44786,44864,44953,45022", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "14540,14631,14713,20426,22339,22421,22502,24609,25803,25956,26049,28669,30444,30620,30809,31255,31487,31620,31867,32076,32264,32363,32541,32649,32742,33020,33128,33256,33464,33676,33773,33984,34201,34295,34355,34425,34513,34614,34706,34779,34875,34959,35065,35301,35367,35435,35513,35629,35713,35802,35870,35951,36022,36098,36228,36747,36851,36945,37017,37096,37163,37251,37311,37415,37535,37642,37771,37834,37928,38032,38155,38226,38547,38888,40531,40599,40677,40781,40959,41237,41541,41641,41715,42362,42435,42553,42670,42755,42827,42898,42984,43069,43155,43294,43437,43505,43577,43726,43914,43985,44043,44119,44194,44271,44352,44470,44589,44694,44781,44859,44948,45017,45179"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3435,3530,3632,3729,3826,3932,4050,13879", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3525,3627,3724,3821,3927,4045,4160,13975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5726", "endColumns": "131", "endOffsets": "5853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,351,420,481,552,618,680,748,818,878,940,1013,1077,1154,1217,1290,1353,1444,1519,1601,1693,1791,1876,1923,1970,2046,2110,2176,2245,2349,2436,2534", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "114,195,262,346,415,476,547,613,675,743,813,873,935,1008,1072,1149,1212,1285,1348,1439,1514,1596,1688,1786,1871,1918,1965,2041,2105,2171,2240,2344,2431,2529,2626"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15013,15259,15525,15670,15754,15823,16319,16390,16528,16590,16658,16728,16788,16850,16923,17046,17123,17358,18275,18338,19375,19536,19618,24233,24331,25661,26537,26584,26713,27456,27522,28363,28467,29187,29285", "endColumns": "63,80,66,83,68,60,70,65,61,67,69,59,61,72,63,76,62,72,62,90,74,81,91,97,84,46,46,75,63,65,68,103,86,97,96", "endOffsets": "15072,15335,15587,15749,15818,15879,16385,16451,16585,16653,16723,16783,16845,16918,16982,17118,17181,17426,18333,18424,19445,19613,19705,24326,24411,25703,26579,26655,26772,27517,27586,28462,28549,29280,29377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,195,259,346,409,481,540,616,686,753,822", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "131,190,254,341,404,476,535,611,681,748,817,884"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15077,15884,16022,16086,16173,16456,16987,17186,17431,17745,18117,18429", "endColumns": "80,58,63,86,62,71,58,75,69,66,68,66", "endOffsets": "15153,15938,16081,16168,16231,16523,17041,17257,17496,17807,18181,18491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3105,3181,3260,3350,4165,4271,4387,7377,7441,7583,8046,8285,8344,8431,8493,8555,8615,8681,8743,8797,8909,8966,9027,9081,9153,9279,9365,9443,9536,9622,9706,9845,9926,10007,10142,10232,10314,10367,10419,10485,10557,10641,10712,10792,10867,10943,11016,11091,11189,11274,11349,11441,11535,11609,11682,11776,11828,11910,11979,12064,12151,12213,12277,12340,12412,12515,12620,12715,12818,12875,13240,13570,13651,13801", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "314,3100,3176,3255,3345,3430,4266,4382,4465,7436,7501,7672,8106,8339,8426,8488,8550,8610,8676,8738,8792,8904,8961,9022,9076,9148,9274,9360,9438,9531,9617,9701,9840,9921,10002,10137,10227,10309,10362,10414,10480,10552,10636,10707,10787,10862,10938,11011,11086,11184,11269,11344,11436,11530,11604,11677,11771,11823,11905,11974,12059,12146,12208,12272,12335,12407,12510,12615,12710,12813,12870,12926,13315,13646,13724,13874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,205", "endColumns": "77,71,76", "endOffsets": "128,200,277"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4642,7219,7506", "endColumns": "77,71,76", "endOffsets": "4715,7286,7578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4720,4827,4991,5117,5223,5378,5505,5620,5858,6024,6129,6293,6419,6574,6718,6782,6842", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4822,4986,5112,5218,5373,5500,5615,5721,6019,6124,6288,6414,6569,6713,6777,6837,6916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,434,538,646,733,837,953,1036,1114,1205,1298,1393,1487,1587,1680,1775,1869,1960,2051,2137,2240,2345,2446,2550,2659,2767,2927,13485", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "429,533,641,728,832,948,1031,1109,1200,1293,1388,1482,1582,1675,1770,1864,1955,2046,2132,2235,2340,2441,2545,2654,2762,2922,3021,13565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,341,427,485,544,600,676,759,839,940,1041,1125,1203,1282,1365,1461,1551,1618,1705,1792,1891,2010,2099,2186,2264,2364,2440,2533,2623,2709,2793,2905,2978,3064,3182,3293,3368,3432,4171,4886,4963,5085,5189,5244,5344,5439,5505,5597,5690,5747,5831,5882,5966,6065,6133,6204,6271,6337,6384,6465,6560,6635,6683,6728,6805,6863,6929,7113,7287,7407,7472,7555,7633,7731,7816,7901,7978,8072,8148,8254,8341,8430,8483,8612,8660,8714,8779,8854,8922,8990,9064,9152,9220,9271", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "119,197,259,336,422,480,539,595,671,754,834,935,1036,1120,1198,1277,1360,1456,1546,1613,1700,1787,1886,2005,2094,2181,2259,2359,2435,2528,2618,2704,2788,2900,2973,3059,3177,3288,3363,3427,4166,4881,4958,5080,5184,5239,5339,5434,5500,5592,5685,5742,5826,5877,5961,6060,6128,6199,6266,6332,6379,6460,6555,6630,6678,6723,6800,6858,6924,7108,7282,7402,7467,7550,7628,7726,7811,7896,7973,8067,8143,8249,8336,8425,8478,8607,8655,8709,8774,8849,8917,8985,9059,9147,9215,9266,9351"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13980,14049,14127,14189,14266,14352,14410,14718,14774,14850,14933,15158,15340,15441,15592,15943,16236,17262,17501,17591,17658,17812,17899,17998,18186,18496,18583,18661,18761,18837,18930,19020,19106,19190,19302,19450,19710,19828,20672,20747,20811,22507,23222,23299,23421,23525,23580,23680,23775,23841,23933,24026,24083,24678,24729,24813,25080,25148,25219,25286,25808,26193,26274,26369,26444,26492,26777,26854,26912,26978,27162,27336,27644,27709,28674,28752,28931,29016,29382,29459,29632,30283,30814,30901,38552,38605,38734,38893,39491,41720,41795,41863,41931,42005,42093,42161,43582", "endColumns": "68,77,61,76,85,57,58,55,75,82,79,100,100,83,77,78,82,95,89,66,86,86,98,118,88,86,77,99,75,92,89,85,83,111,72,85,117,110,74,63,738,714,76,121,103,54,99,94,65,91,92,56,83,50,83,98,67,70,66,65,46,80,94,74,47,44,76,57,65,183,173,119,64,82,77,97,84,84,76,93,75,105,86,88,52,128,47,53,64,74,67,67,73,87,67,50,84", "endOffsets": "14044,14122,14184,14261,14347,14405,14464,14769,14845,14928,15008,15254,15436,15520,15665,16017,16314,17353,17586,17653,17740,17894,17993,18112,18270,18578,18656,18756,18832,18925,19015,19101,19185,19297,19370,19531,19823,19934,20742,20806,21545,23217,23294,23416,23520,23575,23675,23770,23836,23928,24021,24078,24162,24724,24808,24907,25143,25214,25281,25347,25850,26269,26364,26439,26487,26532,26849,26907,26973,27157,27331,27451,27704,27787,28747,28845,29011,29096,29454,29548,29703,30384,30896,30985,38600,38729,38777,38942,39551,41790,41858,41926,42000,42088,42156,42207,43662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,344,447,688,736,806,907,980,1240,1300,1391,1457,1512,1576,1652,1744,2053,2125,2192,2245,2298,2388,2521,2629,2686,2772,2853,2939,3018,3124,3437,3516,3593,3657,3717,3779,3839,3912,3994,4094,4189,4288,4388,4482,4556,4635,4720,4949,5187,5303,5435,5493,6247,6351,6413", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "170,339,442,683,731,801,902,975,1235,1295,1386,1452,1507,1571,1647,1739,2048,2120,2187,2240,2293,2383,2516,2624,2681,2767,2848,2934,3013,3119,3432,3511,3588,3652,3712,3774,3834,3907,3989,4089,4184,4283,4383,4477,4551,4630,4715,4944,5182,5298,5430,5488,6242,6346,6408,6473"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19939,20059,20228,20431,21550,21598,21668,21769,21842,22102,22162,24167,24416,24614,24912,24988,25352,26054,26126,26660,27591,27889,27979,28112,28220,28277,28850,29101,29553,29708,29814,30127,30206,30449,30625,30685,30990,31625,31698,31872,32081,33778,36233,36333,36427,36501,36580,38231,38947,39185,39301,39433,39556,40310,40414,42212", "endColumns": "119,168,102,240,47,69,100,72,259,59,90,65,54,63,75,91,308,71,66,52,52,89,132,107,56,85,80,85,78,105,312,78,76,63,59,61,59,72,81,99,94,98,99,93,73,78,84,228,237,115,131,57,753,103,61,64", "endOffsets": "20054,20223,20326,20667,21593,21663,21764,21837,22097,22157,22248,24228,24466,24673,24983,25075,25656,26121,26188,26708,27639,27974,28107,28215,28272,28358,28926,29182,29627,29809,30122,30201,30278,30508,30680,30742,31045,31693,31775,31967,32171,33872,36328,36422,36496,36575,36660,38455,39180,39296,39428,39486,40305,40409,40471,42272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "45447,45533", "endColumns": "85,89", "endOffsets": "45528,45618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4560,7021,7119,7291,8111,8194,12931,13018,13103,13173,13320,13402,13729,45184,45262,45328", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "4555,4637,7114,7214,7372,8189,8280,13013,13098,13168,13235,13397,13480,13796,45257,45323,45442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "7985,45623", "endColumns": "60,77", "endOffsets": "8041,45696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "27792", "endColumns": "96", "endOffsets": "27884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6921,7677,7775,7884", "endColumns": "99,97,108,100", "endOffsets": "7016,7770,7879,7980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,222,304,404,495,577,658,801,901,1007,1100,1220,1280,1392,1459,1669,1901,2034,2126,2235,2328,2427,2605,2713,2806,3084,3192,3320,3528,3740,3837,3949,4166,4260,4320,4390,4478,4579,4671,4744,4840,4924,5030,5266,5332,5400,5478,5594,5678,5767,5835,5916,5987,6063,6193,6280,6384,6478,6550,6629,6696,6784,6844,6948,7068,7175,7304,7367,7461,7565,7688,7759,7851,7962,8022,8090,8168,8272,8450,8728,9032,9132,9206,9296,9369,9487,9604,9689,9761,9832,9918,10003,10089,10228,10371,10439,10511,10575,10763,10834,10892,10968,11043,11120,11201,11319,11438,11543,11630,11708,11797,11866", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "126,217,299,399,490,572,653,796,896,1002,1095,1215,1275,1387,1454,1664,1896,2029,2121,2230,2323,2422,2600,2708,2801,3079,3187,3315,3523,3735,3832,3944,4161,4255,4315,4385,4473,4574,4666,4739,4835,4919,5025,5261,5327,5395,5473,5589,5673,5762,5830,5911,5982,6058,6188,6275,6379,6473,6545,6624,6691,6779,6839,6943,7063,7170,7299,7362,7456,7560,7683,7754,7846,7957,8017,8085,8163,8267,8445,8723,9027,9127,9201,9291,9364,9482,9599,9684,9756,9827,9913,9998,10084,10223,10366,10434,10506,10570,10758,10829,10887,10963,11038,11115,11196,11314,11433,11538,11625,11703,11792,11861,12023"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14469,14545,14636,20331,22253,22344,22426,24471,25708,25855,25961,28554,30389,30513,30747,31050,31260,31492,31780,31972,32176,32269,32368,32546,32654,32747,33025,33133,33261,33469,33681,33877,33989,34206,34300,34360,34430,34518,34619,34711,34784,34880,34964,35070,35306,35372,35440,35518,35634,35718,35807,35875,35956,36027,36103,36665,36752,36856,36950,37022,37101,37168,37256,37316,37420,37540,37647,37776,37839,37933,38037,38160,38460,38782,40476,40536,40604,40682,40786,40964,41242,41546,41646,42277,42367,42440,42558,42675,42760,42832,42903,42989,43074,43160,43299,43442,43510,43667,43731,43919,43990,44048,44124,44199,44276,44357,44475,44594,44699,44786,44864,44953,45022", "endColumns": "75,90,81,99,90,81,80,142,99,105,92,119,59,111,66,209,231,132,91,108,92,98,177,107,92,277,107,127,207,211,96,111,216,93,59,69,87,100,91,72,95,83,105,235,65,67,77,115,83,88,67,80,70,75,129,86,103,93,71,78,66,87,59,103,119,106,128,62,93,103,122,70,91,110,59,67,77,103,177,277,303,99,73,89,72,117,116,84,71,70,85,84,85,138,142,67,71,63,187,70,57,75,74,76,80,117,118,104,86,77,88,68,161", "endOffsets": "14540,14631,14713,20426,22339,22421,22502,24609,25803,25956,26049,28669,30444,30620,30809,31255,31487,31620,31867,32076,32264,32363,32541,32649,32742,33020,33128,33256,33464,33676,33773,33984,34201,34295,34355,34425,34513,34614,34706,34779,34875,34959,35065,35301,35367,35435,35513,35629,35713,35802,35870,35951,36022,36098,36228,36747,36851,36945,37017,37096,37163,37251,37311,37415,37535,37642,37771,37834,37928,38032,38155,38226,38547,38888,40531,40599,40677,40781,40959,41237,41541,41641,41715,42362,42435,42553,42670,42755,42827,42898,42984,43069,43155,43294,43437,43505,43577,43726,43914,43985,44043,44119,44194,44271,44352,44470,44589,44694,44781,44859,44948,45017,45179"}}]}]}