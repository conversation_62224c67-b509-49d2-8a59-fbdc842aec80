[{"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_slide_up.xml", "source": "com.flutter.stripe.stripe_android-jetified-paymentsheet-21.6.0-53:/anim/stripe_slide_up.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/linear_indeterminate_line1_tail_interpolator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/linear_indeterminate_line1_tail_interpolator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_popup_enter.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_popup_enter.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/m3_motion_fade_enter.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/m3_motion_fade_enter.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_checkbox_to_checked_icon_null_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_checkbox_to_checked_icon_null_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_radio_to_on_mtrl_ring_outer_path_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_checkbox_to_checked_box_inner_merged_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_fade_in.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_fade_in.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_slide_down.xml", "source": "com.flutter.stripe.stripe_android-jetified-paymentsheet-21.6.0-53:/anim/stripe_slide_down.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_3ds2_challenge_transition_slide_in.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/anim/stripe_3ds2_challenge_transition_slide_in.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_3ds2_challenge_transition_slide_out.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-3ds2-android-6.2.0-14:/anim/stripe_3ds2_challenge_transition_slide_out.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_checkbox_to_checked_box_outer_merged_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_slide_out_top.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_slide_out_top.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_radio_to_on_mtrl_dot_group_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_grow_fade_in_from_bottom.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_grow_fade_in_from_bottom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/linear_indeterminate_line1_head_interpolator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/linear_indeterminate_line1_head_interpolator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/m3_motion_fade_exit.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/m3_motion_fade_exit.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_card_widget_progress_fade_out.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/anim/stripe_card_widget_progress_fade_out.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_fade_out.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_fade_out.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_radio_to_off_mtrl_ring_outer_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_tooltip_exit.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_tooltip_exit.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_radio_to_off_mtrl_ring_outer_path_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_card_widget_progress_fade_in.xml", "source": "com.flutter.stripe.stripe_android-jetified-payments-core-21.6.0-56:/anim/stripe_card_widget_progress_fade_in.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/linear_indeterminate_line2_tail_interpolator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/linear_indeterminate_line2_tail_interpolator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_slide_out_bottom.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_slide_out_bottom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_checkbox_to_unchecked_box_inner_merged_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_popup_exit.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_popup_exit.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_radio_to_on_mtrl_ring_outer_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_checkbox_to_unchecked_check_path_merged_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/design_snackbar_in.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/design_snackbar_in.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_checkbox_to_unchecked_icon_null_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_transition_fade_in.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-ui-core-21.6.0-25:/anim/stripe_transition_fade_in.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/mtrl_card_lowers_interpolator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/mtrl_card_lowers_interpolator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_slide_in_bottom.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_slide_in_bottom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/design_snackbar_out.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/design_snackbar_out.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/linear_indeterminate_line2_head_interpolator.xml", "source": "com.flutter.stripe.stripe_android-material-1.12.0-3:/anim/linear_indeterminate_line2_head_interpolator.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/btn_radio_to_off_mtrl_dot_group_animation.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_shrink_fade_out_from_bottom.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_shrink_fade_out_from_bottom.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_tooltip_enter.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_tooltip_enter.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/stripe_transition_fade_out.xml", "source": "com.flutter.stripe.stripe_android-jetified-stripe-ui-core-21.6.0-25:/anim/stripe_transition_fade_out.xml"}, {"merged": "com.flutter.stripe.stripe_android-release-91:/anim/abc_slide_in_top.xml", "source": "com.flutter.stripe.stripe_android-appcompat-1.7.0-75:/anim/abc_slide_in_top.xml"}]