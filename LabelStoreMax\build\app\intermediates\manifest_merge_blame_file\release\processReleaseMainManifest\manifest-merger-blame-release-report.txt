1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.velvete.ly"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:5-78
12-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:5-76
13-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:7:22-73
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:5-65
14-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:8:22-63
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:5-79
15-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:9:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:5-81
16-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:10:22-78
17
18    <!-- Profile picture functionality permissions -->
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:5-65
19-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:13:22-62
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:5-80
20-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:14:22-77
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:5-81
21-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:15:22-78
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:5-76
22-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:16:22-73
23    <!-- Samsung -->
24    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-86
24-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-83
25    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-87
25-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-84
26    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:5-81
26-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:22-78
27    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:5-83
27-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:22-80
28    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:5-88
28-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:22-85
29    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:5-92
29-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:22-89
30    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:5-84
30-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:22-81
31    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:5-83
31-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:22-80
32    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:5-91
32-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:22-88
33    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:5-92
33-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:22-89
34    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:5-93
34-->[:app_badge_plus] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\app_badge_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:22-90
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.WAKE_LOCK" />
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-68
36-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-65
37
38    <queries>
38-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-11:15
39        <intent>
39-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:18
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-90
40-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-87
41        </intent>
42        <!-- Added to check the default browser that will host the AuthFlow. -->
43        <intent>
43-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:13:9-17:18
44            <action android:name="android.intent.action.VIEW" />
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
44-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
45
46            <data android:scheme="http" />
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
46-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
47        </intent>
48
49        <package android:name="com.facebook.katana" /> <!-- Added to check if Chrome is installed for browser-based payment authentication (e.g. 3DS1). -->
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:9-55
49-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:16:18-52
50        <package android:name="com.android.chrome" />
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:9-54
50-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:11:18-51
51
52        <intent>
52-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:11:9-17:18
53            <action android:name="android.intent.action.VIEW" />
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
53-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
54
55            <data
55-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
56                android:mimeType="*/*"
57                android:scheme="*" />
57-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
58        </intent>
59        <intent>
59-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:18:9-27:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
60-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
61
62            <category android:name="android.intent.category.BROWSABLE" />
62-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
62-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
63
64            <data
64-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
65                android:host="pay"
66                android:mimeType="*/*"
67                android:scheme="upi" />
67-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
68        </intent>
69        <intent>
69-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:28:9-30:18
70            <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:17-68
70-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:25-66
71        </intent>
72        <intent>
72-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:31:9-35:18
73            <action android:name="android.intent.action.SEND" />
73-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:13-65
73-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:32:21-62
74
75            <data android:mimeType="*/*" />
75-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
76        </intent>
77        <intent>
77-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:36:9-38:18
78            <action android:name="rzp.device_token.share" />
78-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:13-61
78-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:37:21-58
79        </intent> <!-- Needs to be explicitly declared on Android R+ -->
80        <package android:name="com.google.android.apps.maps" />
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
81    </queries>
82
83    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- Support for Google Privacy Sandbox adservices API -->
83-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:5-79
83-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:14:22-76
84    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
84-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:5-88
84-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:16:22-85
85    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
85-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:5-82
85-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:17:22-79
86    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE" />
86-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:5-92
86-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:18:22-89
87    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
87-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:5-83
87-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:19:22-80
88    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
89
90    <uses-feature
90-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
91        android:glEsVersion="0x00020000"
91-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
92        android:required="true" />
92-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
93
94    <permission
94-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ce322de103688eff69f9ca63ef8e11d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
95        android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
95-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ce322de103688eff69f9ca63ef8e11d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
96        android:protectionLevel="signature" />
96-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ce322de103688eff69f9ca63ef8e11d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
97
98    <uses-permission android:name="com.velvete.ly.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ce322de103688eff69f9ca63ef8e11d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
98-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ce322de103688eff69f9ca63ef8e11d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
99    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
99-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24ffce05d2992ce68f7720713d7b9f49\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
99-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\24ffce05d2992ce68f7720713d7b9f49\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
100
101    <application
102        android:name="android.app.Application"
102-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:19:9-42
103        android:allowBackup="false"
103-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:21:9-36
104        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
104-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4ce322de103688eff69f9ca63ef8e11d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
105        android:extractNativeLibs="true"
106        android:fullBackupContent="false"
106-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:22:9-42
107        android:icon="@mipmap/ic_launcher"
107-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:24:9-43
108        android:label="Label StoreMax"
108-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:20:9-39
109        android:supportsRtl="true"
109-->[com.facebook.android:facebook-login:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b72fde255f7d5f902bd69f07371f54d\transformed\jetified-facebook-login-18.0.3\AndroidManifest.xml:14:18-44
110        android:usesCleartextTraffic="true" >
110-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:23:9-44
111        <activity
111-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:25:9-43:20
112            android:name="com.velvete.ly.MainActivity"
112-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:26:13-55
113            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
113-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:29:13-163
114            android:exported="true"
114-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:33:13-36
115            android:hardwareAccelerated="true"
115-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:30:13-47
116            android:launchMode="singleTop"
116-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:27:13-43
117            android:screenOrientation="portrait"
117-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:31:13-49
118            android:theme="@style/LaunchTheme"
118-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:28:13-47
119            android:windowSoftInputMode="adjustResize" >
119-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:32:13-55
120            <meta-data
120-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:34:13-37:17
121                android:name="io.flutter.embedding.android.NormalTheme"
121-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:35:15-70
122                android:resource="@style/NormalTheme" />
122-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:36:15-52
123
124            <intent-filter>
124-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:39:13-42:29
125                <action android:name="android.intent.action.MAIN" />
125-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:17-68
125-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:25-66
126
127                <category android:name="android.intent.category.LAUNCHER" />
127-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:17-76
127-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:41:27-74
128            </intent-filter>
129        </activity>
130
131        <meta-data
131-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:44:9-46:33
132            android:name="flutterEmbedding"
132-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:45:13-44
133            android:value="2" />
133-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:46:13-30
134        <meta-data
134-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:47:9-49:71
135            android:name="com.google.android.geo.API_KEY"
135-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:48:13-58
136            android:value="AIzaSyDrKt-lB3zOcD_eLMRdnkUSspv1ovnhn6s" />
136-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:49:13-68
137
138        <service
138-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:56
139            android:name="com.baseflow.geolocator.GeolocatorLocationService"
139-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-77
140            android:enabled="true"
140-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-35
141            android:exported="false"
141-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
142            android:foregroundServiceType="location" />
142-->[:geolocator_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-53
143
144        <provider
144-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
145            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
145-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
146            android:authorities="com.velvete.ly.flutter.image_provider"
146-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
147            android:exported="false"
147-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
148            android:grantUriPermissions="true" >
148-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
149            <meta-data
149-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
150                android:name="android.support.FILE_PROVIDER_PATHS"
150-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
151                android:resource="@xml/flutter_image_picker_file_paths" />
151-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
152        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
153        <service
153-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
154            android:name="com.google.android.gms.metadata.ModuleDependencies"
154-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
155            android:enabled="false"
155-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
156            android:exported="false" >
156-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
157            <intent-filter>
157-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
158                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
158-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
158-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
159            </intent-filter>
160
161            <meta-data
161-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
162                android:name="photopicker_activity:0:required"
162-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
163                android:value="" />
163-->[:image_picker_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
164        </service>
165
166        <activity
166-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
167            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
167-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
168            android:exported="false"
168-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
169            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
169-->[:url_launcher_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
170
171        <service
171-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-17:72
172            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
172-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-107
173            android:exported="false"
173-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
174            android:permission="android.permission.BIND_JOB_SERVICE" />
174-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-69
175        <service
175-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-24:19
176            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
176-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-97
177            android:exported="false" >
177-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-37
178            <intent-filter>
178-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
179                <action android:name="com.google.firebase.MESSAGING_EVENT" />
179-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
179-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
180            </intent-filter>
181        </service>
182
183        <receiver
183-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-33:20
184            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
184-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-98
185            android:exported="true"
185-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-36
186            android:permission="com.google.android.c2dm.permission.SEND" >
186-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-73
187            <intent-filter>
187-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
188                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
188-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
188-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
189            </intent-filter>
190        </receiver>
191
192        <service
192-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:9-39:19
193            android:name="com.google.firebase.components.ComponentDiscoveryService"
193-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:18-89
194            android:directBootAware="true"
194-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
195            android:exported="false" >
195-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
196            <meta-data
196-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-38:85
197                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
197-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:37:17-128
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:17-82
199            <meta-data
199-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
200                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
200-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[:firebase_core] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_core\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
202            <meta-data
202-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
203                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
203-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
205            <meta-data
205-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
206                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
206-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
208            <meta-data
208-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69482b8b36c499bb2ab20f789ca7504\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
209                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
209-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69482b8b36c499bb2ab20f789ca7504\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69482b8b36c499bb2ab20f789ca7504\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
211            <meta-data
211-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69482b8b36c499bb2ab20f789ca7504\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
212                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
212-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69482b8b36c499bb2ab20f789ca7504\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69482b8b36c499bb2ab20f789ca7504\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
214            <meta-data
214-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29d746fd0ff873f5865e4252e2f7806\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
215                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
215-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29d746fd0ff873f5865e4252e2f7806\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
216                android:value="com.google.firebase.components.ComponentRegistrar" />
216-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d29d746fd0ff873f5865e4252e2f7806\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
217            <meta-data
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
218                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
219                android:value="com.google.firebase.components.ComponentRegistrar" />
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
220            <meta-data
220-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3359009bac7f8cc7276aaa1aaa62aabd\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
221                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
221-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3359009bac7f8cc7276aaa1aaa62aabd\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
222                android:value="com.google.firebase.components.ComponentRegistrar" />
222-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3359009bac7f8cc7276aaa1aaa62aabd\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
223        </service>
224
225        <provider
225-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:9-45:38
226            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
226-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:42:13-102
227            android:authorities="com.velvete.ly.flutterfirebasemessaginginitprovider"
227-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:13-88
228            android:exported="false"
228-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-37
229            android:initOrder="99" />
229-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-35
230
231        <activity
231-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-18:47
232            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
232-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-112
233            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
233-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-137
234            android:exported="false"
234-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-37
235            android:theme="@style/AppTheme" />
235-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-44
236        <activity
236-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-22:55
237            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
237-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-120
238            android:exported="false"
238-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
239            android:theme="@style/ThemeTransparent" />
239-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-52
240        <activity
240-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:9-26:55
241            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
241-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-114
242            android:exported="false"
242-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-37
243            android:theme="@style/ThemeTransparent" />
243-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-52
244        <activity
244-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:9-31:55
245            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
245-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-134
246            android:exported="false"
246-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-37
247            android:launchMode="singleInstance"
247-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-48
248            android:theme="@style/ThemeTransparent" />
248-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-52
249        <activity
249-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:9-36:55
250            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
250-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-128
251            android:exported="false"
251-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-37
252            android:launchMode="singleInstance"
252-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-48
253            android:theme="@style/ThemeTransparent" />
253-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-52
254
255        <receiver
255-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:9-41:40
256            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
256-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-119
257            android:enabled="true"
257-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-35
258            android:exported="false" />
258-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:13-37
259
260        <meta-data
260-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:9-45:36
261            android:name="io.flutter.embedded_views_preview"
261-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-61
262            android:value="true" />
262-->[:flutter_inappwebview_android] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-33
263
264        <activity
264-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:21:9-65:20
265            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetRedirectActivity"
265-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:22:13-109
266            android:exported="true"
266-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:23:13-36
267            android:launchMode="singleTask" >
267-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:24:13-44
268            <intent-filter>
268-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:25:13-64:29
269                <action android:name="android.intent.action.VIEW" />
269-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
269-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
270
271                <category android:name="android.intent.category.DEFAULT" />
271-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
271-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
272                <category android:name="android.intent.category.BROWSABLE" />
272-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
272-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
273
274                <!-- Returning from app2app: return_url is triggered to reopen web AuthFlow and poll accounts. -->
275                <data
275-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
276                    android:host="link-accounts"
277                    android:pathPrefix="/com.velvete.ly/authentication_return"
278                    android:scheme="stripe-auth" />
278-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
279
280                <!-- Returning from app2app: return_url is triggered to reopen native AuthFlow and poll accounts. -->
281                <data
281-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
282                    android:host="link-native-accounts"
283                    android:pathPrefix="/com.velvete.ly/authentication_return"
284                    android:scheme="stripe-auth" />
284-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
285
286                <!-- End of web AuthFlow success and cancel URIs that begin with "stripe-auth://link-accounts/{app-id}/...” -->
287                <data
287-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
288                    android:host="link-accounts"
289                    android:path="/com.velvete.ly/success"
290                    android:scheme="stripe-auth" />
290-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
291                <data
291-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
292                    android:host="link-accounts"
293                    android:path="/com.velvete.ly/cancel"
294                    android:scheme="stripe-auth" />
294-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
295
296                <!-- Opening app2app: Web flow triggers stripe-auth://native-redirect/{app-id}/http://web-that-redirects-to-native -->
297                <data
297-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
298                    android:host="native-redirect"
299                    android:pathPrefix="/com.velvete.ly"
300                    android:scheme="stripe-auth" />
300-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
301
302                <!-- Accepts success/cancel/fail URIs that begin with "stripe://auth-redirect” -->
303                <data
303-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
304                    android:host="auth-redirect"
305                    android:pathPrefix="/com.velvete.ly"
306                    android:scheme="stripe" />
306-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
307            </intent-filter>
308        </activity>
309        <activity
309-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:66:9-69:77
310            android:name="com.stripe.android.financialconnections.FinancialConnectionsSheetActivity"
310-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:67:13-101
311            android:exported="false"
311-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:68:13-37
312            android:theme="@style/StripeFinancialConnectionsDefaultTheme" />
312-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:69:13-74
313        <activity
313-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:70:9-74:58
314            android:name="com.stripe.android.financialconnections.ui.FinancialConnectionsSheetNativeActivity"
314-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:71:13-110
315            android:exported="false"
315-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:72:13-37
316            android:theme="@style/StripeFinancialConnectionsDefaultTheme"
316-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:73:13-74
317            android:windowSoftInputMode="adjustResize" />
317-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:74:13-55
318        <activity
318-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:20:9-23:66
319            android:name="com.facebook.FacebookActivity"
319-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:21:13-57
320            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
320-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:22:13-96
321            android:theme="@style/com_facebook_activity_theme" />
321-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:23:13-63
322        <activity android:name="com.facebook.CustomTabMainActivity" />
322-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:9-71
322-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:24:19-68
323        <activity
323-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:25:9-39:20
324            android:name="com.facebook.CustomTabActivity"
324-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:26:13-58
325            android:exported="true" >
325-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:27:13-36
326            <intent-filter>
326-->[com.facebook.android:facebook-common:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cc35582481f4bfb75a8244a2386072e4\transformed\jetified-facebook-common-18.0.3\AndroidManifest.xml:29:13-38:29
327                <action android:name="android.intent.action.VIEW" />
327-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
327-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
328
329                <category android:name="android.intent.category.DEFAULT" />
329-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
329-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
330                <category android:name="android.intent.category.BROWSABLE" />
330-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
330-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
331
332                <data
332-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
333                    android:host="cct.com.velvete.ly"
334                    android:scheme="fbconnect" />
334-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
335            </intent-filter>
336        </activity>
337        <activity
337-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:8:9-11:69
338            android:name="com.stripe.android.paymentsheet.PaymentSheetActivity"
338-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:9:13-80
339            android:exported="false"
339-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:10:13-37
340            android:theme="@style/StripePaymentSheetDefaultTheme" />
340-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:11:13-66
341        <activity
341-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:12:9-15:69
342            android:name="com.stripe.android.paymentsheet.PaymentOptionsActivity"
342-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:13:13-82
343            android:exported="false"
343-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:14:13-37
344            android:theme="@style/StripePaymentSheetDefaultTheme" />
344-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:15:13-66
345        <activity
345-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:16:9-19:69
346            android:name="com.stripe.android.customersheet.CustomerSheetActivity"
346-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:17:13-82
347            android:exported="false"
347-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:18:13-37
348            android:theme="@style/StripePaymentSheetDefaultTheme" />
348-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:19:13-66
349        <activity
349-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:20:9-23:69
350            android:name="com.stripe.android.paymentsheet.addresselement.AddressElementActivity"
350-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:21:13-97
351            android:exported="false"
351-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:22:13-37
352            android:theme="@style/StripePaymentSheetDefaultTheme" />
352-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:23:13-66
353        <activity
353-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:24:9-27:69
354            android:name="com.stripe.android.paymentsheet.paymentdatacollection.bacs.BacsMandateConfirmationActivity"
354-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:25:13-118
355            android:exported="false"
355-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:26:13-37
356            android:theme="@style/StripePaymentSheetDefaultTheme" />
356-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:27:13-66
357        <activity
357-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:28:9-31:69
358            android:name="com.stripe.android.paymentsheet.paymentdatacollection.polling.PollingActivity"
358-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:29:13-105
359            android:exported="false"
359-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:30:13-37
360            android:theme="@style/StripePaymentSheetDefaultTheme" />
360-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:31:13-66
361        <activity
361-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:32:9-35:69
362            android:name="com.stripe.android.paymentsheet.ui.SepaMandateActivity"
362-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:33:13-82
363            android:exported="false"
363-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:34:13-37
364            android:theme="@style/StripePaymentSheetDefaultTheme" />
364-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:35:13-66
365        <activity
365-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:36:9-39:68
366            android:name="com.stripe.android.paymentsheet.ExternalPaymentMethodProxyActivity"
366-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:37:13-94
367            android:exported="false"
367-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:38:13-37
368            android:theme="@style/StripePayLauncherDefaultTheme" />
368-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:39:13-65
369        <activity
369-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:40:9-42:69
370            android:name="com.stripe.android.paymentsheet.paymentdatacollection.cvcrecollection.CvcRecollectionActivity"
370-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:41:13-121
371            android:theme="@style/StripePaymentSheetDefaultTheme" />
371-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:42:13-66
372        <activity
372-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:43:9-45:69
373            android:name="com.stripe.android.paymentelement.embedded.form.FormActivity"
373-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:44:13-88
374            android:theme="@style/StripePaymentSheetDefaultTheme" />
374-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:45:13-66
375        <activity
375-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:46:9-48:69
376            android:name="com.stripe.android.paymentelement.embedded.manage.ManageActivity"
376-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:47:13-92
377            android:theme="@style/StripePaymentSheetDefaultTheme" />
377-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:48:13-66
378        <activity
378-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:49:9-56:58
379            android:name="com.stripe.android.link.LinkActivity"
379-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:50:13-64
380            android:autoRemoveFromRecents="true"
380-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:51:13-49
381            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
381-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:52:13-115
382            android:exported="false"
382-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:53:13-37
383            android:label="@string/stripe_link"
383-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:54:13-48
384            android:theme="@style/StripeLinkBaseTheme"
384-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:55:13-55
385            android:windowSoftInputMode="adjustResize" />
385-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:56:13-55
386        <activity
386-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:57:9-62:61
387            android:name="com.stripe.android.link.LinkForegroundActivity"
387-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:58:13-74
388            android:autoRemoveFromRecents="true"
388-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:59:13-49
389            android:configChanges="orientation|keyboard|keyboardHidden|screenLayout|screenSize|smallestScreenSize"
389-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:60:13-115
390            android:launchMode="singleTop"
390-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:61:13-43
391            android:theme="@style/StripeTransparentTheme" />
391-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:62:13-58
392        <activity
392-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:63:9-80:20
393            android:name="com.stripe.android.link.LinkRedirectHandlerActivity"
393-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:64:13-79
394            android:autoRemoveFromRecents="true"
394-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:65:13-49
395            android:exported="true"
395-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:66:13-36
396            android:launchMode="singleInstance"
396-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:67:13-48
397            android:theme="@style/StripeTransparentTheme" >
397-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:68:13-58
398            <intent-filter>
398-->[com.stripe:paymentsheet:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9d74c733895accc053be45587d98f531\transformed\jetified-paymentsheet-21.6.0\AndroidManifest.xml:69:13-79:29
399                <action android:name="android.intent.action.VIEW" />
399-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
399-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
400
401                <category android:name="android.intent.category.DEFAULT" />
401-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
401-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
402                <category android:name="android.intent.category.BROWSABLE" />
402-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
402-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
403
404                <data
404-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
405                    android:host="complete"
406                    android:path="/com.velvete.ly"
407                    android:scheme="link-popup" />
407-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
408            </intent-filter>
409        </activity>
410        <activity
410-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10723edee8237d3346b0c820444d9e\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:8:9-11:69
411            android:name="com.stripe.android.ui.core.cardscan.CardScanActivity"
411-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10723edee8237d3346b0c820444d9e\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:9:13-80
412            android:exported="false"
412-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10723edee8237d3346b0c820444d9e\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:10:13-37
413            android:theme="@style/StripePaymentSheetDefaultTheme" />
413-->[com.stripe:payments-ui-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10723edee8237d3346b0c820444d9e\transformed\jetified-payments-ui-core-21.6.0\AndroidManifest.xml:11:13-66
414        <activity
414-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:15:9-18:57
415            android:name="com.stripe.android.view.PaymentAuthWebViewActivity"
415-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:16:13-78
416            android:exported="false"
416-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:17:13-37
417            android:theme="@style/StripeDefaultTheme" />
417-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:18:13-54
418        <activity
418-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:19:9-22:61
419            android:name="com.stripe.android.view.PaymentRelayActivity"
419-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:20:13-72
420            android:exported="false"
420-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:21:13-37
421            android:theme="@style/StripeTransparentTheme" />
421-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:22:13-58
422        <!--
423        Set android:launchMode="singleTop" so that the StripeBrowserLauncherActivity instance that
424        launched the browser Activity will also handle the return URL deep link.
425        -->
426        <activity
426-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:28:9-32:61
427            android:name="com.stripe.android.payments.StripeBrowserLauncherActivity"
427-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:29:13-85
428            android:exported="false"
428-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:30:13-37
429            android:launchMode="singleTask"
429-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:31:13-44
430            android:theme="@style/StripeTransparentTheme" />
430-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:32:13-58
431        <activity
431-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:33:9-50:20
432            android:name="com.stripe.android.payments.StripeBrowserProxyReturnActivity"
432-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:34:13-88
433            android:exported="true"
433-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:35:13-36
434            android:launchMode="singleTask"
434-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:36:13-44
435            android:theme="@style/StripeTransparentTheme" >
435-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:37:13-58
436            <intent-filter>
436-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:38:13-49:29
437                <action android:name="android.intent.action.VIEW" />
437-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:13-65
437-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:14:21-62
438
439                <category android:name="android.intent.category.DEFAULT" />
439-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:17-76
439-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:28:27-73
440                <category android:name="android.intent.category.BROWSABLE" />
440-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:17-78
440-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:29:27-75
441
442                <!-- Must match `DefaultReturnUrl#value`. -->
443                <data
443-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:13-43
444                    android:host="payment_return_url"
445                    android:path="/com.velvete.ly"
446                    android:scheme="stripesdk" />
446-->[com.stripe:financial-connections:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f18d1e11621683c30a49a5d0c6c36fb1\transformed\jetified-financial-connections-21.6.0\AndroidManifest.xml:16:19-40
447            </intent-filter>
448        </activity>
449        <activity
449-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:51:9-54:57
450            android:name="com.stripe.android.payments.core.authentication.threeds2.Stripe3ds2TransactionActivity"
450-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:52:13-114
451            android:exported="false"
451-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:53:13-37
452            android:theme="@style/StripeDefaultTheme" />
452-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:54:13-54
453        <activity
453-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:55:9-58:66
454            android:name="com.stripe.android.googlepaylauncher.GooglePayLauncherActivity"
454-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:56:13-90
455            android:exported="false"
455-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:57:13-37
456            android:theme="@style/StripeGooglePayDefaultTheme" />
456-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:58:13-63
457        <activity
457-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:59:9-62:66
458            android:name="com.stripe.android.googlepaylauncher.GooglePayPaymentMethodLauncherActivity"
458-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:60:13-103
459            android:exported="false"
459-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:61:13-37
460            android:theme="@style/StripeGooglePayDefaultTheme" />
460-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:62:13-63
461        <activity
461-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:63:9-66:68
462            android:name="com.stripe.android.payments.paymentlauncher.PaymentLauncherConfirmationActivity"
462-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:64:13-107
463            android:exported="false"
463-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:65:13-37
464            android:theme="@style/StripePayLauncherDefaultTheme" />
464-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:66:13-65
465        <activity
465-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:67:9-70:61
466            android:name="com.stripe.android.payments.bankaccount.ui.CollectBankAccountActivity"
466-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:68:13-97
467            android:exported="false"
467-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:69:13-37
468            android:theme="@style/StripeTransparentTheme" />
468-->[com.stripe:payments-core:21.6.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32a9e3a05e9238088b3f4a5e4aa55da0\transformed\jetified-payments-core-21.6.0\AndroidManifest.xml:70:13-58
469        <activity
469-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2abb2661987f7c6e3ea5c9716013ed8c\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:8:9-11:54
470            android:name="com.stripe.android.stripe3ds2.views.ChallengeActivity"
470-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2abb2661987f7c6e3ea5c9716013ed8c\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:9:13-81
471            android:exported="false"
471-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2abb2661987f7c6e3ea5c9716013ed8c\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:10:13-37
472            android:theme="@style/Stripe3DS2Theme" />
472-->[com.stripe:stripe-3ds2-android:6.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2abb2661987f7c6e3ea5c9716013ed8c\transformed\jetified-stripe-3ds2-android-6.2.0\AndroidManifest.xml:11:13-51
473        <activity
473-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:42:9-50:20
474            android:name="com.razorpay.CheckoutActivity"
474-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:43:13-57
475            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
475-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:44:13-83
476            android:exported="false"
476-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:45:13-37
477            android:theme="@style/CheckoutTheme" >
477-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:46:13-49
478            <intent-filter>
478-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:47:13-49:29
479                <action android:name="android.intent.action.MAIN" />
479-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:17-68
479-->C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\android\app\src\main\AndroidManifest.xml:40:25-66
480            </intent-filter>
481        </activity>
482
483        <provider
483-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:52:9-60:20
484            android:name="androidx.startup.InitializationProvider"
484-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:53:13-67
485            android:authorities="com.velvete.ly.androidx-startup"
485-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:54:13-68
486            android:exported="false" >
486-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:55:13-37
487            <meta-data
487-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:57:13-59:52
488                android:name="com.razorpay.RazorpayInitializer"
488-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:58:17-64
489                android:value="androidx.startup" />
489-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:59:17-49
490            <meta-data
490-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\691aec2dcbaf2dca262387963dd38f05\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
491                android:name="androidx.emoji2.text.EmojiCompatInitializer"
491-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\691aec2dcbaf2dca262387963dd38f05\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
492                android:value="androidx.startup" />
492-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\691aec2dcbaf2dca262387963dd38f05\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
493            <meta-data
493-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c5063114fe880c9ff97afba432742e3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
494                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
494-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c5063114fe880c9ff97afba432742e3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
495                android:value="androidx.startup" />
495-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c5063114fe880c9ff97afba432742e3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
496            <meta-data
496-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
497                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
497-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
498                android:value="androidx.startup" />
498-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
499        </provider>
500
501        <activity
501-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:62:9-65:75
502            android:name="com.razorpay.MagicXActivity"
502-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:63:13-55
503            android:exported="false"
503-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:64:13-37
504            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
504-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:65:13-72
505
506        <meta-data
506-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:67:9-69:58
507            android:name="com.razorpay.plugin.googlepay_all"
507-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:68:13-61
508            android:value="com.razorpay.RzpGpayMerged" />
508-->[com.razorpay:standard-core:1.6.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ded7246b52afaa091453dff9ef450623\transformed\jetified-standard-core-1.6.52\AndroidManifest.xml:69:13-55
509
510        <activity
510-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:23:9-27:75
511            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
511-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:24:13-93
512            android:excludeFromRecents="true"
512-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:25:13-46
513            android:exported="false"
513-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:26:13-37
514            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
514-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:27:13-72
515        <!--
516            Service handling Google Sign-In user revocation. For apps that do not integrate with
517            Google Sign-In, this service will never be started.
518        -->
519        <service
519-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:33:9-37:51
520            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
520-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:34:13-89
521            android:exported="true"
521-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:35:13-36
522            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
522-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:36:13-107
523            android:visibleToInstantApps="true" />
523-->[com.google.android.gms:play-services-auth:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c36a0e77c56e7309d5535fab3f8790d\transformed\jetified-play-services-auth-21.1.0\AndroidManifest.xml:37:13-48
524        <!--
525         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
526         with the application context. This config is merged in with the host app's manifest,
527         but there can only be one provider with the same authority activated at any given
528         point; so if the end user has two or more different apps that use Facebook SDK, only the
529         first one will be able to use the provider. To work around this problem, we use the
530         following placeholder in the authority to identify each host application as if it was
531         a completely different provider.
532        -->
533        <provider
533-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:32:9-35:40
534            android:name="com.facebook.internal.FacebookInitProvider"
534-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:33:13-70
535            android:authorities="com.velvete.ly.FacebookInitProvider"
535-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:34:13-72
536            android:exported="false" />
536-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:35:13-37
537
538        <receiver
538-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:37:9-43:20
539            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
539-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:38:13-86
540            android:exported="false" >
540-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:39:13-37
541            <intent-filter>
541-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:40:13-42:29
542                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
542-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:17-95
542-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:41:25-92
543            </intent-filter>
544        </receiver>
545        <receiver
545-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:44:9-50:20
546            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
546-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:45:13-118
547            android:exported="false" >
547-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:46:13-37
548            <intent-filter>
548-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:47:13-49:29
549                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
549-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:17-103
549-->[com.facebook.android:facebook-core:18.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\990101496bac1e8066ce4ccbc785f69f\transformed\jetified-facebook-core-18.0.3\AndroidManifest.xml:48:25-100
550            </intent-filter>
551        </receiver>
552        <receiver
552-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
553            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
553-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
554            android:exported="true"
554-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
555            android:permission="com.google.android.c2dm.permission.SEND" >
555-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
556            <intent-filter>
556-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-32:29
557                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
557-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:17-81
557-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:25-78
558            </intent-filter>
559
560            <meta-data
560-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
561                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
561-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
562                android:value="true" />
562-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
563        </receiver>
564        <!--
565             FirebaseMessagingService performs security checks at runtime,
566             but set to not exported to explicitly avoid allowing another app to call it.
567        -->
568        <service
568-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
569            android:name="com.google.firebase.messaging.FirebaseMessagingService"
569-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
570            android:directBootAware="true"
570-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
571            android:exported="false" >
571-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9fb6fdceb1e53cf2bbac6fb343862c93\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
572            <intent-filter android:priority="-500" >
572-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-23:29
573                <action android:name="com.google.firebase.MESSAGING_EVENT" />
573-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:17-78
573-->[:firebase_messaging] C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\firebase_messaging\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:25-75
574            </intent-filter>
575        </service> <!-- Needs to be explicitly declared on P+ -->
576        <uses-library
576-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
577            android:name="org.apache.http.legacy"
577-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
578            android:required="false" />
578-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db76d5c20fc7edcf4d741ed1094ca5de\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
579
580        <activity
580-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cc0d5fa9a24b638d99b4ef8f806b8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
581            android:name="com.google.android.gms.common.api.GoogleApiActivity"
581-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cc0d5fa9a24b638d99b4ef8f806b8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
582            android:exported="false"
582-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cc0d5fa9a24b638d99b4ef8f806b8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
583            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
583-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d25cc0d5fa9a24b638d99b4ef8f806b8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
584
585        <provider
585-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
586            android:name="com.google.firebase.provider.FirebaseInitProvider"
586-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
587            android:authorities="com.velvete.ly.firebaseinitprovider"
587-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
588            android:directBootAware="true"
588-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
589            android:exported="false"
589-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
590            android:initOrder="100" />
590-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80f26cf253e5d3c0f94c10d6ce58d0b0\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
591
592        <uses-library
592-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db45465e5f672056ffda091be7209360\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
593            android:name="androidx.window.extensions"
593-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db45465e5f672056ffda091be7209360\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
594            android:required="false" />
594-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db45465e5f672056ffda091be7209360\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
595        <uses-library
595-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db45465e5f672056ffda091be7209360\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
596            android:name="androidx.window.sidecar"
596-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db45465e5f672056ffda091be7209360\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
597            android:required="false" />
597-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db45465e5f672056ffda091be7209360\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
598
599        <meta-data
599-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc19047cd54e21c30bae5bff58239c4a\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
600            android:name="com.google.android.gms.version"
600-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc19047cd54e21c30bae5bff58239c4a\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
601            android:value="@integer/google_play_services_version" />
601-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc19047cd54e21c30bae5bff58239c4a\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
602
603        <receiver
603-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
604            android:name="androidx.profileinstaller.ProfileInstallReceiver"
604-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
605            android:directBootAware="false"
605-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
606            android:enabled="true"
606-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
607            android:exported="true"
607-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
608            android:permission="android.permission.DUMP" >
608-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
609            <intent-filter>
609-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
610                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
610-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
611            </intent-filter>
612            <intent-filter>
612-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
613                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
613-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
614            </intent-filter>
615            <intent-filter>
615-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
616                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
616-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
617            </intent-filter>
618            <intent-filter>
618-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
619                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
619-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
619-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1fb588a0498b1b373f9e8d1750123bec\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
620            </intent-filter>
621        </receiver>
622
623        <service
623-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0b543c12ec929ad44c323dcaf16956\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
624            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
624-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0b543c12ec929ad44c323dcaf16956\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
625            android:exported="false" >
625-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0b543c12ec929ad44c323dcaf16956\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
626            <meta-data
626-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0b543c12ec929ad44c323dcaf16956\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
627                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
627-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0b543c12ec929ad44c323dcaf16956\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
628                android:value="cct" />
628-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b0b543c12ec929ad44c323dcaf16956\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
629        </service>
630        <service
630-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
631            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
631-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
632            android:exported="false"
632-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
633            android:permission="android.permission.BIND_JOB_SERVICE" >
633-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
634        </service>
635
636        <receiver
636-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
637            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
637-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
638            android:exported="false" />
638-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2b32e6480fad23f1f57b936b857c9bcd\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
639
640        <meta-data
640-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f3ac1daa57778b06e26aa2118018bb\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:10:9-12:33
641            android:name="aia-compat-api-min-version"
641-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f3ac1daa57778b06e26aa2118018bb\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:11:13-54
642            android:value="1" /> <!-- The activities will be merged into the manifest of the hosting app. -->
642-->[com.google.android.instantapps:instantapps:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\53f3ac1daa57778b06e26aa2118018bb\transformed\jetified-instantapps-1.1.0\AndroidManifest.xml:12:13-30
643        <activity
643-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ce4b4613c84f363539b5686c778bd6\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:14:9-18:65
644            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
644-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ce4b4613c84f363539b5686c778bd6\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:15:13-93
645            android:exported="false"
645-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ce4b4613c84f363539b5686c778bd6\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:16:13-37
646            android:stateNotNeeded="true"
646-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ce4b4613c84f363539b5686c778bd6\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:17:13-42
647            android:theme="@style/Theme.PlayCore.Transparent" />
647-->[com.google.android.play:core-common:2.0.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\99ce4b4613c84f363539b5686c778bd6\transformed\jetified-core-common-2.0.4\AndroidManifest.xml:18:13-62
648    </application>
649
650</manifest>
