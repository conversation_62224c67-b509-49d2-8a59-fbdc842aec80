{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,210,272,348,433,494,561,612,697,785,872,976,1080,1167,1247,1329,1413,1517,1612,1682,1774,1863,1948,2055,2137,2229,2306,2405,2488,2595,2695,2791,2883,2993,3075,3163,3285,3400,3483,3548,4311,5050,5129,5243,5354,5409,5520,5632,5706,5812,5918,5974,6062,6112,6195,6313,6388,6465,6532,6598,6646,6734,6836,6915,6963,7012,7081,7139,7204,7405,7601,7750,7818,7907,7993,8101,8200,8301,8384,8480,8560,8674,8756,8857,8911,9056,9108,9163,9232,9302,9377,9447,9521,9611,9687,9743", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "123,205,267,343,428,489,556,607,692,780,867,971,1075,1162,1242,1324,1408,1512,1607,1677,1769,1858,1943,2050,2132,2224,2301,2400,2483,2590,2690,2786,2878,2988,3070,3158,3280,3395,3478,3543,4306,5045,5124,5238,5349,5404,5515,5627,5701,5807,5913,5969,6057,6107,6190,6308,6383,6460,6527,6593,6641,6729,6831,6910,6958,7007,7076,7134,7199,7400,7596,7745,7813,7902,7988,8096,8195,8296,8379,8475,8555,8669,8751,8852,8906,9051,9103,9158,9227,9297,9372,9442,9516,9606,9682,9738,9817"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,348,349,351,352,356,357,359,364,371,372,443,444,445,447,452,465,466,467,468,469,470,471,487", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18174,18247,18329,18391,18467,18552,18613,18929,18980,19065,19153,19387,19573,19677,19831,20188,20476,21510,21752,21847,21917,22076,22165,22250,22417,22718,22810,22887,22986,23069,23176,23276,23372,23464,23574,23737,24015,24137,24921,25004,25069,26822,27561,27640,27754,27865,27920,28031,28143,28217,28323,28429,28485,29118,29168,29251,29531,29606,29683,29750,30278,30679,30767,30869,30948,30996,31309,31378,31436,31501,31702,31898,32248,32316,33245,33331,33527,33626,34044,34127,34294,34939,35518,35600,43572,43626,43771,43937,44544,46989,47059,47134,47204,47278,47368,47444,48969", "endColumns": "72,81,61,75,84,60,66,50,84,87,86,103,103,86,79,81,83,103,94,69,91,88,84,106,81,91,76,98,82,106,99,95,91,109,81,87,121,114,82,64,762,738,78,113,110,54,110,111,73,105,105,55,87,49,82,117,74,76,66,65,47,87,101,78,47,48,68,57,64,200,195,148,67,88,85,107,98,100,82,95,79,113,81,100,53,144,51,54,68,69,74,69,73,89,75,55,78", "endOffsets": "18242,18324,18386,18462,18547,18608,18675,18975,19060,19148,19235,19486,19672,19759,19906,20265,20555,21609,21842,21912,22004,22160,22245,22352,22494,22805,22882,22981,23064,23171,23271,23367,23459,23569,23651,23820,24132,24247,24999,25064,25827,27556,27635,27749,27860,27915,28026,28138,28212,28318,28424,28480,28568,29163,29246,29364,29601,29678,29745,29811,30321,30762,30864,30943,30991,31040,31373,31431,31496,31697,31893,32042,32311,32400,33326,33434,33621,33722,34122,34218,34369,35048,35595,35696,43621,43766,43818,43987,44608,47054,47129,47199,47273,47363,47439,47495,49043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,204", "endColumns": "74,73,76", "endOffsets": "125,199,276"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4795,10336,10725", "endColumns": "74,73,76", "endOffsets": "4865,10405,10797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,74", "endOffsets": "262,337"}, "to": {"startLines": "106,512", "startColumns": "4,4", "startOffsets": "11225,51376", "endColumns": "60,78", "endOffsets": "11281,51450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,325,426,637,684,756,860,935,1208,1271,1366,1440,1495,1559,1632,1721,2045,2112,2179,2234,2289,2383,2533,2646,2705,2792,2880,2973,3044,3147,3449,3530,3609,3686,3748,3810,3888,3963,4042,4151,4256,4355,4473,4576,4650,4729,4820,5018,5228,5361,5508,5570,6437,6545,6610", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "157,320,421,632,679,751,855,930,1203,1266,1361,1435,1490,1554,1627,1716,2040,2107,2174,2229,2284,2378,2528,2641,2700,2787,2875,2968,3039,3142,3444,3525,3604,3681,3743,3805,3883,3958,4037,4146,4251,4350,4468,4571,4645,4724,4815,5013,5223,5356,5503,5565,6432,6540,6605,6663"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,340,341,342,343,344,350,353,358,360,361,362,363,366,368,369,373,377,378,380,382,394,419,420,421,422,423,441,448,449,450,451,453,454,455,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24252,24359,24522,24710,25832,25879,25951,26055,26130,26403,26466,28573,28847,29054,29369,29442,29816,30545,30612,31192,32193,32405,32499,32649,32762,32821,33439,33727,34223,34374,34477,34779,34860,35128,35329,35391,35701,36322,36397,36566,36785,38614,41114,41232,41335,41409,41488,43278,43992,44202,44335,44482,44613,45480,45588,47500", "endColumns": "106,162,100,210,46,71,103,74,272,62,94,73,54,63,72,88,323,66,66,54,54,93,149,112,58,86,87,92,70,102,301,80,78,76,61,61,77,74,78,108,104,98,117,102,73,78,90,197,209,132,146,61,866,107,64,57", "endOffsets": "24354,24517,24618,24916,25874,25946,26050,26125,26398,26461,26556,28642,28897,29113,29437,29526,30135,30607,30674,31242,32243,32494,32644,32757,32816,32903,33522,33815,34289,34472,34774,34855,34934,35200,35386,35448,35774,36392,36471,36670,36885,38708,41227,41330,41404,41483,41574,43471,44197,44330,44477,44539,45475,45583,45648,47553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "92,100,171,175,504,510,511", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9955,10628,16512,16817,50588,51210,51296", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "10020,10720,16584,16955,50752,51291,51371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "508,509", "startColumns": "4,4", "startOffsets": "51027,51115", "endColumns": "87,94", "endOffsets": "51110,51205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,381,542,655,743,830,908,998,1095,1210,1324,1422,1520,1629,1745,1828,1913,2113,2207,2317,2439,2551", "endColumns": "162,162,160,112,87,86,77,89,96,114,113,97,97,108,115,82,84,199,93,109,121,111,173", "endOffsets": "213,376,537,650,738,825,903,993,1090,1205,1319,1417,1515,1624,1740,1823,1908,2108,2202,2312,2434,2546,2720"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,5033,5196,5357,5470,5558,5645,5723,5813,5910,6025,6139,6237,6335,6444,6560,6643,6728,6928,7022,7132,7254,7366", "endColumns": "162,162,160,112,87,86,77,89,96,114,113,97,97,108,115,82,84,199,93,109,121,111,173", "endOffsets": "5028,5191,5352,5465,5553,5640,5718,5808,5905,6020,6134,6232,6330,6439,6555,6638,6723,6923,7017,7127,7249,7361,7535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7540,7646,7826,7956,8065,8236,8369,8490,8764,8959,9071,9256,9392,9552,9731,9804,9871", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "7641,7821,7951,8060,8231,8364,8485,8598,8954,9066,9251,9387,9547,9726,9799,9866,9950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,269,354,425,486,558,631,695,763,833,893,953,1027,1091,1162,1225,1290,1355,1439,1520,1612,1710,1828,1910,1961,2011,2108,2170,2245,2316,2427,2516,2628", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "115,197,264,349,420,481,553,626,690,758,828,888,948,1022,1086,1157,1220,1285,1350,1434,1515,1607,1705,1823,1905,1956,2006,2103,2165,2240,2311,2422,2511,2623,2735"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,345,346,354,355", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19240,19491,19764,19911,19996,20067,20560,20632,20776,20840,20908,20978,21038,21098,21172,21294,21365,21614,22499,22564,23656,23825,23917,28647,28765,30140,31045,31095,31247,32047,32122,32908,33019,33820,33932", "endColumns": "64,81,66,84,70,60,71,72,63,67,69,59,59,73,63,70,62,64,64,83,80,91,97,117,81,50,49,96,61,74,70,110,88,111,111", "endOffsets": "19300,19568,19826,19991,20062,20123,20627,20700,20835,20903,20973,21033,21093,21167,21231,21360,21423,21674,22559,22643,23732,23912,24010,28760,28842,30186,31090,31187,31304,32117,32188,33014,33103,33927,34039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,259,341,403,474,532,614,687,754,814", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "132,192,254,336,398,469,527,609,682,749,809,879"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19305,20128,20270,20332,20414,20705,21236,21428,21679,22009,22357,22648", "endColumns": "81,59,61,81,61,70,57,81,72,66,59,69", "endOffsets": "19382,20183,20327,20409,20471,20771,21289,21505,21747,22071,22412,22713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,551,661,748,854,984,1069,1149,1240,1333,1431,1526,1626,1719,1812,1907,1998,2089,2175,2285,2396,2499,2610,2718,2825,2984,17126", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,546,656,743,849,979,1064,1144,1235,1328,1426,1521,1621,1714,1807,1902,1993,2084,2170,2280,2391,2494,2605,2713,2820,2979,3078,17208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,451,526", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "140,246,313,381,446,521,589"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17635,17725,17831,17898,17966,18031,18106", "endColumns": "89,105,66,67,64,74,67", "endOffsets": "17720,17826,17893,17961,18026,18101,18169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,391,485,569,652,804,891,1007,1110,1247,1322,1446,1511,1682,1881,2054,2144,2254,2356,2458,2653,2780,2892,3153,3264,3406,3631,3880,3978,4112,4314,4413,4474,4540,4625,4727,4830,4905,5005,5097,5202,5446,5521,5593,5674,5808,5883,5967,6034,6118,6189,6258,6379,6476,6589,6692,6769,6865,6936,7031,7096,7211,7328,7439,7579,7644,7743,7855,8006,8078,8174,8288,8350,8408,8487,8611,8791,9109,9445,9544,9624,9725,9801,9926,10083,10172,10265,10335,10422,10514,10611,10742,10884,10963,11035,11090,11253,11322,11383,11468,11544,11622,11716,11863,11981,12090,12182,12258,12340,12417", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "129,216,299,386,480,564,647,799,886,1002,1105,1242,1317,1441,1506,1677,1876,2049,2139,2249,2351,2453,2648,2775,2887,3148,3259,3401,3626,3875,3973,4107,4309,4408,4469,4535,4620,4722,4825,4900,5000,5092,5197,5441,5516,5588,5669,5803,5878,5962,6029,6113,6184,6253,6374,6471,6584,6687,6764,6860,6931,7026,7091,7206,7323,7434,7574,7639,7738,7850,8001,8073,8169,8283,8345,8403,8482,8606,8786,9104,9440,9539,9619,9720,9796,9921,10078,10167,10260,10330,10417,10509,10606,10737,10879,10958,11030,11085,11248,11317,11378,11463,11539,11617,11711,11858,11976,12085,12177,12253,12335,12412,12570"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,347,365,367,370,374,375,376,379,381,383,384,385,386,387,388,389,390,391,392,393,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,442,446,456,457,458,459,460,461,462,463,464,473,474,475,476,477,478,479,480,481,482,483,484,485,486,488,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18680,18759,18846,24623,26561,26655,26739,28902,30191,30326,30442,33108,35053,35205,35453,35779,35950,36149,36476,36675,36890,36992,37094,37289,37416,37528,37789,37900,38042,38267,38516,38713,38847,39049,39148,39209,39275,39360,39462,39565,39640,39740,39832,39937,40181,40256,40328,40409,40543,40618,40702,40769,40853,40924,40993,41579,41676,41789,41892,41969,42065,42136,42231,42296,42411,42528,42639,42779,42844,42943,43055,43206,43476,43823,45653,45715,45773,45852,45976,46156,46474,46810,46909,47558,47659,47735,47860,48017,48106,48199,48269,48356,48448,48545,48676,48818,48897,49048,49103,49266,49335,49396,49481,49557,49635,49729,49876,49994,50103,50195,50271,50353,50430", "endColumns": "78,86,82,86,93,83,82,151,86,115,102,136,74,123,64,170,198,172,89,109,101,101,194,126,111,260,110,141,224,248,97,133,201,98,60,65,84,101,102,74,99,91,104,243,74,71,80,133,74,83,66,83,70,68,120,96,112,102,76,95,70,94,64,114,116,110,139,64,98,111,150,71,95,113,61,57,78,123,179,317,335,98,79,100,75,124,156,88,92,69,86,91,96,130,141,78,71,54,162,68,60,84,75,77,93,146,117,108,91,75,81,76,157", "endOffsets": "18754,18841,18924,24705,26650,26734,26817,29049,30273,30437,30540,33240,35123,35324,35513,35945,36144,36317,36561,36780,36987,37089,37284,37411,37523,37784,37895,38037,38262,38511,38609,38842,39044,39143,39204,39270,39355,39457,39560,39635,39735,39827,39932,40176,40251,40323,40404,40538,40613,40697,40764,40848,40919,40988,41109,41671,41784,41887,41964,42060,42131,42226,42291,42406,42523,42634,42774,42839,42938,43050,43201,43273,43567,43932,45710,45768,45847,45971,46151,46469,46805,46904,46984,47654,47730,47855,48012,48101,48194,48264,48351,48443,48540,48671,48813,48892,48964,49098,49261,49330,49391,49476,49552,49630,49724,49871,49989,50098,50190,50266,50348,50425,50583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "10025,10899,11001,11120", "endColumns": "106,101,118,104", "endOffsets": "10127,10996,11115,11220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1150,1216,1313,1393,1455,1547,1614,1688,1749,1828,1892,1946,2062,2121,2183,2237,2319,2448,2540,2615,2710,2791,2875,3019,3098,3179,3326,3419,3498,3553,3604,3670,3749,3830,3901,3981,4053,4131,4206,4278,4389,4486,4563,4661,4759,4837,4918,5018,5075,5159,5225,5308,5395,5457,5521,5584,5660,5762,5869,5966,6072,6131,6186,6275,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "278,385,493,575,676,773,873,995,1080,1145,1211,1308,1388,1450,1542,1609,1683,1744,1823,1887,1941,2057,2116,2178,2232,2314,2443,2535,2610,2705,2786,2870,3014,3093,3174,3321,3414,3493,3548,3599,3665,3744,3825,3896,3976,4048,4126,4201,4273,4384,4481,4558,4656,4754,4832,4913,5013,5070,5154,5220,5303,5390,5452,5516,5579,5655,5757,5864,5961,6067,6126,6181,6270,6357,6434,6515"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3083,3190,3298,3380,3481,4301,4401,4523,10497,10562,10802,11286,11540,11602,11694,11761,11835,11896,11975,12039,12093,12209,12268,12330,12384,12466,12595,12687,12762,12857,12938,13022,13166,13245,13326,13473,13566,13645,13700,13751,13817,13896,13977,14048,14128,14200,14278,14353,14425,14536,14633,14710,14808,14906,14984,15065,15165,15222,15306,15372,15455,15542,15604,15668,15731,15807,15909,16016,16113,16219,16278,16728,17213,17300,17453", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,106,107,81,100,96,99,121,84,64,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,74,94,80,83,143,78,80,146,92,78,54,50,65,78,80,70,79,71,77,74,71,110,96,76,97,97,77,80,99,56,83,65,82,86,61,63,62,75,101,106,96,105,58,54,88,86,76,80", "endOffsets": "328,3185,3293,3375,3476,3573,4396,4518,4603,10557,10623,10894,11361,11597,11689,11756,11830,11891,11970,12034,12088,12204,12263,12325,12379,12461,12590,12682,12757,12852,12933,13017,13161,13240,13321,13468,13561,13640,13695,13746,13812,13891,13972,14043,14123,14195,14273,14348,14420,14531,14628,14705,14803,14901,14979,15060,15160,15217,15301,15367,15450,15537,15599,15663,15726,15802,15904,16011,16108,16214,16273,16328,16812,17295,17372,17529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,505,506,507", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4608,4707,10132,10230,10410,11366,11446,16333,16425,16589,16660,16960,17041,17377,50757,50836,50905", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "4702,4790,10225,10331,10492,11441,11535,16420,16507,16655,16723,17036,17121,17448,50831,50900,51022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8603", "endColumns": "160", "endOffsets": "8759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3578,3676,3778,3877,3979,4083,4187,17534", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3671,3773,3872,3974,4078,4182,4296,17630"}}]}]}