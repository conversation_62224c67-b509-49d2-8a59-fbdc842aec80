{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9487,10349,10450,10559", "endColumns": "102,100,108,98", "endOffsets": "9585,10445,10554,10653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7246,7357,7510,7641,7747,7890,8016,8132,8389,8530,8636,8785,8911,9059,9198,9264,9334", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "7352,7505,7636,7742,7885,8011,8127,8234,8525,8631,8780,8906,9054,9193,9259,9329,9412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,197,258,338,400,469,528,604,677,745,810", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "126,192,253,333,395,464,523,599,672,740,805,875"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18443,19255,19410,19471,19551,19834,20371,20567,20822,21154,21511,21821", "endColumns": "75,65,60,79,61,68,58,75,72,67,64,69", "endOffsets": "18514,19316,19466,19546,19608,19898,20425,20638,20890,21217,21571,21886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,336,425,611,660,726,823,893,1134,1204,1303,1369,1425,1489,1564,1655,1933,2001,2064,2119,2174,2248,2366,2474,2533,2620,2695,2786,2860,2960,3214,3293,3370,3438,3498,3560,3622,3692,3771,3869,3971,4066,4166,4259,4334,4413,4498,4682,4877,4993,5126,5186,5829,5930,5992", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "171,331,420,606,655,721,818,888,1129,1199,1298,1364,1420,1484,1559,1650,1928,1996,2059,2114,2169,2243,2361,2469,2528,2615,2690,2781,2855,2955,3209,3288,3365,3433,3493,3555,3617,3687,3766,3864,3966,4061,4161,4254,4329,4408,4493,4677,4872,4988,5121,5181,5824,5925,5987,6046"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,341,342,343,344,345,351,354,359,361,362,363,364,367,369,370,374,378,379,381,383,395,420,421,422,423,424,442,449,450,451,452,454,455,456,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23267,23388,23548,23731,24646,24695,24761,24858,24928,25169,25239,27090,27343,27515,27812,27887,28263,28913,28981,29514,30392,30684,30758,30876,30984,31043,31623,31860,32299,32450,32550,32804,32883,33152,33329,33389,33696,34263,34333,34500,34689,36311,38661,38761,38854,38929,39008,40643,41299,41494,41610,41743,41872,42515,42616,44322", "endColumns": "120,159,88,185,48,65,96,69,240,69,98,65,55,63,74,90,277,67,62,54,54,73,117,107,58,86,74,90,73,99,253,78,76,67,59,61,61,69,78,97,101,94,99,92,74,78,84,183,194,115,132,59,642,100,61,58", "endOffsets": "23383,23543,23632,23912,24690,24756,24853,24923,25164,25234,25333,27151,27394,27574,27882,27973,28536,28976,29039,29564,30442,30753,30871,30979,31038,31125,31693,31946,32368,32545,32799,32878,32955,33215,33384,33446,33753,34328,34407,34593,34786,36401,38756,38849,38924,39003,39088,40822,41489,41605,41738,41798,42510,42611,42673,44376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "30595", "endColumns": "88", "endOffsets": "30679"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,267,345,480,649,739", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "170,262,340,475,644,734,816"}, "to": {"startLines": "92,100,171,175,505,511,512", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9417,10085,15776,16067,47158,47778,47868", "endColumns": "69,91,77,134,168,89,81", "endOffsets": "9482,10172,15849,16197,47322,47863,47945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8239", "endColumns": "149", "endOffsets": "8384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "509,510", "startColumns": "4,4", "startOffsets": "47598,47688", "endColumns": "89,89", "endOffsets": "47683,47773"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,214", "endColumns": "79,78,78", "endOffsets": "130,209,288"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4593,9790,10177", "endColumns": "79,78,78", "endOffsets": "4668,9864,10251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,426,526,635,721,826,944,1030,1109,1200,1293,1388,1482,1576,1669,1765,1864,1955,2049,2129,2236,2337,2434,2540,2640,2738,2888,16365", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "421,521,630,716,821,939,1025,1104,1195,1288,1383,1477,1571,1664,1760,1859,1950,2044,2124,2231,2332,2429,2535,2635,2733,2883,2983,16441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,348,494,606,690,777,853,943,1041,1159,1276,1373,1470,1583,1716,1798,1878,2052,2147,2260,2374,2492", "endColumns": "161,130,145,111,83,86,75,89,97,117,116,96,96,112,132,81,79,173,94,112,113,117,135", "endOffsets": "212,343,489,601,685,772,848,938,1036,1154,1271,1368,1465,1578,1711,1793,1873,2047,2142,2255,2369,2487,2623"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4673,4835,4966,5112,5224,5308,5395,5471,5561,5659,5777,5894,5991,6088,6201,6334,6416,6496,6670,6765,6878,6992,7110", "endColumns": "161,130,145,111,83,86,75,89,97,117,116,96,96,112,132,81,79,173,94,112,113,117,135", "endOffsets": "4830,4961,5107,5219,5303,5390,5466,5556,5654,5772,5889,5986,6083,6196,6329,6411,6491,6665,6760,6873,6987,7105,7241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3488,3590,3688,3793,3898,4010,16758", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3483,3585,3683,3788,3893,4005,4121,16854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,206,268,341,423,484,551,603,681,754,828,927,1026,1114,1197,1286,1371,1470,1566,1636,1729,1819,1907,2018,2106,2199,2276,2376,2449,2539,2630,2714,2793,2893,2961,3038,3148,3245,3312,3377,3974,4543,4616,4727,4822,4877,4977,5073,5143,5242,5336,5393,5472,5522,5600,5705,5778,5857,5924,5990,6037,6111,6199,6280,6327,6375,6447,6505,6572,6728,6879,6995,7068,7143,7223,7315,7395,7477,7553,7642,7719,7838,7922,8012,8067,8201,8250,8306,8375,8440,8509,8574,8640,8729,8799,8849", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "121,201,263,336,418,479,546,598,676,749,823,922,1021,1109,1192,1281,1366,1465,1561,1631,1724,1814,1902,2013,2101,2194,2271,2371,2444,2534,2625,2709,2788,2888,2956,3033,3143,3240,3307,3372,3969,4538,4611,4722,4817,4872,4972,5068,5138,5237,5331,5388,5467,5517,5595,5700,5773,5852,5919,5985,6032,6106,6194,6275,6322,6370,6442,6500,6567,6723,6874,6990,7063,7138,7218,7310,7390,7472,7548,7637,7714,7833,7917,8007,8062,8196,8245,8301,8370,8435,8504,8569,8635,8724,8794,8844,8914"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,349,350,352,353,357,358,360,365,372,373,444,445,446,448,453,466,467,468,469,470,471,472,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17388,17459,17539,17601,17674,17756,17817,18102,18154,18232,18305,18519,18694,18793,18951,19321,19613,20643,20895,20991,21061,21222,21312,21400,21576,21891,21984,22061,22161,22234,22324,22415,22499,22578,22678,22821,23060,23170,23917,23984,24049,25592,26161,26234,26345,26440,26495,26595,26691,26761,26860,26954,27011,27579,27629,27707,27978,28051,28130,28197,28684,29044,29118,29206,29287,29334,29631,29703,29761,29828,29984,30135,30447,30520,31451,31531,31698,31778,32134,32210,32373,32960,33522,33606,40905,40960,41094,41243,41803,43848,43913,43982,44047,44113,44202,44272,45635", "endColumns": "70,79,61,72,81,60,66,51,77,72,73,98,98,87,82,88,84,98,95,69,92,89,87,110,87,92,76,99,72,89,90,83,78,99,67,76,109,96,66,64,596,568,72,110,94,54,99,95,69,98,93,56,78,49,77,104,72,78,66,65,46,73,87,80,46,47,71,57,66,155,150,115,72,74,79,91,79,81,75,88,76,118,83,89,54,133,48,55,68,64,68,64,65,88,69,49,69", "endOffsets": "17454,17534,17596,17669,17751,17812,17879,18149,18227,18300,18374,18613,18788,18876,19029,19405,19693,20737,20986,21056,21149,21307,21395,21506,21659,21979,22056,22156,22229,22319,22410,22494,22573,22673,22741,22893,23165,23262,23979,24044,24641,26156,26229,26340,26435,26490,26590,26686,26756,26855,26949,27006,27085,27624,27702,27807,28046,28125,28192,28258,28726,29113,29201,29282,29329,29377,29698,29756,29823,29979,30130,30246,30515,30590,31526,31618,31773,31855,32205,32294,32445,33074,33601,33691,40955,41089,41138,41294,41867,43908,43977,44042,44108,44197,44267,44317,45700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,273,367,464,543,621,737,831,920,1013,1148,1221,1330,1401,1566,1759,1906,1994,2085,2180,2275,2467,2567,2664,2896,2989,3112,3302,3511,3605,3715,3888,3976,4036,4101,4179,4277,4384,4462,4554,4652,4751,4956,5025,5094,5164,5279,5361,5445,5510,5590,5661,5736,5860,5949,6057,6149,6223,6304,6372,6453,6515,6613,6726,6831,6962,7028,7116,7209,7336,7410,7488,7588,7647,7711,7805,7920,8074,8322,8591,8683,8758,8846,8914,9017,9130,9216,9295,9369,9459,9535,9626,9742,9869,9934,10012,10069,10211,10277,10334,10412,10480,10558,10639,10770,10890,10997,11084,11164,11249,11318", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "119,198,268,362,459,538,616,732,826,915,1008,1143,1216,1325,1396,1561,1754,1901,1989,2080,2175,2270,2462,2562,2659,2891,2984,3107,3297,3506,3600,3710,3883,3971,4031,4096,4174,4272,4379,4457,4549,4647,4746,4951,5020,5089,5159,5274,5356,5440,5505,5585,5656,5731,5855,5944,6052,6144,6218,6299,6367,6448,6510,6608,6721,6826,6957,7023,7111,7204,7331,7405,7483,7583,7642,7706,7800,7915,8069,8317,8586,8678,8753,8841,8909,9012,9125,9211,9290,9364,9454,9530,9621,9737,9864,9929,10007,10064,10206,10272,10329,10407,10475,10553,10634,10765,10885,10992,11079,11159,11244,11313,11460"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,348,366,368,371,375,376,377,380,382,384,385,386,387,388,389,390,391,392,393,394,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,443,447,457,458,459,460,461,462,463,464,465,474,475,476,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17884,17953,18032,23637,25338,25435,25514,27399,28590,28731,28820,31316,33079,33220,33451,33758,33923,34116,34412,34598,34791,34886,34981,35173,35273,35370,35602,35695,35818,36008,36217,36406,36516,36689,36777,36837,36902,36980,37078,37185,37263,37355,37453,37552,37757,37826,37895,37965,38080,38162,38246,38311,38391,38462,38537,39093,39182,39290,39382,39456,39537,39605,39686,39748,39846,39959,40064,40195,40261,40349,40442,40569,40827,41143,42678,42737,42801,42895,43010,43164,43412,43681,43773,44381,44469,44537,44640,44753,44839,44918,44992,45082,45158,45249,45365,45492,45557,45705,45762,45904,45970,46027,46105,46173,46251,46332,46463,46583,46690,46777,46857,46942,47011", "endColumns": "68,78,69,93,96,78,77,115,93,88,92,134,72,108,70,164,192,146,87,90,94,94,191,99,96,231,92,122,189,208,93,109,172,87,59,64,77,97,106,77,91,97,98,204,68,68,69,114,81,83,64,79,70,74,123,88,107,91,73,80,67,80,61,97,112,104,130,65,87,92,126,73,77,99,58,63,93,114,153,247,268,91,74,87,67,102,112,85,78,73,89,75,90,115,126,64,77,56,141,65,56,77,67,77,80,130,119,106,86,79,84,68,146", "endOffsets": "17948,18027,18097,23726,25430,25509,25587,27510,28679,28815,28908,31446,33147,33324,33517,33918,34111,34258,34495,34684,34881,34976,35168,35268,35365,35597,35690,35813,36003,36212,36306,36511,36684,36772,36832,36897,36975,37073,37180,37258,37350,37448,37547,37752,37821,37890,37960,38075,38157,38241,38306,38386,38457,38532,38656,39177,39285,39377,39451,39532,39600,39681,39743,39841,39954,40059,40190,40256,40344,40437,40564,40638,40900,41238,42732,42796,42890,43005,43159,43407,43676,43768,43843,44464,44532,44635,44748,44834,44913,44987,45077,45153,45244,45360,45487,45552,45630,45757,45899,45965,46022,46100,46168,46246,46327,46458,46578,46685,46772,46852,46937,47006,47153"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,75", "endOffsets": "258,334"}, "to": {"startLines": "106,513", "startColumns": "4,4", "startOffsets": "10658,47950", "endColumns": "60,79", "endOffsets": "10714,48025"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,139,239,305,375,440,515", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "134,234,300,370,435,510,579"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "16859,16943,17043,17109,17179,17244,17319", "endColumns": "83,99,65,69,64,74,68", "endOffsets": "16938,17038,17104,17174,17239,17314,17383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,982,1050,1131,1213,1285,1362,1434", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,977,1045,1126,1208,1280,1357,1429,1551"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4414,4508,9590,9689,9869,10794,10871,15603,15694,15854,15920,16202,16283,16611,47327,47404,47476", "endColumns": "93,84,98,100,88,76,92,90,81,65,67,80,81,71,76,71,121", "endOffsets": "4503,4588,9684,9785,9953,10866,10959,15689,15771,15915,15983,16278,16360,16678,47399,47471,47593"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2988,3064,3138,3221,3310,4126,4222,4330,9958,10020,10256,10719,10964,11029,11117,11182,11248,11306,11377,11443,11497,11607,11667,11731,11785,11858,11974,12058,12134,12225,12306,12387,12520,12605,12690,12823,12913,12987,13039,13090,13156,13233,13315,13386,13460,13534,13613,13690,13762,13869,13958,14034,14125,14220,14294,14367,14461,14515,14589,14661,14747,14833,14895,14959,15022,15093,15194,15297,15392,15492,15548,15988,16446,16532,16683", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "313,3059,3133,3216,3305,3387,4217,4325,4409,10015,10080,10344,10789,11024,11112,11177,11243,11301,11372,11438,11492,11602,11662,11726,11780,11853,11969,12053,12129,12220,12301,12382,12515,12600,12685,12818,12908,12982,13034,13085,13151,13228,13310,13381,13455,13529,13608,13685,13757,13864,13953,14029,14120,14215,14289,14362,14456,14510,14584,14656,14742,14828,14890,14954,15017,15088,15189,15292,15387,15487,15543,15598,16062,16527,16606,16753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,195,265,357,425,486,553,622,684,754,829,889,951,1026,1090,1163,1227,1307,1375,1464,1539,1617,1701,1804,1888,1937,1989,2069,2131,2200,2272,2371,2458,2551", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "114,190,260,352,420,481,548,617,679,749,824,884,946,1021,1085,1158,1222,1302,1370,1459,1534,1612,1696,1799,1883,1932,1984,2064,2126,2195,2267,2366,2453,2546,2636"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,346,347,355,356", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18379,18618,18881,19034,19126,19194,19698,19765,19903,19965,20035,20110,20170,20232,20307,20430,20503,20742,21664,21732,22746,22898,22976,27156,27259,28541,29382,29434,29569,30251,30320,31130,31229,31951,32044", "endColumns": "63,75,69,91,67,60,66,68,61,69,74,59,61,74,63,72,63,79,67,88,74,77,83,102,83,48,51,79,61,68,71,98,86,92,89", "endOffsets": "18438,18689,18946,19121,19189,19250,19760,19829,19960,20030,20105,20165,20227,20302,20366,20498,20562,20817,21727,21816,22816,22971,23055,27254,27338,28585,29429,29509,29626,30315,30387,31224,31311,32039,32129"}}]}]}