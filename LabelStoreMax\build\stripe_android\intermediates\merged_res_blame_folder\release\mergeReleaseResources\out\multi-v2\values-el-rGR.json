{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-el-rGR/values-el-rGR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,205,273,356,424,485,551,620,685,766,837,897,958,1027,1091,1162,1229,1307,1372,1456,1536,1637,1744,1865,1959,2008,2085,2177,2241,2323,2395,2502,2587,2691", "endColumns": "66,82,67,82,67,60,65,68,64,80,70,59,60,68,63,70,66,77,64,83,79,100,106,120,93,48,76,91,63,81,71,106,84,103,101", "endOffsets": "117,200,268,351,419,480,546,615,680,761,832,892,953,1022,1086,1157,1224,1302,1367,1451,1531,1632,1739,1860,1954,2003,2080,2172,2236,2318,2390,2497,2582,2686,2788"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1662,1918,2198,2348,2431,2499,2998,3064,3205,3270,3351,3422,3482,3543,3612,3736,3807,4057,5018,5083,6218,6384,6485,11300,11421,12803,13711,13788,13939,14704,14786,15593,15700,16485,16589", "endColumns": "66,82,67,82,67,60,65,68,64,80,70,59,60,68,63,70,66,77,64,83,79,100,106,120,93,48,76,91,63,81,71,106,84,103,101", "endOffsets": "1724,1996,2261,2426,2494,2555,3059,3128,3265,3346,3417,3477,3538,3607,3671,3802,3869,4130,5078,5162,6293,6480,6587,11416,11510,12847,13783,13875,13998,14781,14853,15695,15780,16584,16686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,138,242,309,377,442,525", "endColumns": "82,103,66,67,64,82,68", "endOffsets": "133,237,304,372,437,520,589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,208,270,344,427,497,557,612,693,780,867,973,1079,1170,1252,1334,1422,1525,1630,1709,1811,1900,1993,2109,2196,2298,2384,2493,2574,2675,2784,2889,2977,3088,3168,3254,3360,3459,3537,3603,4385,5143,5225,5350,5459,5522,5625,5730,5819,5940,6056,6114,6200,6250,6330,6439,6512,6581,6648,6714,6764,6847,6953,7043,7089,7141,7212,7270,7329,7527,7705,7842,7914,8001,8078,8188,8287,8388,8473,8567,8656,8759,8844,8944,8997,9154,9205,9262,9330,9398,9476,9553,9626,9716,9786,9838", "endColumns": "71,80,61,73,82,69,59,54,80,86,86,105,105,90,81,81,87,102,104,78,101,88,92,115,86,101,85,108,80,100,108,104,87,110,79,85,105,98,77,65,781,757,81,124,108,62,102,104,88,120,115,57,85,49,79,108,72,68,66,65,49,82,105,89,45,51,70,57,58,197,177,136,71,86,76,109,98,100,84,93,88,102,84,99,52,156,50,56,67,67,77,76,72,89,69,51,82", "endOffsets": "122,203,265,339,422,492,552,607,688,775,862,968,1074,1165,1247,1329,1417,1520,1625,1704,1806,1895,1988,2104,2191,2293,2379,2488,2569,2670,2779,2884,2972,3083,3163,3249,3355,3454,3532,3598,4380,5138,5220,5345,5454,5517,5620,5725,5814,5935,6051,6109,6195,6245,6325,6434,6507,6576,6643,6709,6759,6842,6948,7038,7084,7136,7207,7265,7324,7522,7700,7837,7909,7996,8073,8183,8282,8383,8468,8562,8651,8754,8839,8939,8992,9149,9200,9257,9325,9393,9471,9548,9621,9711,9781,9833,9916"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "594,666,747,809,883,966,1036,1352,1407,1488,1575,1812,2001,2107,2266,2619,2910,3954,4217,4322,4401,4569,4658,4751,4931,5246,5348,5434,5543,5624,5725,5834,5939,6027,6138,6298,6592,6698,7472,7550,7616,9416,10174,10256,10381,10490,10553,10656,10761,10850,10971,11087,11145,11791,11841,11921,12192,12265,12334,12401,12947,13334,13417,13523,13613,13659,14003,14074,14132,14191,14389,14567,14911,14983,15920,15997,16198,16297,16691,16776,16948,17623,18169,18254,26163,26216,26373,26541,27120,29536,29604,29682,29759,29832,29922,29992,31491", "endColumns": "71,80,61,73,82,69,59,54,80,86,86,105,105,90,81,81,87,102,104,78,101,88,92,115,86,101,85,108,80,100,108,104,87,110,79,85,105,98,77,65,781,757,81,124,108,62,102,104,88,120,115,57,85,49,79,108,72,68,66,65,49,82,105,89,45,51,70,57,58,197,177,136,71,86,76,109,98,100,84,93,88,102,84,99,52,156,50,56,67,67,77,76,72,89,69,51,82", "endOffsets": "661,742,804,878,961,1031,1091,1402,1483,1570,1657,1913,2102,2193,2343,2696,2993,4052,4317,4396,4498,4653,4746,4862,5013,5343,5429,5538,5619,5720,5829,5934,6022,6133,6213,6379,6693,6792,7545,7611,8393,10169,10251,10376,10485,10548,10651,10756,10845,10966,11082,11140,11226,11836,11916,12025,12260,12329,12396,12462,12992,13412,13518,13608,13654,13706,14069,14127,14186,14384,14562,14699,14978,15065,15992,16102,16292,16393,16771,16865,17032,17721,18249,18349,26211,26368,26419,26593,27183,29599,29677,29754,29827,29917,29987,30039,31569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,324,414,635,680,755,862,944,1217,1280,1375,1444,1502,1566,1639,1728,2064,2141,2207,2266,2319,2415,2574,2695,2752,2842,2933,3020,3098,3199,3526,3606,3684,3749,3812,3876,3939,4012,4094,4194,4318,4417,4515,4607,4681,4760,4846,5053,5264,5382,5516,5575,6437,6544,6609", "endColumns": "114,153,89,220,44,74,106,81,272,62,94,68,57,63,72,88,335,76,65,58,52,95,158,120,56,89,90,86,77,100,326,79,77,64,62,63,62,72,81,99,123,98,97,91,73,78,85,206,210,117,133,58,861,106,64,65", "endOffsets": "165,319,409,630,675,750,857,939,1212,1275,1370,1439,1497,1561,1634,1723,2059,2136,2202,2261,2314,2410,2569,2690,2747,2837,2928,3015,3093,3194,3521,3601,3679,3744,3807,3871,3934,4007,4089,4189,4313,4412,4510,4602,4676,4755,4841,5048,5259,5377,5511,5570,6432,6539,6604,6670"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6797,6912,7066,7251,8398,8443,8518,8625,8707,8980,9043,11231,11515,11727,12030,12103,12467,13191,13268,13880,14858,15070,15166,15325,15446,15503,16107,16398,16870,17037,17138,17465,17545,17792,17974,18037,18354,18975,19048,19221,19429,21261,23781,23879,23971,24045,24124,25861,26598,26809,26927,27061,27188,28050,28157,30044", "endColumns": "114,153,89,220,44,74,106,81,272,62,94,68,57,63,72,88,335,76,65,58,52,95,158,120,56,89,90,86,77,100,326,79,77,64,62,63,62,72,81,99,123,98,97,91,73,78,85,206,210,117,133,58,861,106,64,65", "endOffsets": "6907,7061,7151,7467,8438,8513,8620,8702,8975,9038,9133,11295,11568,11786,12098,12187,12798,13263,13329,13934,14906,15161,15320,15441,15498,15588,16193,16480,16943,17133,17460,17540,17618,17852,18032,18096,18412,19043,19125,19316,19548,21355,23874,23966,24040,24119,24205,26063,26804,26922,27056,27115,28045,28152,28217,30105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,227,311,406,503,594,684,838,933,1033,1127,1262,1328,1445,1513,1690,1905,2071,2162,2270,2371,2472,2665,2781,2887,3185,3299,3450,3657,3882,3978,4105,4306,4401,4464,4532,4625,4724,4850,4928,5027,5117,5234,5451,5524,5591,5667,5793,5880,5964,6036,6125,6198,6269,6399,6486,6602,6708,6784,6867,6937,7027,7091,7198,7324,7433,7570,7637,7732,7839,7978,8050,8145,8262,8322,8382,8468,8572,8749,9050,9381,9490,9576,9665,9736,9855,9978,10064,10138,10213,10304,10393,10488,10638,10799,10874,10957,11023,11192,11260,11321,11400,11474,11552,11633,11770,11913,12036,12134,12213,12307,12378", "endColumns": "83,87,83,94,96,90,89,153,94,99,93,134,65,116,67,176,214,165,90,107,100,100,192,115,105,297,113,150,206,224,95,126,200,94,62,67,92,98,125,77,98,89,116,216,72,66,75,125,86,83,71,88,72,70,129,86,115,105,75,82,69,89,63,106,125,108,136,66,94,106,138,71,94,116,59,59,85,103,176,300,330,108,85,88,70,118,122,85,73,74,90,88,94,149,160,74,82,65,168,67,60,78,73,77,80,136,142,122,97,78,93,70,170", "endOffsets": "134,222,306,401,498,589,679,833,928,1028,1122,1257,1323,1440,1508,1685,1900,2066,2157,2265,2366,2467,2660,2776,2882,3180,3294,3445,3652,3877,3973,4100,4301,4396,4459,4527,4620,4719,4845,4923,5022,5112,5229,5446,5519,5586,5662,5788,5875,5959,6031,6120,6193,6264,6394,6481,6597,6703,6779,6862,6932,7022,7086,7193,7319,7428,7565,7632,7727,7834,7973,8045,8140,8257,8317,8377,8463,8567,8744,9045,9376,9485,9571,9660,9731,9850,9973,10059,10133,10208,10299,10388,10483,10633,10794,10869,10952,11018,11187,11255,11316,11395,11469,11547,11628,11765,11908,12031,12129,12208,12302,12373,12544"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1096,1180,1268,7156,9138,9235,9326,11573,12852,12997,13097,15785,17726,17857,18101,18417,18594,18809,19130,19321,19553,19654,19755,19948,20064,20170,20468,20582,20733,20940,21165,21360,21487,21688,21783,21846,21914,22007,22106,22232,22310,22409,22499,22616,22833,22906,22973,23049,23175,23262,23346,23418,23507,23580,23651,24210,24297,24413,24519,24595,24678,24748,24838,24902,25009,25135,25244,25381,25448,25543,25650,25789,26068,26424,28222,28282,28342,28428,28532,28709,29010,29341,29450,30110,30199,30270,30389,30512,30598,30672,30747,30838,30927,31022,31172,31333,31408,31574,31640,31809,31877,31938,32017,32091,32169,32250,32387,32530,32653,32751,32830,32924,32995", "endColumns": "83,87,83,94,96,90,89,153,94,99,93,134,65,116,67,176,214,165,90,107,100,100,192,115,105,297,113,150,206,224,95,126,200,94,62,67,92,98,125,77,98,89,116,216,72,66,75,125,86,83,71,88,72,70,129,86,115,105,75,82,69,89,63,106,125,108,136,66,94,106,138,71,94,116,59,59,85,103,176,300,330,108,85,88,70,118,122,85,73,74,90,88,94,149,160,74,82,65,168,67,60,78,73,77,80,136,142,122,97,78,93,70,170", "endOffsets": "1175,1263,1347,7246,9230,9321,9411,11722,12942,13092,13186,15915,17787,17969,18164,18589,18804,18970,19216,19424,19649,19750,19943,20059,20165,20463,20577,20728,20935,21160,21256,21482,21683,21778,21841,21909,22002,22101,22227,22305,22404,22494,22611,22828,22901,22968,23044,23170,23257,23341,23413,23502,23575,23646,23776,24292,24408,24514,24590,24673,24743,24833,24897,25004,25130,25239,25376,25443,25538,25645,25784,25856,26158,26536,28277,28337,28423,28527,28704,29005,29336,29445,29531,30194,30265,30384,30507,30593,30667,30742,30833,30922,31017,31167,31328,31403,31486,31635,31804,31872,31933,32012,32086,32164,32245,32382,32525,32648,32746,32825,32919,32990,33161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,197,259,341,406,478,538,618,700,766,830", "endColumns": "82,58,61,81,64,71,59,79,81,65,63,78", "endOffsets": "133,192,254,336,401,473,533,613,695,761,825,904"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1729,2560,2701,2763,2845,3133,3676,3874,4135,4503,4867,5167", "endColumns": "82,58,61,81,64,71,59,79,81,65,63,78", "endOffsets": "1807,2614,2758,2840,2905,3200,3731,3949,4212,4564,4926,5241"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-el-rGR/values-el-rGR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,205,273,356,424,485,551,620,685,766,837,897,958,1027,1091,1162,1229,1307,1372,1456,1536,1637,1744,1865,1959,2008,2085,2177,2241,2323,2395,2502,2587,2691", "endColumns": "66,82,67,82,67,60,65,68,64,80,70,59,60,68,63,70,66,77,64,83,79,100,106,120,93,48,76,91,63,81,71,106,84,103,101", "endOffsets": "117,200,268,351,419,480,546,615,680,761,832,892,953,1022,1086,1157,1224,1302,1367,1451,1531,1632,1739,1860,1954,2003,2080,2172,2236,2318,2390,2497,2582,2686,2788"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1662,1918,2198,2348,2431,2499,2998,3064,3205,3270,3351,3422,3482,3543,3612,3736,3807,4057,5018,5083,6218,6384,6485,11300,11421,12803,13711,13788,13939,14704,14786,15593,15700,16485,16589", "endColumns": "66,82,67,82,67,60,65,68,64,80,70,59,60,68,63,70,66,77,64,83,79,100,106,120,93,48,76,91,63,81,71,106,84,103,101", "endOffsets": "1724,1996,2261,2426,2494,2555,3059,3128,3265,3346,3417,3477,3538,3607,3671,3802,3869,4130,5078,5162,6293,6480,6587,11416,11510,12847,13783,13875,13998,14781,14853,15695,15780,16584,16686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,138,242,309,377,442,525", "endColumns": "82,103,66,67,64,82,68", "endOffsets": "133,237,304,372,437,520,589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,208,270,344,427,497,557,612,693,780,867,973,1079,1170,1252,1334,1422,1525,1630,1709,1811,1900,1993,2109,2196,2298,2384,2493,2574,2675,2784,2889,2977,3088,3168,3254,3360,3459,3537,3603,4385,5143,5225,5350,5459,5522,5625,5730,5819,5940,6056,6114,6200,6250,6330,6439,6512,6581,6648,6714,6764,6847,6953,7043,7089,7141,7212,7270,7329,7527,7705,7842,7914,8001,8078,8188,8287,8388,8473,8567,8656,8759,8844,8944,8997,9154,9205,9262,9330,9398,9476,9553,9626,9716,9786,9838", "endColumns": "71,80,61,73,82,69,59,54,80,86,86,105,105,90,81,81,87,102,104,78,101,88,92,115,86,101,85,108,80,100,108,104,87,110,79,85,105,98,77,65,781,757,81,124,108,62,102,104,88,120,115,57,85,49,79,108,72,68,66,65,49,82,105,89,45,51,70,57,58,197,177,136,71,86,76,109,98,100,84,93,88,102,84,99,52,156,50,56,67,67,77,76,72,89,69,51,82", "endOffsets": "122,203,265,339,422,492,552,607,688,775,862,968,1074,1165,1247,1329,1417,1520,1625,1704,1806,1895,1988,2104,2191,2293,2379,2488,2569,2670,2779,2884,2972,3083,3163,3249,3355,3454,3532,3598,4380,5138,5220,5345,5454,5517,5620,5725,5814,5935,6051,6109,6195,6245,6325,6434,6507,6576,6643,6709,6759,6842,6948,7038,7084,7136,7207,7265,7324,7522,7700,7837,7909,7996,8073,8183,8282,8383,8468,8562,8651,8754,8839,8939,8992,9149,9200,9257,9325,9393,9471,9548,9621,9711,9781,9833,9916"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "594,666,747,809,883,966,1036,1352,1407,1488,1575,1812,2001,2107,2266,2619,2910,3954,4217,4322,4401,4569,4658,4751,4931,5246,5348,5434,5543,5624,5725,5834,5939,6027,6138,6298,6592,6698,7472,7550,7616,9416,10174,10256,10381,10490,10553,10656,10761,10850,10971,11087,11145,11791,11841,11921,12192,12265,12334,12401,12947,13334,13417,13523,13613,13659,14003,14074,14132,14191,14389,14567,14911,14983,15920,15997,16198,16297,16691,16776,16948,17623,18169,18254,26163,26216,26373,26541,27120,29536,29604,29682,29759,29832,29922,29992,31491", "endColumns": "71,80,61,73,82,69,59,54,80,86,86,105,105,90,81,81,87,102,104,78,101,88,92,115,86,101,85,108,80,100,108,104,87,110,79,85,105,98,77,65,781,757,81,124,108,62,102,104,88,120,115,57,85,49,79,108,72,68,66,65,49,82,105,89,45,51,70,57,58,197,177,136,71,86,76,109,98,100,84,93,88,102,84,99,52,156,50,56,67,67,77,76,72,89,69,51,82", "endOffsets": "661,742,804,878,961,1031,1091,1402,1483,1570,1657,1913,2102,2193,2343,2696,2993,4052,4317,4396,4498,4653,4746,4862,5013,5343,5429,5538,5619,5720,5829,5934,6022,6133,6213,6379,6693,6792,7545,7611,8393,10169,10251,10376,10485,10548,10651,10756,10845,10966,11082,11140,11226,11836,11916,12025,12260,12329,12396,12462,12992,13412,13518,13608,13654,13706,14069,14127,14186,14384,14562,14699,14978,15065,15992,16102,16292,16393,16771,16865,17032,17721,18249,18349,26211,26368,26419,26593,27183,29599,29677,29754,29827,29917,29987,30039,31569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,324,414,635,680,755,862,944,1217,1280,1375,1444,1502,1566,1639,1728,2064,2141,2207,2266,2319,2415,2574,2695,2752,2842,2933,3020,3098,3199,3526,3606,3684,3749,3812,3876,3939,4012,4094,4194,4318,4417,4515,4607,4681,4760,4846,5053,5264,5382,5516,5575,6437,6544,6609", "endColumns": "114,153,89,220,44,74,106,81,272,62,94,68,57,63,72,88,335,76,65,58,52,95,158,120,56,89,90,86,77,100,326,79,77,64,62,63,62,72,81,99,123,98,97,91,73,78,85,206,210,117,133,58,861,106,64,65", "endOffsets": "165,319,409,630,675,750,857,939,1212,1275,1370,1439,1497,1561,1634,1723,2059,2136,2202,2261,2314,2410,2569,2690,2747,2837,2928,3015,3093,3194,3521,3601,3679,3744,3807,3871,3934,4007,4089,4189,4313,4412,4510,4602,4676,4755,4841,5048,5259,5377,5511,5570,6432,6539,6604,6670"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6797,6912,7066,7251,8398,8443,8518,8625,8707,8980,9043,11231,11515,11727,12030,12103,12467,13191,13268,13880,14858,15070,15166,15325,15446,15503,16107,16398,16870,17037,17138,17465,17545,17792,17974,18037,18354,18975,19048,19221,19429,21261,23781,23879,23971,24045,24124,25861,26598,26809,26927,27061,27188,28050,28157,30044", "endColumns": "114,153,89,220,44,74,106,81,272,62,94,68,57,63,72,88,335,76,65,58,52,95,158,120,56,89,90,86,77,100,326,79,77,64,62,63,62,72,81,99,123,98,97,91,73,78,85,206,210,117,133,58,861,106,64,65", "endOffsets": "6907,7061,7151,7467,8438,8513,8620,8702,8975,9038,9133,11295,11568,11786,12098,12187,12798,13263,13329,13934,14906,15161,15320,15441,15498,15588,16193,16480,16943,17133,17460,17540,17618,17852,18032,18096,18412,19043,19125,19316,19548,21355,23874,23966,24040,24119,24205,26063,26804,26922,27056,27115,28045,28152,28217,30105"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,227,311,406,503,594,684,838,933,1033,1127,1262,1328,1445,1513,1690,1905,2071,2162,2270,2371,2472,2665,2781,2887,3185,3299,3450,3657,3882,3978,4105,4306,4401,4464,4532,4625,4724,4850,4928,5027,5117,5234,5451,5524,5591,5667,5793,5880,5964,6036,6125,6198,6269,6399,6486,6602,6708,6784,6867,6937,7027,7091,7198,7324,7433,7570,7637,7732,7839,7978,8050,8145,8262,8322,8382,8468,8572,8749,9050,9381,9490,9576,9665,9736,9855,9978,10064,10138,10213,10304,10393,10488,10638,10799,10874,10957,11023,11192,11260,11321,11400,11474,11552,11633,11770,11913,12036,12134,12213,12307,12378", "endColumns": "83,87,83,94,96,90,89,153,94,99,93,134,65,116,67,176,214,165,90,107,100,100,192,115,105,297,113,150,206,224,95,126,200,94,62,67,92,98,125,77,98,89,116,216,72,66,75,125,86,83,71,88,72,70,129,86,115,105,75,82,69,89,63,106,125,108,136,66,94,106,138,71,94,116,59,59,85,103,176,300,330,108,85,88,70,118,122,85,73,74,90,88,94,149,160,74,82,65,168,67,60,78,73,77,80,136,142,122,97,78,93,70,170", "endOffsets": "134,222,306,401,498,589,679,833,928,1028,1122,1257,1323,1440,1508,1685,1900,2066,2157,2265,2366,2467,2660,2776,2882,3180,3294,3445,3652,3877,3973,4100,4301,4396,4459,4527,4620,4719,4845,4923,5022,5112,5229,5446,5519,5586,5662,5788,5875,5959,6031,6120,6193,6264,6394,6481,6597,6703,6779,6862,6932,7022,7086,7193,7319,7428,7565,7632,7727,7834,7973,8045,8140,8257,8317,8377,8463,8567,8744,9045,9376,9485,9571,9660,9731,9850,9973,10059,10133,10208,10299,10388,10483,10633,10794,10869,10952,11018,11187,11255,11316,11395,11469,11547,11628,11765,11908,12031,12129,12208,12302,12373,12544"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1096,1180,1268,7156,9138,9235,9326,11573,12852,12997,13097,15785,17726,17857,18101,18417,18594,18809,19130,19321,19553,19654,19755,19948,20064,20170,20468,20582,20733,20940,21165,21360,21487,21688,21783,21846,21914,22007,22106,22232,22310,22409,22499,22616,22833,22906,22973,23049,23175,23262,23346,23418,23507,23580,23651,24210,24297,24413,24519,24595,24678,24748,24838,24902,25009,25135,25244,25381,25448,25543,25650,25789,26068,26424,28222,28282,28342,28428,28532,28709,29010,29341,29450,30110,30199,30270,30389,30512,30598,30672,30747,30838,30927,31022,31172,31333,31408,31574,31640,31809,31877,31938,32017,32091,32169,32250,32387,32530,32653,32751,32830,32924,32995", "endColumns": "83,87,83,94,96,90,89,153,94,99,93,134,65,116,67,176,214,165,90,107,100,100,192,115,105,297,113,150,206,224,95,126,200,94,62,67,92,98,125,77,98,89,116,216,72,66,75,125,86,83,71,88,72,70,129,86,115,105,75,82,69,89,63,106,125,108,136,66,94,106,138,71,94,116,59,59,85,103,176,300,330,108,85,88,70,118,122,85,73,74,90,88,94,149,160,74,82,65,168,67,60,78,73,77,80,136,142,122,97,78,93,70,170", "endOffsets": "1175,1263,1347,7246,9230,9321,9411,11722,12942,13092,13186,15915,17787,17969,18164,18589,18804,18970,19216,19424,19649,19750,19943,20059,20165,20463,20577,20728,20935,21160,21256,21482,21683,21778,21841,21909,22002,22101,22227,22305,22404,22494,22611,22828,22901,22968,23044,23170,23257,23341,23413,23502,23575,23646,23776,24292,24408,24514,24590,24673,24743,24833,24897,25004,25130,25239,25376,25443,25538,25645,25784,25856,26158,26536,28277,28337,28423,28527,28704,29005,29336,29445,29531,30194,30265,30384,30507,30593,30667,30742,30833,30922,31017,31167,31328,31403,31486,31635,31804,31872,31933,32012,32086,32164,32245,32382,32525,32648,32746,32825,32919,32990,33161"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-el-rGR\\values-el-rGR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,138,197,259,341,406,478,538,618,700,766,830", "endColumns": "82,58,61,81,64,71,59,79,81,65,63,78", "endOffsets": "133,192,254,336,401,473,533,613,695,761,825,904"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1729,2560,2701,2763,2845,3133,3676,3874,4135,4503,4867,5167", "endColumns": "82,58,61,81,64,71,59,79,81,65,63,78", "endOffsets": "1807,2614,2758,2840,2905,3200,3731,3949,4212,4564,4926,5241"}}]}]}