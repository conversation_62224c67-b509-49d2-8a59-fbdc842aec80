<variant
    name="release"
    package="net.wolverinebeach.flutter_timezone"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\default_proguard_files\global\proguard-android.txt-8.3.2"
    partialResultsDir="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin;src\release\java;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\javac\release\compileReleaseJavaWithJavac\classes;C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\tmp\kotlin-classes\release;C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="net.wolverinebeach.flutter_timezone"
      generatedSourceFolders="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\generated\ap_generated_sources\release\out"
      generatedResourceFolders="C:\Users\<USER>\Downloads\Velvete\APP\Project 01\flutter_velvete_1\LabelStoreMax\build\flutter_timezone\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d161a5ad5c894ff6fa6a547ea57aa9c3\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
