{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,324,419,633,680,751,852,926,1146,1206,1298,1370,1427,1491,1563,1651,1971,2043,2110,2164,2221,2305,2437,2551,2609,2698,2779,2868,2934,3035,3338,3415,3493,3556,3617,3679,3740,3814,3897,3997,4098,4188,4291,4388,4463,4542,4628,4842,5053,5168,5294,5350,6002,6106,6171", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "166,319,414,628,675,746,847,921,1141,1201,1293,1365,1422,1486,1558,1646,1966,2038,2105,2159,2216,2300,2432,2546,2604,2693,2774,2863,2929,3030,3333,3410,3488,3551,3612,3674,3735,3809,3892,3992,4093,4183,4286,4383,4458,4537,4623,4837,5048,5163,5289,5345,5997,6101,6166,6221"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20712,20828,20981,21172,22211,22258,22329,22430,22504,22724,22784,24747,25016,25211,25515,25587,25949,26654,26726,27268,28199,28407,28491,28623,28737,28795,29348,29588,30014,30153,30254,30557,30634,30873,31051,31112,31419,31974,32048,32223,32429,34153,36559,36662,36759,36834,36913,38586,39266,39477,39592,39718,39841,40493,40597,42386", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "20823,20976,21071,21381,22253,22324,22425,22499,22719,22779,22871,24814,25068,25270,25582,25670,26264,26721,26788,27317,28251,28486,28618,28732,28790,28879,29424,29672,30075,30249,30552,30629,30707,30931,31107,31169,31475,32043,32126,32318,32525,34238,36657,36754,36829,36908,36994,38795,39472,39587,39713,39769,40488,40592,40657,42436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3222,3292,3357,4047,4712,4790,4907,5022,5077,5171,5257,5329,5419,5516,5573,5667,5718,5796,5907,5978,6048,6115,6181,6229,6313,6411,6484,6534,6581,6646,6704,6767,6959,7124,7263,7328,7414,7489,7578,7660,7737,7806,7897,7970,8075,8161,8256,8309,8425,8475,8529,8596,8668,8741,8810,8885,8975,9045,9097", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3217,3287,3352,4042,4707,4785,4902,5017,5072,5166,5252,5324,5414,5511,5568,5662,5713,5791,5902,5973,6043,6110,6176,6224,6308,6406,6479,6529,6576,6641,6699,6762,6954,7119,7258,7323,7409,7484,7573,7655,7732,7801,7892,7965,8070,8156,8251,8304,8420,8470,8524,8591,8663,8736,8805,8880,8970,9040,9092,9171"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14821,14895,14978,15040,15114,15197,15260,15571,15625,15701,15774,16001,16185,16287,16441,16798,17087,18105,18349,18444,18516,18676,18764,18852,19022,19329,19421,19500,19599,19673,19759,19846,19929,20012,20115,20268,20510,20612,21386,21456,21521,23127,23792,23870,23987,24102,24157,24251,24337,24409,24499,24596,24653,25275,25326,25404,25675,25746,25816,25883,26414,26793,26877,26975,27048,27098,27386,27451,27509,27572,27764,27929,28256,28321,29184,29259,29429,29511,29854,29923,30080,30712,31238,31324,38892,38945,39061,39212,39774,41885,41957,42030,42099,42174,42264,42334,43706", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "14890,14973,15035,15109,15192,15255,15314,15620,15696,15769,15848,16098,16282,16368,16514,16874,17166,18191,18439,18511,18603,18759,18847,18955,19099,19416,19495,19594,19668,19754,19841,19924,20007,20110,20183,20340,20607,20707,21451,21516,22206,23787,23865,23982,24097,24152,24246,24332,24404,24494,24591,24648,24742,25321,25399,25510,25741,25811,25878,25944,26457,26872,26970,27043,27093,27140,27446,27504,27567,27759,27924,28063,28316,28402,29254,29343,29506,29583,29918,30009,30148,30812,31319,31414,38940,39056,39106,39261,39836,41952,42025,42094,42169,42259,42329,42381,43780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "8130,45722", "endColumns": "60,79", "endOffsets": "8186,45797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,224,307,403,489,572,654,792,887,988,1079,1203,1259,1374,1438,1609,1792,1932,2024,2130,2225,2324,2492,2602,2702,2980,3093,3232,3433,3652,3753,3876,4072,4173,4234,4300,4382,4482,4583,4660,4761,4849,4959,5172,5240,5309,5388,5515,5589,5674,5736,5815,5886,5954,6069,6156,6263,6360,6435,6517,6586,6677,6737,6842,6949,7053,7183,7243,7343,7451,7585,7656,7748,7849,7909,7972,8050,8168,8337,8606,8905,9000,9072,9157,9229,9341,9454,9541,9617,9690,9776,9864,9951,10065,10198,10266,10337,10394,10557,10627,10684,10763,10840,10920,10999,11126,11252,11366,11454,11531,11615,11686", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "127,219,302,398,484,567,649,787,882,983,1074,1198,1254,1369,1433,1604,1787,1927,2019,2125,2220,2319,2487,2597,2697,2975,3088,3227,3428,3647,3748,3871,4067,4168,4229,4295,4377,4477,4578,4655,4756,4844,4954,5167,5235,5304,5383,5510,5584,5669,5731,5810,5881,5949,6064,6151,6258,6355,6430,6512,6581,6672,6732,6837,6944,7048,7178,7238,7338,7446,7580,7651,7743,7844,7904,7967,8045,8163,8332,8601,8900,8995,9067,9152,9224,9336,9449,9536,9612,9685,9771,9859,9946,10060,10193,10261,10332,10389,10552,10622,10679,10758,10835,10915,10994,11121,11247,11361,11449,11526,11610,11681,11845"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15319,15396,15488,21076,22876,22962,23045,25073,26319,26462,26563,29060,30817,30936,31174,31480,31651,31834,32131,32323,32530,32625,32724,32892,33002,33102,33380,33493,33632,33833,34052,34243,34366,34562,34663,34724,34790,34872,34972,35073,35150,35251,35339,35449,35662,35730,35799,35878,36005,36079,36164,36226,36305,36376,36444,36999,37086,37193,37290,37365,37447,37516,37607,37667,37772,37879,37983,38113,38173,38273,38381,38515,38800,39111,40662,40722,40785,40863,40981,41150,41419,41718,41813,42441,42526,42598,42710,42823,42910,42986,43059,43145,43233,43320,43434,43567,43635,43785,43842,44005,44075,44132,44211,44288,44368,44447,44574,44700,44814,44902,44979,45063,45134", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "15391,15483,15566,21167,22957,23040,23122,25206,26409,26558,26649,29179,30868,31046,31233,31646,31829,31969,32218,32424,32620,32719,32887,32997,33097,33375,33488,33627,33828,34047,34148,34361,34557,34658,34719,34785,34867,34967,35068,35145,35246,35334,35444,35657,35725,35794,35873,36000,36074,36159,36221,36300,36371,36439,36554,37081,37188,37285,37360,37442,37511,37602,37662,37767,37874,37978,38108,38168,38268,38376,38510,38581,38887,39207,40717,40780,40858,40976,41145,41414,41713,41808,41880,42521,42593,42705,42818,42905,42981,43054,43140,43228,43315,43429,43562,43630,43701,43837,44000,44070,44127,44206,44283,44363,44442,44569,44695,44809,44897,44974,45058,45129,45293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4706,7355,7647", "endColumns": "75,81,73", "endOffsets": "4777,7432,7716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4782,4887,5035,5162,5270,5437,5567,5689,5939,6109,6217,6381,6511,6668,6825,6894,6960", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4882,5030,5157,5265,5432,5562,5684,5789,6104,6212,6376,6506,6663,6820,6889,6955,7039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,7159,7256,7437,8270,8353,13209,13300,13387,13459,13605,13690,14026,45298,45374,45441", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,7251,7350,7518,8348,8445,13295,13382,13454,13523,13685,13775,14097,45369,45436,45549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5794", "endColumns": "144", "endOffsets": "5934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,14181", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,14277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15919,16737,16879,16941,17023,17311,17843,18034,18274,18608,18960,19257", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "15996,16793,16936,17018,17082,17379,17897,18100,18344,18671,19017,19324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14282,14369,14477,14544,14612,14678,14752", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "14364,14472,14539,14607,14673,14747,14816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,7523,7583,7721,8191,8450,8510,8600,8664,8735,8798,8873,8937,8991,9118,9176,9238,9292,9371,9512,9599,9675,9770,9851,9933,10072,10155,10239,10378,10465,10545,10601,10652,10718,10792,10872,10943,11026,11099,11176,11245,11319,11421,11509,11586,11679,11775,11849,11929,12026,12078,12162,12228,12315,12403,12465,12529,12592,12660,12769,12880,12984,13094,13154,13528,13866,13949,14102", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,7578,7642,7808,8265,8505,8595,8659,8730,8793,8868,8932,8986,9113,9171,9233,9287,9366,9507,9594,9670,9765,9846,9928,10067,10150,10234,10373,10460,10540,10596,10647,10713,10787,10867,10938,11021,11094,11171,11240,11314,11416,11504,11581,11674,11770,11844,11924,12021,12073,12157,12223,12310,12398,12460,12524,12587,12655,12764,12875,12979,13089,13149,13204,13600,13944,14021,14176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "45554,45637", "endColumns": "82,84", "endOffsets": "45632,45717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,13780", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,13861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7044,7813,7912,8024", "endColumns": "114,98,111,105", "endOffsets": "7154,7907,8019,8125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15853,16103,16373,16519,16605,16676,17171,17244,17384,17446,17514,17584,17644,17705,17779,17902,17971,18196,19104,19169,20188,20345,20424,24819,24925,26269,27145,27193,27322,28068,28130,28884,28971,29677,29773", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "15914,16180,16436,16600,16671,16732,17239,17306,17441,17509,17579,17639,17700,17774,17838,17966,18029,18269,19164,19252,20263,20419,20505,24920,25011,26314,27188,27263,27381,28125,28194,28966,29055,29768,29849"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,324,419,633,680,751,852,926,1146,1206,1298,1370,1427,1491,1563,1651,1971,2043,2110,2164,2221,2305,2437,2551,2609,2698,2779,2868,2934,3035,3338,3415,3493,3556,3617,3679,3740,3814,3897,3997,4098,4188,4291,4388,4463,4542,4628,4842,5053,5168,5294,5350,6002,6106,6171", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "166,319,414,628,675,746,847,921,1141,1201,1293,1365,1422,1486,1558,1646,1966,2038,2105,2159,2216,2300,2432,2546,2604,2693,2774,2863,2929,3030,3333,3410,3488,3551,3612,3674,3735,3809,3892,3992,4093,4183,4286,4383,4458,4537,4623,4837,5048,5163,5289,5345,5997,6101,6166,6221"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,313,314,315,316,317,323,326,331,333,334,335,336,339,341,342,346,350,351,353,355,367,392,393,394,395,396,414,421,422,423,424,426,427,428,445", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20712,20828,20981,21172,22211,22258,22329,22430,22504,22724,22784,24747,25016,25211,25515,25587,25949,26654,26726,27268,28199,28407,28491,28623,28737,28795,29348,29588,30014,30153,30254,30557,30634,30873,31051,31112,31419,31974,32048,32223,32429,34153,36559,36662,36759,36834,36913,38586,39266,39477,39592,39718,39841,40493,40597,42386", "endColumns": "115,152,94,213,46,70,100,73,219,59,91,71,56,63,71,87,319,71,66,53,56,83,131,113,57,88,80,88,65,100,302,76,77,62,60,61,60,73,82,99,100,89,102,96,74,78,85,213,210,114,125,55,651,103,64,54", "endOffsets": "20823,20976,21071,21381,22253,22324,22425,22499,22719,22779,22871,24814,25068,25270,25582,25670,26264,26721,26788,27317,28251,28486,28618,28732,28790,28879,29424,29672,30075,30249,30552,30629,30707,30931,31107,31169,31475,32043,32126,32318,32525,34238,36657,36754,36829,36908,36994,38795,39472,39587,39713,39769,40488,40592,40657,42436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,348,431,494,553,607,683,756,835,937,1039,1125,1203,1284,1368,1459,1554,1626,1718,1806,1894,2002,2084,2176,2255,2354,2428,2514,2601,2684,2767,2870,2943,3020,3122,3222,3292,3357,4047,4712,4790,4907,5022,5077,5171,5257,5329,5419,5516,5573,5667,5718,5796,5907,5978,6048,6115,6181,6229,6313,6411,6484,6534,6581,6646,6704,6767,6959,7124,7263,7328,7414,7489,7578,7660,7737,7806,7897,7970,8075,8161,8256,8309,8425,8475,8529,8596,8668,8741,8810,8885,8975,9045,9097", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "124,207,269,343,426,489,548,602,678,751,830,932,1034,1120,1198,1279,1363,1454,1549,1621,1713,1801,1889,1997,2079,2171,2250,2349,2423,2509,2596,2679,2762,2865,2938,3015,3117,3217,3287,3352,4042,4707,4785,4902,5017,5072,5166,5252,5324,5414,5511,5568,5662,5713,5791,5902,5973,6043,6110,6176,6224,6308,6406,6479,6529,6576,6641,6699,6762,6954,7119,7258,7323,7409,7484,7573,7655,7732,7801,7892,7965,8070,8156,8251,8304,8420,8470,8524,8591,8663,8736,8805,8880,8970,9040,9092,9171"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,321,322,324,325,329,330,332,337,344,345,416,417,418,420,425,438,439,440,441,442,443,444,460", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14821,14895,14978,15040,15114,15197,15260,15571,15625,15701,15774,16001,16185,16287,16441,16798,17087,18105,18349,18444,18516,18676,18764,18852,19022,19329,19421,19500,19599,19673,19759,19846,19929,20012,20115,20268,20510,20612,21386,21456,21521,23127,23792,23870,23987,24102,24157,24251,24337,24409,24499,24596,24653,25275,25326,25404,25675,25746,25816,25883,26414,26793,26877,26975,27048,27098,27386,27451,27509,27572,27764,27929,28256,28321,29184,29259,29429,29511,29854,29923,30080,30712,31238,31324,38892,38945,39061,39212,39774,41885,41957,42030,42099,42174,42264,42334,43706", "endColumns": "73,82,61,73,82,62,58,53,75,72,78,101,101,85,77,80,83,90,94,71,91,87,87,107,81,91,78,98,73,85,86,82,82,102,72,76,101,99,69,64,689,664,77,116,114,54,93,85,71,89,96,56,93,50,77,110,70,69,66,65,47,83,97,72,49,46,64,57,62,191,164,138,64,85,74,88,81,76,68,90,72,104,85,94,52,115,49,53,66,71,72,68,74,89,69,51,78", "endOffsets": "14890,14973,15035,15109,15192,15255,15314,15620,15696,15769,15848,16098,16282,16368,16514,16874,17166,18191,18439,18511,18603,18759,18847,18955,19099,19416,19495,19594,19668,19754,19841,19924,20007,20110,20183,20340,20607,20707,21451,21516,22206,23787,23865,23982,24097,24152,24246,24332,24404,24494,24591,24648,24742,25321,25399,25510,25741,25811,25878,25944,26457,26872,26970,27043,27093,27140,27446,27504,27567,27759,27924,28063,28316,28402,29254,29343,29506,29583,29918,30009,30148,30812,31319,31414,38940,39056,39106,39261,39836,41952,42025,42094,42169,42259,42329,42381,43780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "206,263", "endColumns": "56,75", "endOffsets": "262,338"}, "to": {"startLines": "81,482", "startColumns": "4,4", "startOffsets": "8130,45722", "endColumns": "60,79", "endOffsets": "8186,45797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,224,307,403,489,572,654,792,887,988,1079,1203,1259,1374,1438,1609,1792,1932,2024,2130,2225,2324,2492,2602,2702,2980,3093,3232,3433,3652,3753,3876,4072,4173,4234,4300,4382,4482,4583,4660,4761,4849,4959,5172,5240,5309,5388,5515,5589,5674,5736,5815,5886,5954,6069,6156,6263,6360,6435,6517,6586,6677,6737,6842,6949,7053,7183,7243,7343,7451,7585,7656,7748,7849,7909,7972,8050,8168,8337,8606,8905,9000,9072,9157,9229,9341,9454,9541,9617,9690,9776,9864,9951,10065,10198,10266,10337,10394,10557,10627,10684,10763,10840,10920,10999,11126,11252,11366,11454,11531,11615,11686", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "127,219,302,398,484,567,649,787,882,983,1074,1198,1254,1369,1433,1604,1787,1927,2019,2125,2220,2319,2487,2597,2697,2975,3088,3227,3428,3647,3748,3871,4067,4168,4229,4295,4377,4477,4578,4655,4756,4844,4954,5167,5235,5304,5383,5510,5584,5669,5731,5810,5881,5949,6064,6151,6258,6355,6430,6512,6581,6672,6732,6837,6944,7048,7178,7238,7338,7446,7580,7651,7743,7844,7904,7967,8045,8163,8332,8601,8900,8995,9067,9152,9224,9336,9449,9536,9612,9685,9771,9859,9946,10060,10193,10261,10332,10389,10552,10622,10679,10758,10835,10915,10994,11121,11247,11361,11449,11526,11610,11681,11845"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,320,338,340,343,347,348,349,352,354,356,357,358,359,360,361,362,363,364,365,366,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,415,419,429,430,431,432,433,434,435,436,437,446,447,448,449,450,451,452,453,454,455,456,457,458,459,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15319,15396,15488,21076,22876,22962,23045,25073,26319,26462,26563,29060,30817,30936,31174,31480,31651,31834,32131,32323,32530,32625,32724,32892,33002,33102,33380,33493,33632,33833,34052,34243,34366,34562,34663,34724,34790,34872,34972,35073,35150,35251,35339,35449,35662,35730,35799,35878,36005,36079,36164,36226,36305,36376,36444,36999,37086,37193,37290,37365,37447,37516,37607,37667,37772,37879,37983,38113,38173,38273,38381,38515,38800,39111,40662,40722,40785,40863,40981,41150,41419,41718,41813,42441,42526,42598,42710,42823,42910,42986,43059,43145,43233,43320,43434,43567,43635,43785,43842,44005,44075,44132,44211,44288,44368,44447,44574,44700,44814,44902,44979,45063,45134", "endColumns": "76,91,82,95,85,82,81,137,94,100,90,123,55,114,63,170,182,139,91,105,94,98,167,109,99,277,112,138,200,218,100,122,195,100,60,65,81,99,100,76,100,87,109,212,67,68,78,126,73,84,61,78,70,67,114,86,106,96,74,81,68,90,59,104,106,103,129,59,99,107,133,70,91,100,59,62,77,117,168,268,298,94,71,84,71,111,112,86,75,72,85,87,86,113,132,67,70,56,162,69,56,78,76,79,78,126,125,113,87,76,83,70,163", "endOffsets": "15391,15483,15566,21167,22957,23040,23122,25206,26409,26558,26649,29179,30868,31046,31233,31646,31829,31969,32218,32424,32620,32719,32887,32997,33097,33375,33488,33627,33828,34047,34148,34361,34557,34658,34719,34785,34867,34967,35068,35145,35246,35334,35444,35657,35725,35794,35873,36000,36074,36159,36221,36300,36371,36439,36554,37081,37188,37285,37360,37442,37511,37602,37662,37767,37874,37978,38108,38168,38268,38376,38510,38581,38887,39207,40717,40780,40858,40976,41145,41414,41713,41808,41880,42521,42593,42705,42818,42905,42981,43054,43140,43228,43315,43429,43562,43630,43701,43837,44000,44070,44127,44206,44283,44363,44442,44569,44695,44809,44897,44974,45058,45129,45293"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,213", "endColumns": "75,81,73", "endOffsets": "126,208,282"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4706,7355,7647", "endColumns": "75,81,73", "endOffsets": "4777,7432,7716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4782,4887,5035,5162,5270,5437,5567,5689,5939,6109,6217,6381,6511,6668,6825,6894,6960", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "4882,5030,5157,5265,5432,5562,5684,5789,6104,6212,6376,6506,6663,6820,6889,6955,7039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4525,4620,7159,7256,7437,8270,8353,13209,13300,13387,13459,13605,13690,14026,45298,45374,45441", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4615,4701,7251,7350,7518,8348,8445,13295,13382,13454,13523,13685,13775,14097,45369,45436,45549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5794", "endColumns": "144", "endOffsets": "5934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3488,3585,3687,3786,3886,3993,4103,14181", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3580,3682,3781,3881,3988,4098,4218,14277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,198,260,342,406,479,538,609,684,752,814", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "132,193,255,337,401,474,533,604,679,747,809,881"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15919,16737,16879,16941,17023,17311,17843,18034,18274,18608,18960,19257", "endColumns": "81,60,61,81,63,72,58,70,74,67,61,71", "endOffsets": "15996,16793,16936,17018,17082,17379,17897,18100,18344,18671,19017,19324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,250,317,385,451,525", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "137,245,312,380,446,520,589"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "14282,14369,14477,14544,14612,14678,14752", "endColumns": "86,107,66,67,65,73,68", "endOffsets": "14364,14472,14539,14607,14673,14747,14816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,354,432,516,611,700,801,921,1002,1062,1126,1218,1297,1357,1447,1511,1582,1645,1720,1784,1838,1965,2023,2085,2139,2218,2359,2446,2522,2617,2698,2780,2919,3002,3086,3225,3312,3392,3448,3499,3565,3639,3719,3790,3873,3946,4023,4092,4166,4268,4356,4433,4526,4622,4696,4776,4873,4925,5009,5075,5162,5250,5312,5376,5439,5507,5616,5727,5831,5941,6001,6056,6133,6216,6293", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "268,349,427,511,606,695,796,916,997,1057,1121,1213,1292,1352,1442,1506,1577,1640,1715,1779,1833,1960,2018,2080,2134,2213,2354,2441,2517,2612,2693,2775,2914,2997,3081,3220,3307,3387,3443,3494,3560,3634,3714,3785,3868,3941,4018,4087,4161,4263,4351,4428,4521,4617,4691,4771,4868,4920,5004,5070,5157,5245,5307,5371,5434,5502,5611,5722,5826,5936,5996,6051,6128,6211,6288,6367"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3061,3142,3220,3304,3399,4223,4324,4444,7523,7583,7721,8191,8450,8510,8600,8664,8735,8798,8873,8937,8991,9118,9176,9238,9292,9371,9512,9599,9675,9770,9851,9933,10072,10155,10239,10378,10465,10545,10601,10652,10718,10792,10872,10943,11026,11099,11176,11245,11319,11421,11509,11586,11679,11775,11849,11929,12026,12078,12162,12228,12315,12403,12465,12529,12592,12660,12769,12880,12984,13094,13154,13528,13866,13949,14102", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,80,77,83,94,88,100,119,80,59,63,91,78,59,89,63,70,62,74,63,53,126,57,61,53,78,140,86,75,94,80,81,138,82,83,138,86,79,55,50,65,73,79,70,82,72,76,68,73,101,87,76,92,95,73,79,96,51,83,65,86,87,61,63,62,67,108,110,103,109,59,54,76,82,76,78", "endOffsets": "318,3137,3215,3299,3394,3483,4319,4439,4520,7578,7642,7808,8265,8505,8595,8659,8730,8793,8868,8932,8986,9113,9171,9233,9287,9366,9507,9594,9670,9765,9846,9928,10067,10150,10234,10373,10460,10540,10596,10647,10713,10787,10867,10938,11021,11094,11171,11240,11314,11416,11504,11581,11674,11770,11844,11924,12021,12073,12157,12223,12310,12398,12460,12524,12587,12655,12764,12875,12979,13089,13149,13204,13600,13944,14021,14176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,84", "endOffsets": "133,218"}, "to": {"startLines": "480,481", "startColumns": "4,4", "startOffsets": "45554,45637", "endColumns": "82,84", "endOffsets": "45632,45717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,443,549,656,745,846,965,1050,1130,1221,1314,1409,1503,1603,1696,1791,1886,1977,2068,2153,2260,2371,2473,2581,2689,2799,2961,13780", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "438,544,651,740,841,960,1045,1125,1216,1309,1404,1498,1598,1691,1786,1881,1972,2063,2148,2255,2366,2468,2576,2684,2794,2956,3056,13861"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7044,7813,7912,8024", "endColumns": "114,98,111,105", "endOffsets": "7154,7907,8019,8125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,203,271,357,428,489,562,629,691,759,829,889,950,1024,1088,1157,1220,1298,1363,1451,1531,1610,1696,1802,1893,1943,1991,2066,2130,2192,2261,2348,2437,2533", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "116,198,266,352,423,484,557,624,686,754,824,884,945,1019,1083,1152,1215,1293,1358,1446,1526,1605,1691,1797,1888,1938,1986,2061,2125,2187,2256,2343,2432,2528,2609"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,318,319,327,328", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15853,16103,16373,16519,16605,16676,17171,17244,17384,17446,17514,17584,17644,17705,17779,17902,17971,18196,19104,19169,20188,20345,20424,24819,24925,26269,27145,27193,27322,28068,28130,28884,28971,29677,29773", "endColumns": "65,81,67,85,70,60,72,66,61,67,69,59,60,73,63,68,62,77,64,87,79,78,85,105,90,49,47,74,63,61,68,86,88,95,80", "endOffsets": "15914,16180,16436,16600,16671,16732,17239,17306,17441,17509,17579,17639,17700,17774,17838,17966,18029,18269,19164,19252,20263,20419,20505,24920,25011,26314,27188,27263,27381,28125,28194,28966,29055,29768,29849"}}]}]}