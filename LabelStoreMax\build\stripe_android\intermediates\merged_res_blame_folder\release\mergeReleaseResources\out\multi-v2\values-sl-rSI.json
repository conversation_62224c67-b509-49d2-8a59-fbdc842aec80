{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-sl-rSI/values-sl-rSI.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,356,457,665,711,782,883,955,1183,1248,1341,1415,1470,1534,1607,1696,1994,2065,2131,2184,2238,2319,2437,2546,2604,2687,2768,2856,2924,3041,3353,3443,3520,3583,3643,3706,3767,3838,3920,4022,4115,4206,4312,4412,4488,4567,4658,4856,5063,5181,5314,5383,6099,6199,6261", "endColumns": "130,169,100,207,45,70,100,71,227,64,92,73,54,63,72,88,297,70,65,52,53,80,117,108,57,82,80,87,67,116,311,89,76,62,59,62,60,70,81,101,92,90,105,99,75,78,90,197,206,117,132,68,715,99,61,54", "endOffsets": "181,351,452,660,706,777,878,950,1178,1243,1336,1410,1465,1529,1602,1691,1989,2060,2126,2179,2233,2314,2432,2541,2599,2682,2763,2851,2919,3036,3348,3438,3515,3578,3638,3701,3762,3833,3915,4017,4110,4201,4307,4407,4483,4562,4653,4851,5058,5176,5309,5378,6094,6194,6256,6311"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6559,6690,6860,7072,8134,8180,8251,8352,8424,8652,8717,10667,10936,11108,11430,11503,11882,12581,12652,13222,14169,14380,14461,14579,14688,14746,15342,15601,16044,16192,16309,16621,16711,16959,17139,17199,17515,18087,18158,18328,18529,20278,22724,22830,22930,23006,23085,24784,25501,25708,25826,25959,26102,26818,26918,28758", "endColumns": "130,169,100,207,45,70,100,71,227,64,92,73,54,63,72,88,297,70,65,52,53,80,117,108,57,82,80,87,67,116,311,89,76,62,59,62,60,70,81,101,92,90,105,99,75,78,90,197,206,117,132,68,715,99,61,54", "endOffsets": "6685,6855,6956,7275,8175,8246,8347,8419,8647,8712,8805,10736,10986,11167,11498,11587,12175,12647,12713,13270,14218,14456,14574,14683,14741,14824,15418,15684,16107,16304,16616,16706,16783,17017,17194,17257,17571,18153,18235,18425,18617,20364,22825,22925,23001,23080,23171,24977,25703,25821,25954,26023,26813,26913,26975,28808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,187,251,336,398,467,525,607,684,751,820", "endColumns": "71,59,63,84,61,68,57,81,76,66,68,73", "endOffsets": "122,182,246,331,393,462,520,602,679,746,815,889"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1683,2470,2609,2673,2758,3036,3572,3768,4026,4366,4726,5031", "endColumns": "71,59,63,84,61,68,57,81,76,66,68,73", "endOffsets": "1750,2525,2668,2753,2815,3100,3625,3845,4098,4428,4790,5100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,208,282,393,484,565,645,762,873,974,1065,1202,1269,1386,1459,1638,1820,1970,2058,2157,2250,2347,2543,2652,2754,3030,3134,3269,3483,3708,3813,3934,4126,4219,4279,4342,4431,4523,4625,4701,4800,4895,5004,5226,5295,5365,5438,5564,5653,5744,5813,5897,5969,6048,6168,6258,6377,6482,6559,6655,6725,6814,6874,6975,7080,7181,7313,7377,7470,7568,7704,7776,7864,7980,8037,8108,8194,8301,8479,8773,9090,9185,9269,9361,9432,9535,9646,9732,9818,9897,9985,10071,10165,10292,10451,10519,10592,10649,10820,10888,10947,11027,11099,11175,11258,11393,11507,11612,11699,11777,11855,11926", "endColumns": "70,81,73,110,90,80,79,116,110,100,90,136,66,116,72,178,181,149,87,98,92,96,195,108,101,275,103,134,213,224,104,120,191,92,59,62,88,91,101,75,98,94,108,221,68,69,72,125,88,90,68,83,71,78,119,89,118,104,76,95,69,88,59,100,104,100,131,63,92,97,135,71,87,115,56,70,85,106,177,293,316,94,83,91,70,102,110,85,85,78,87,85,93,126,158,67,72,56,170,67,58,79,71,75,82,134,113,104,86,77,77,70,152", "endOffsets": "121,203,277,388,479,560,640,757,868,969,1060,1197,1264,1381,1454,1633,1815,1965,2053,2152,2245,2342,2538,2647,2749,3025,3129,3264,3478,3703,3808,3929,4121,4214,4274,4337,4426,4518,4620,4696,4795,4890,4999,5221,5290,5360,5433,5559,5648,5739,5808,5892,5964,6043,6163,6253,6372,6477,6554,6650,6720,6809,6869,6970,7075,7176,7308,7372,7465,7563,7699,7771,7859,7975,8032,8103,8189,8296,8474,8768,9085,9180,9264,9356,9427,9530,9641,9727,9813,9892,9980,10066,10160,10287,10446,10514,10587,10644,10815,10883,10942,11022,11094,11170,11253,11388,11502,11607,11694,11772,11850,11921,12074"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1097,1168,1250,6961,8810,8901,8982,10991,12231,12389,12490,15033,16892,17022,17262,17576,17755,17937,18240,18430,18622,18715,18812,19008,19117,19219,19495,19599,19734,19948,20173,20369,20490,20682,20775,20835,20898,20987,21079,21181,21257,21356,21451,21560,21782,21851,21921,21994,22120,22209,22300,22369,22453,22525,22604,23176,23266,23385,23490,23567,23663,23733,23822,23882,23983,24088,24189,24321,24385,24478,24576,24712,24982,25311,26980,27037,27108,27194,27301,27479,27773,28090,28185,28813,28905,28976,29079,29190,29276,29362,29441,29529,29615,29709,29836,29995,30063,30215,30272,30443,30511,30570,30650,30722,30798,30881,31016,31130,31235,31322,31400,31478,31549", "endColumns": "70,81,73,110,90,80,79,116,110,100,90,136,66,116,72,178,181,149,87,98,92,96,195,108,101,275,103,134,213,224,104,120,191,92,59,62,88,91,101,75,98,94,108,221,68,69,72,125,88,90,68,83,71,78,119,89,118,104,76,95,69,88,59,100,104,100,131,63,92,97,135,71,87,115,56,70,85,106,177,293,316,94,83,91,70,102,110,85,85,78,87,85,93,126,158,67,72,56,170,67,58,79,71,75,82,134,113,104,86,77,77,70,152", "endOffsets": "1163,1245,1319,7067,8896,8977,9057,11103,12337,12485,12576,15165,16954,17134,17330,17750,17932,18082,18323,18524,18710,18807,19003,19112,19214,19490,19594,19729,19943,20168,20273,20485,20677,20770,20830,20893,20982,21074,21176,21252,21351,21446,21555,21777,21846,21916,21989,22115,22204,22295,22364,22448,22520,22599,22719,23261,23380,23485,23562,23658,23728,23817,23877,23978,24083,24184,24316,24380,24473,24571,24707,24779,25065,25422,27032,27103,27189,27296,27474,27768,28085,28180,28264,28900,28971,29074,29185,29271,29357,29436,29524,29610,29704,29831,29990,30058,30131,30267,30438,30506,30565,30645,30717,30793,30876,31011,31125,31230,31317,31395,31473,31544,31697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,355,425,486,554,620,685,753,828,888,949,1023,1087,1162,1225,1300,1367,1448,1523,1612,1707,1819,1902,1953,2010,2095,2157,2221,2289,2384,2493,2588", "endColumns": "63,80,66,87,69,60,67,65,64,67,74,59,60,73,63,74,62,74,66,80,74,88,94,111,82,50,56,84,61,63,67,94,108,94,92", "endOffsets": "114,195,262,350,420,481,549,615,680,748,823,883,944,1018,1082,1157,1220,1295,1362,1443,1518,1607,1702,1814,1897,1948,2005,2090,2152,2216,2284,2379,2488,2583,2676"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1619,1846,2101,2251,2339,2409,2902,2970,3105,3170,3238,3313,3373,3434,3508,3630,3705,3951,4883,4950,5998,6153,6242,10741,10853,12180,13080,13137,13275,14037,14101,14829,14924,15689,15784", "endColumns": "63,80,66,87,69,60,67,65,64,67,74,59,60,73,63,74,62,74,66,80,74,88,94,111,82,50,56,84,61,63,67,94,108,94,92", "endOffsets": "1678,1922,2163,2334,2404,2465,2965,3031,3165,3233,3308,3368,3429,3503,3567,3700,3763,4021,4945,5026,6068,6237,6332,10848,10931,12226,13132,13217,13332,14096,14164,14919,15028,15779,15872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,344,423,488,559,626,697,780,854,945,1036,1119,1202,1281,1363,1464,1560,1634,1727,1813,1907,2020,2108,2201,2282,2382,2457,2556,2653,2746,2824,2929,3001,3081,3195,3303,3373,3440,4157,4850,4928,5037,5143,5198,5283,5367,5436,5530,5624,5680,5762,5813,5905,6020,6100,6177,6244,6310,6357,6436,6543,6623,6673,6719,6791,6849,6909,7093,7264,7419,7490,7576,7656,7748,7838,7926,8002,8093,8173,8277,8365,8457,8514,8647,8698,8772,8846,8914,8987,9054,9125,9212,9284,9335", "endColumns": "73,82,61,69,78,64,70,66,70,82,73,90,90,82,82,78,81,100,95,73,92,85,93,112,87,92,80,99,74,98,96,92,77,104,71,79,113,107,69,66,716,692,77,108,105,54,84,83,68,93,93,55,81,50,91,114,79,76,66,65,46,78,106,79,49,45,71,57,59,183,170,154,70,85,79,91,89,87,75,90,79,103,87,91,56,132,50,73,73,67,72,66,70,86,71,50,78", "endOffsets": "124,207,269,339,418,483,554,621,692,775,849,940,1031,1114,1197,1276,1358,1459,1555,1629,1722,1808,1902,2015,2103,2196,2277,2377,2452,2551,2648,2741,2819,2924,2996,3076,3190,3298,3368,3435,4152,4845,4923,5032,5138,5193,5278,5362,5431,5525,5619,5675,5757,5808,5900,6015,6095,6172,6239,6305,6352,6431,6538,6618,6668,6714,6786,6844,6904,7088,7259,7414,7485,7571,7651,7743,7833,7921,7997,8088,8168,8272,8360,8452,8509,8642,8693,8767,8841,8909,8982,9049,9120,9207,9279,9330,9409"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "593,667,750,812,882,961,1026,1324,1391,1462,1545,1755,1927,2018,2168,2530,2820,3850,4103,4199,4273,4433,4519,4613,4795,5105,5198,5279,5379,5454,5553,5650,5743,5821,5926,6073,6337,6451,7280,7350,7417,9062,9755,9833,9942,10048,10103,10188,10272,10341,10435,10529,10585,11172,11223,11315,11592,11672,11749,11816,12342,12718,12797,12904,12984,13034,13337,13409,13467,13527,13711,13882,14223,14294,15170,15250,15423,15513,15877,15953,16112,16788,17335,17423,25070,25127,25260,25427,26028,28269,28337,28410,28477,28548,28635,28707,30136", "endColumns": "73,82,61,69,78,64,70,66,70,82,73,90,90,82,82,78,81,100,95,73,92,85,93,112,87,92,80,99,74,98,96,92,77,104,71,79,113,107,69,66,716,692,77,108,105,54,84,83,68,93,93,55,81,50,91,114,79,76,66,65,46,78,106,79,49,45,71,57,59,183,170,154,70,85,79,91,89,87,75,90,79,103,87,91,56,132,50,73,73,67,72,66,70,86,71,50,78", "endOffsets": "662,745,807,877,956,1021,1092,1386,1457,1540,1614,1841,2013,2096,2246,2604,2897,3946,4194,4268,4361,4514,4608,4721,4878,5193,5274,5374,5449,5548,5645,5738,5816,5921,5993,6148,6446,6554,7345,7412,8129,9750,9828,9937,10043,10098,10183,10267,10336,10430,10524,10580,10662,11218,11310,11425,11667,11744,11811,11877,12384,12792,12899,12979,13029,13075,13404,13462,13522,13706,13877,14032,14289,14375,15245,15337,15508,15596,15948,16039,16187,16887,17418,17510,25122,25255,25306,25496,26097,28332,28405,28472,28543,28630,28702,28753,30210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,244,310,377,443,523", "endColumns": "86,101,65,66,65,79,69", "endOffsets": "137,239,305,372,438,518,588"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-sl-rSI/values-sl-rSI.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,356,457,665,711,782,883,955,1183,1248,1341,1415,1470,1534,1607,1696,1994,2065,2131,2184,2238,2319,2437,2546,2604,2687,2768,2856,2924,3041,3353,3443,3520,3583,3643,3706,3767,3838,3920,4022,4115,4206,4312,4412,4488,4567,4658,4856,5063,5181,5314,5383,6099,6199,6261", "endColumns": "130,169,100,207,45,70,100,71,227,64,92,73,54,63,72,88,297,70,65,52,53,80,117,108,57,82,80,87,67,116,311,89,76,62,59,62,60,70,81,101,92,90,105,99,75,78,90,197,206,117,132,68,715,99,61,54", "endOffsets": "181,351,452,660,706,777,878,950,1178,1243,1336,1410,1465,1529,1602,1691,1989,2060,2126,2179,2233,2314,2432,2541,2599,2682,2763,2851,2919,3036,3348,3438,3515,3578,3638,3701,3762,3833,3915,4017,4110,4201,4307,4407,4483,4562,4653,4851,5058,5176,5309,5378,6094,6194,6256,6311"}, "to": {"startLines": "85,86,87,89,93,94,95,96,97,98,99,115,118,120,124,125,130,136,137,145,155,158,159,160,161,162,168,171,176,178,179,180,181,184,186,187,191,195,196,198,200,212,237,238,239,240,241,259,266,267,268,269,271,272,273,290", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6559,6690,6860,7072,8134,8180,8251,8352,8424,8652,8717,10667,10936,11108,11430,11503,11882,12581,12652,13222,14169,14380,14461,14579,14688,14746,15342,15601,16044,16192,16309,16621,16711,16959,17139,17199,17515,18087,18158,18328,18529,20278,22724,22830,22930,23006,23085,24784,25501,25708,25826,25959,26102,26818,26918,28758", "endColumns": "130,169,100,207,45,70,100,71,227,64,92,73,54,63,72,88,297,70,65,52,53,80,117,108,57,82,80,87,67,116,311,89,76,62,59,62,60,70,81,101,92,90,105,99,75,78,90,197,206,117,132,68,715,99,61,54", "endOffsets": "6685,6855,6956,7275,8175,8246,8347,8419,8647,8712,8805,10736,10986,11167,11498,11587,12175,12647,12713,13270,14218,14456,14574,14683,14741,14824,15418,15684,16107,16304,16616,16706,16783,17017,17194,17257,17571,18153,18235,18425,18617,20364,22825,22925,23001,23080,23171,24977,25703,25821,25954,26023,26813,26913,26975,28808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,187,251,336,398,467,525,607,684,751,820", "endColumns": "71,59,63,84,61,68,57,81,76,66,68,73", "endOffsets": "122,182,246,331,393,462,520,602,679,746,815,889"}, "to": {"startLines": "24,34,36,37,38,42,50,53,56,60,64,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1683,2470,2609,2673,2758,3036,3572,3768,4026,4366,4726,5031", "endColumns": "71,59,63,84,61,68,57,81,76,66,68,73", "endOffsets": "1750,2525,2668,2753,2815,3100,3625,3845,4098,4428,4790,5100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,208,282,393,484,565,645,762,873,974,1065,1202,1269,1386,1459,1638,1820,1970,2058,2157,2250,2347,2543,2652,2754,3030,3134,3269,3483,3708,3813,3934,4126,4219,4279,4342,4431,4523,4625,4701,4800,4895,5004,5226,5295,5365,5438,5564,5653,5744,5813,5897,5969,6048,6168,6258,6377,6482,6559,6655,6725,6814,6874,6975,7080,7181,7313,7377,7470,7568,7704,7776,7864,7980,8037,8108,8194,8301,8479,8773,9090,9185,9269,9361,9432,9535,9646,9732,9818,9897,9985,10071,10165,10292,10451,10519,10592,10649,10820,10888,10947,11027,11099,11175,11258,11393,11507,11612,11699,11777,11855,11926", "endColumns": "70,81,73,110,90,80,79,116,110,100,90,136,66,116,72,178,181,149,87,98,92,96,195,108,101,275,103,134,213,224,104,120,191,92,59,62,88,91,101,75,98,94,108,221,68,69,72,125,88,90,68,83,71,78,119,89,118,104,76,95,69,88,59,100,104,100,131,63,92,97,135,71,87,115,56,70,85,106,177,293,316,94,83,91,70,102,110,85,85,78,87,85,93,126,158,67,72,56,170,67,58,79,71,75,82,134,113,104,86,77,77,70,152", "endOffsets": "121,203,277,388,479,560,640,757,868,969,1060,1197,1264,1381,1454,1633,1815,1965,2053,2152,2245,2342,2538,2647,2749,3025,3129,3264,3478,3703,3808,3929,4121,4214,4274,4337,4426,4518,4620,4696,4795,4890,4999,5221,5290,5360,5433,5559,5648,5739,5808,5892,5964,6043,6163,6253,6372,6477,6554,6650,6720,6809,6869,6970,7075,7176,7308,7372,7465,7563,7699,7771,7859,7975,8032,8103,8189,8296,8474,8768,9085,9180,9264,9356,9427,9530,9641,9727,9813,9892,9980,10066,10160,10287,10446,10514,10587,10644,10815,10883,10942,11022,11094,11170,11253,11388,11502,11607,11694,11772,11850,11921,12074"}, "to": {"startLines": "16,17,18,88,100,101,102,119,132,134,135,165,183,185,188,192,193,194,197,199,201,202,203,204,205,206,207,208,209,210,211,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,260,264,274,275,276,277,278,279,280,281,282,291,292,293,294,295,296,297,298,299,300,301,302,303,304,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1097,1168,1250,6961,8810,8901,8982,10991,12231,12389,12490,15033,16892,17022,17262,17576,17755,17937,18240,18430,18622,18715,18812,19008,19117,19219,19495,19599,19734,19948,20173,20369,20490,20682,20775,20835,20898,20987,21079,21181,21257,21356,21451,21560,21782,21851,21921,21994,22120,22209,22300,22369,22453,22525,22604,23176,23266,23385,23490,23567,23663,23733,23822,23882,23983,24088,24189,24321,24385,24478,24576,24712,24982,25311,26980,27037,27108,27194,27301,27479,27773,28090,28185,28813,28905,28976,29079,29190,29276,29362,29441,29529,29615,29709,29836,29995,30063,30215,30272,30443,30511,30570,30650,30722,30798,30881,31016,31130,31235,31322,31400,31478,31549", "endColumns": "70,81,73,110,90,80,79,116,110,100,90,136,66,116,72,178,181,149,87,98,92,96,195,108,101,275,103,134,213,224,104,120,191,92,59,62,88,91,101,75,98,94,108,221,68,69,72,125,88,90,68,83,71,78,119,89,118,104,76,95,69,88,59,100,104,100,131,63,92,97,135,71,87,115,56,70,85,106,177,293,316,94,83,91,70,102,110,85,85,78,87,85,93,126,158,67,72,56,170,67,58,79,71,75,82,134,113,104,86,77,77,70,152", "endOffsets": "1163,1245,1319,7067,8896,8977,9057,11103,12337,12485,12576,15165,16954,17134,17330,17750,17932,18082,18323,18524,18710,18807,19003,19112,19214,19490,19594,19729,19943,20168,20273,20485,20677,20770,20830,20893,20982,21074,21176,21252,21351,21446,21555,21777,21846,21916,21989,22115,22204,22295,22364,22448,22520,22599,22719,23261,23380,23485,23562,23658,23728,23817,23877,23978,24083,24184,24316,24380,24473,24571,24707,24779,25065,25422,27032,27103,27189,27296,27474,27768,28085,28180,28264,28900,28971,29074,29185,29271,29357,29436,29524,29610,29704,29831,29990,30058,30131,30267,30438,30506,30565,30645,30717,30793,30876,31011,31125,31230,31317,31395,31473,31544,31697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,119,200,267,355,425,486,554,620,685,753,828,888,949,1023,1087,1162,1225,1300,1367,1448,1523,1612,1707,1819,1902,1953,2010,2095,2157,2221,2289,2384,2493,2588", "endColumns": "63,80,66,87,69,60,67,65,64,67,74,59,60,73,63,74,62,74,66,80,74,88,94,111,82,50,56,84,61,63,67,94,108,94,92", "endOffsets": "114,195,262,350,420,481,549,615,680,748,823,883,944,1018,1082,1157,1220,1295,1362,1443,1518,1607,1702,1814,1897,1948,2005,2090,2152,2216,2284,2379,2488,2583,2676"}, "to": {"startLines": "23,26,29,31,32,33,40,41,43,44,45,46,47,48,49,51,52,55,66,67,79,81,82,116,117,131,143,144,146,153,154,163,164,172,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1619,1846,2101,2251,2339,2409,2902,2970,3105,3170,3238,3313,3373,3434,3508,3630,3705,3951,4883,4950,5998,6153,6242,10741,10853,12180,13080,13137,13275,14037,14101,14829,14924,15689,15784", "endColumns": "63,80,66,87,69,60,67,65,64,67,74,59,60,73,63,74,62,74,66,80,74,88,94,111,82,50,56,84,61,63,67,94,108,94,92", "endOffsets": "1678,1922,2163,2334,2404,2465,2965,3031,3165,3233,3308,3368,3429,3503,3567,3700,3763,4021,4945,5026,6068,6237,6332,10848,10931,12226,13132,13217,13332,14096,14164,14919,15028,15779,15872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,274,344,423,488,559,626,697,780,854,945,1036,1119,1202,1281,1363,1464,1560,1634,1727,1813,1907,2020,2108,2201,2282,2382,2457,2556,2653,2746,2824,2929,3001,3081,3195,3303,3373,3440,4157,4850,4928,5037,5143,5198,5283,5367,5436,5530,5624,5680,5762,5813,5905,6020,6100,6177,6244,6310,6357,6436,6543,6623,6673,6719,6791,6849,6909,7093,7264,7419,7490,7576,7656,7748,7838,7926,8002,8093,8173,8277,8365,8457,8514,8647,8698,8772,8846,8914,8987,9054,9125,9212,9284,9335", "endColumns": "73,82,61,69,78,64,70,66,70,82,73,90,90,82,82,78,81,100,95,73,92,85,93,112,87,92,80,99,74,98,96,92,77,104,71,79,113,107,69,66,716,692,77,108,105,54,84,83,68,93,93,55,81,50,91,114,79,76,66,65,46,78,106,79,49,45,71,57,59,183,170,154,70,85,79,91,89,87,75,90,79,103,87,91,56,132,50,73,73,67,72,66,70,86,71,50,78", "endOffsets": "124,207,269,339,418,483,554,621,692,775,849,940,1031,1114,1197,1276,1358,1459,1555,1629,1722,1808,1902,2015,2103,2196,2277,2377,2452,2551,2648,2741,2819,2924,2996,3076,3190,3298,3368,3435,4152,4845,4923,5032,5138,5193,5278,5362,5431,5525,5619,5675,5757,5808,5900,6015,6095,6172,6239,6305,6352,6431,6538,6618,6668,6714,6786,6844,6904,7088,7259,7414,7485,7571,7651,7743,7833,7921,7997,8088,8168,8272,8360,8452,8509,8642,8693,8767,8841,8909,8982,9049,9120,9207,9279,9330,9409"}, "to": {"startLines": "9,10,11,12,13,14,15,19,20,21,22,25,27,28,30,35,39,54,57,58,59,61,62,63,65,69,70,71,72,73,74,75,76,77,78,80,83,84,90,91,92,103,104,105,106,107,108,109,110,111,112,113,114,121,122,123,126,127,128,129,133,138,139,140,141,142,147,148,149,150,151,152,156,157,166,167,169,170,174,175,177,182,189,190,261,262,263,265,270,283,284,285,286,287,288,289,305", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "593,667,750,812,882,961,1026,1324,1391,1462,1545,1755,1927,2018,2168,2530,2820,3850,4103,4199,4273,4433,4519,4613,4795,5105,5198,5279,5379,5454,5553,5650,5743,5821,5926,6073,6337,6451,7280,7350,7417,9062,9755,9833,9942,10048,10103,10188,10272,10341,10435,10529,10585,11172,11223,11315,11592,11672,11749,11816,12342,12718,12797,12904,12984,13034,13337,13409,13467,13527,13711,13882,14223,14294,15170,15250,15423,15513,15877,15953,16112,16788,17335,17423,25070,25127,25260,25427,26028,28269,28337,28410,28477,28548,28635,28707,30136", "endColumns": "73,82,61,69,78,64,70,66,70,82,73,90,90,82,82,78,81,100,95,73,92,85,93,112,87,92,80,99,74,98,96,92,77,104,71,79,113,107,69,66,716,692,77,108,105,54,84,83,68,93,93,55,81,50,91,114,79,76,66,65,46,78,106,79,49,45,71,57,59,183,170,154,70,85,79,91,89,87,75,90,79,103,87,91,56,132,50,73,73,67,72,66,70,86,71,50,78", "endOffsets": "662,745,807,877,956,1021,1092,1386,1457,1540,1614,1841,2013,2096,2246,2604,2897,3946,4194,4268,4361,4514,4608,4721,4878,5193,5274,5374,5449,5548,5645,5738,5816,5921,5993,6148,6446,6554,7345,7412,8129,9750,9828,9937,10043,10098,10183,10267,10336,10430,10524,10580,10662,11218,11310,11425,11667,11744,11811,11877,12384,12792,12899,12979,13029,13075,13404,13462,13522,13706,13877,14032,14289,14375,15245,15337,15508,15596,15948,16039,16187,16887,17418,17510,25122,25255,25306,25496,26097,28332,28405,28472,28543,28630,28702,28753,30210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-sl-rSI\\values-sl-rSI.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,142,244,310,377,443,523", "endColumns": "86,101,65,66,65,79,69", "endOffsets": "137,239,305,372,438,518,588"}}]}]}