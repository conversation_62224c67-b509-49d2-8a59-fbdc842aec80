{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "94,104,105,106", "startColumns": "4,4,4,4", "startOffsets": "9247,10056,10156,10262", "endColumns": "90,99,105,101", "endOffsets": "9333,10151,10257,10359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1077,1142,1230,1300,1363,1455,1518,1578,1637,1700,1761,1815,1917,1974,2033,2087,2155,2266,2347,2422,2509,2589,2671,2803,2874,2947,3071,3159,3235,3288,3342,3408,3481,3557,3628,3706,3776,3851,3933,4001,4102,4187,4257,4347,4438,4512,4585,4674,4725,4806,4873,4955,5040,5102,5166,5229,5297,5391,5486,5576,5673,5730,5788,5863,5945,6020", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "306,383,458,535,635,726,819,932,1012,1072,1137,1225,1295,1358,1450,1513,1573,1632,1695,1756,1810,1912,1969,2028,2082,2150,2261,2342,2417,2504,2584,2666,2798,2869,2942,3066,3154,3230,3283,3337,3403,3476,3552,3623,3701,3771,3846,3928,3996,4097,4182,4252,4342,4433,4507,4580,4669,4720,4801,4868,4950,5035,5097,5161,5224,5292,5386,5481,5571,5668,5725,5783,5858,5940,6015,6091"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,99,100,103,108,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,175,180,181,183", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3017,3094,3169,3246,3346,4134,4227,4340,9686,9746,9968,10425,10657,10720,10812,10875,10935,10994,11057,11118,11172,11274,11331,11390,11444,11512,11623,11704,11779,11866,11946,12028,12160,12231,12304,12428,12516,12592,12645,12699,12765,12838,12914,12985,13063,13133,13208,13290,13358,13459,13544,13614,13704,13795,13869,13942,14031,14082,14163,14230,14312,14397,14459,14523,14586,14654,14748,14843,14933,15030,15087,15518,15960,16042,16186", "endLines": "6,34,35,36,37,38,46,47,48,99,100,103,108,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,175,180,181,183", "endColumns": "12,76,74,76,99,90,92,112,79,59,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,74,86,79,81,131,70,72,123,87,75,52,53,65,72,75,70,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74,81,74,75", "endOffsets": "356,3089,3164,3241,3341,3432,4222,4335,4415,9741,9806,10051,10490,10715,10807,10870,10930,10989,11052,11113,11167,11269,11326,11385,11439,11507,11618,11699,11774,11861,11941,12023,12155,12226,12299,12423,12511,12587,12640,12694,12760,12833,12909,12980,13058,13128,13203,13285,13353,13454,13539,13609,13699,13790,13864,13937,14026,14077,14158,14225,14307,14392,14454,14518,14581,14649,14743,14838,14928,15025,15082,15140,15588,16037,16112,16257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,15878", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,15955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,942,1008,1086,1168,1237,1311,1382", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,937,1003,1081,1163,1232,1306,1377,1496"}, "to": {"startLines": "49,50,95,96,98,109,110,170,171,173,174,177,178,182,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4420,4509,9338,9431,9603,10495,10572,15145,15231,15386,15452,15718,15796,16117,16605,16679,16750", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "4504,4588,9426,9521,9681,10567,10652,15226,15305,15447,15513,15791,15873,16181,16674,16745,16864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,258,334,459,628,709", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "169,253,329,454,623,704,783"}, "to": {"startLines": "93,101,172,176,186,192,193", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9178,9811,15310,15593,16436,17044,17125", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "9242,9890,15381,15713,16600,17120,17199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "39,40,41,42,43,44,45,184", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3437,3531,3633,3730,3827,3928,4028,16262", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3526,3628,3725,3822,3923,4023,4129,16358"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "8072", "endColumns": "117", "endOffsets": "8185"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "107,194", "startColumns": "4,4", "startOffsets": "10364,17204", "endColumns": "60,75", "endOffsets": "10420,17275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,206", "endColumns": "73,76,72", "endOffsets": "124,201,274"}, "to": {"startLines": "51,97,102", "startColumns": "4,4,4", "startOffsets": "4593,9526,9895", "endColumns": "73,76,72", "endOffsets": "4662,9598,9963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7107,7210,7364,7489,7593,7732,7857,7969,8190,8326,8430,8575,8698,8832,8977,9037,9097", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "7205,7359,7484,7588,7727,7852,7964,8067,8321,8425,8570,8693,8827,8972,9032,9092,9173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,87", "endOffsets": "137,225"}, "to": {"startLines": "190,191", "startColumns": "4,4", "startOffsets": "16869,16956", "endColumns": "86,87", "endOffsets": "16951,17039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,209,330,458,570,658,743,817,905,995,1109,1220,1311,1402,1502,1619,1700,1782,1950,2044,2143,2260,2364", "endColumns": "153,120,127,111,87,84,73,87,89,113,110,90,90,99,116,80,81,167,93,98,116,103,130", "endOffsets": "204,325,453,565,653,738,812,900,990,1104,1215,1306,1397,1497,1614,1695,1777,1945,2039,2138,2255,2359,2490"}, "to": {"startLines": "52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4667,4821,4942,5070,5182,5270,5355,5429,5517,5607,5721,5832,5923,6014,6114,6231,6312,6394,6562,6656,6755,6872,6976", "endColumns": "153,120,127,111,87,84,73,87,89,113,110,90,90,99,116,80,81,167,93,98,116,103,130", "endOffsets": "4816,4937,5065,5177,5265,5350,5424,5512,5602,5716,5827,5918,6009,6109,6226,6307,6389,6557,6651,6750,6867,6971,7102"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "72", "endOffsets": "123"}, "to": {"startLines": "185", "startColumns": "4", "startOffsets": "16363", "endColumns": "72", "endOffsets": "16431"}}]}]}