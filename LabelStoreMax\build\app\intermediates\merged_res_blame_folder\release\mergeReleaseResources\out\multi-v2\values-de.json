{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9792,10644,10745,10856", "endColumns": "103,100,110,99", "endOffsets": "9891,10740,10851,10951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,197,264,349,418,479,551,618,682,750,820,880,942,1015,1079,1149,1212,1286,1349,1434,1511,1598,1691,1803,1891,1940,1988,2074,2136,2211,2280,2379,2467,2565", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "115,192,259,344,413,474,546,613,677,745,815,875,937,1010,1074,1144,1207,1281,1344,1429,1506,1593,1686,1798,1886,1935,1983,2069,2131,2206,2275,2374,2462,2560,2654"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,346,347,355,356", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18877,19116,19376,19532,19617,19686,20187,20259,20404,20468,20536,20606,20666,20728,20801,20924,20994,21229,22147,22210,23310,23482,23569,28171,28283,29647,30554,30602,30741,31576,31651,32503,32602,33349,33447", "endColumns": "64,76,66,84,68,60,71,66,63,67,69,59,61,72,63,69,62,73,62,84,76,86,92,111,87,48,47,85,61,74,68,98,87,97,93", "endOffsets": "18937,19188,19438,19612,19681,19742,20254,20321,20463,20531,20601,20661,20723,20796,20860,20989,21052,21298,22205,22290,23382,23564,23657,28278,28366,29691,30597,30683,30798,31646,31715,32597,32685,33442,33536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,10257,10319,10550,11017,11264,11323,11431,11497,11566,11624,11696,11760,11814,11942,12002,12064,12118,12196,12333,12425,12503,12597,12683,12767,12912,12996,13082,13215,13305,13384,13441,13492,13558,13632,13714,13785,13860,13934,14012,14084,14158,14268,14360,14442,14531,14620,14694,14772,14858,14913,14992,15059,15139,15223,15285,15349,15412,15481,15588,15695,15794,15900,15961,16405,16874,16957,17109", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,10314,10380,10639,11082,11318,11426,11492,11561,11619,11691,11755,11809,11937,11997,12059,12113,12191,12328,12420,12498,12592,12678,12762,12907,12991,13077,13210,13300,13379,13436,13487,13553,13627,13709,13780,13855,13929,14007,14079,14153,14263,14355,14437,14526,14615,14689,14767,14853,14908,14987,15054,15134,15218,15280,15344,15407,15476,15583,15690,15789,15895,15956,16011,16482,16952,17029,17180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,89", "endOffsets": "137,227"}, "to": {"startLines": "509,510", "startColumns": "4,4", "startOffsets": "50318,50405", "endColumns": "86,89", "endOffsets": "50400,50490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,218,358,500,621,709,801,884,976,1072,1183,1292,1384,1476,1578,1695,1777,1860,2067,2163,2272,2383,2493", "endColumns": "162,139,141,120,87,91,82,91,95,110,108,91,91,101,116,81,82,206,95,108,110,109,137", "endOffsets": "213,353,495,616,704,796,879,971,1067,1178,1287,1379,1471,1573,1690,1772,1855,2062,2158,2267,2378,2488,2626"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4793,4956,5096,5238,5359,5447,5539,5622,5714,5810,5921,6030,6122,6214,6316,6433,6515,6598,6805,6901,7010,7121,7231", "endColumns": "162,139,141,120,87,91,82,91,95,110,108,91,91,101,116,81,82,206,95,108,110,109,137", "endOffsets": "4951,5091,5233,5354,5442,5534,5617,5709,5805,5916,6025,6117,6209,6311,6428,6510,6593,6800,6896,7005,7116,7226,7364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,204,273,342,420,482,541,597,684,766,854,951,1048,1134,1223,1301,1390,1486,1580,1651,1742,1827,1916,2029,2115,2206,2284,2382,2471,2575,2664,2755,2849,2974,3059,3154,3256,3351,3436,3501,4206,4899,4973,5103,5209,5264,5372,5472,5540,5634,5734,5791,5879,5931,6011,6114,6191,6263,6330,6396,6447,6528,6623,6705,6752,6803,6878,6936,6997,7217,7417,7576,7641,7730,7820,7916,8002,8089,8166,8255,8334,8447,8534,8624,8680,8825,8877,8932,9001,9070,9145,9209,9281,9370,9443,9499", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "120,199,268,337,415,477,536,592,679,761,849,946,1043,1129,1218,1296,1385,1481,1575,1646,1737,1822,1911,2024,2110,2201,2279,2377,2466,2570,2659,2750,2844,2969,3054,3149,3251,3346,3431,3496,4201,4894,4968,5098,5204,5259,5367,5467,5535,5629,5729,5786,5874,5926,6006,6109,6186,6258,6325,6391,6442,6523,6618,6700,6747,6798,6873,6931,6992,7212,7412,7571,7636,7725,7815,7911,7997,8084,8161,8250,8329,8442,8529,8619,8675,8820,8872,8927,8996,9065,9140,9204,9276,9365,9438,9494,9570"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,349,350,352,353,357,358,360,365,372,373,444,445,446,448,453,466,467,468,469,470,471,472,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17834,17904,17983,18052,18121,18199,18261,18564,18620,18707,18789,19019,19193,19290,19443,19805,20098,21133,21377,21471,21542,21708,21793,21882,22061,22366,22457,22535,22633,22722,22826,22915,23006,23100,23225,23387,23662,23764,24595,24680,24745,26430,27123,27197,27327,27433,27488,27596,27696,27764,27858,27958,28015,28636,28688,28768,29043,29120,29192,29259,29796,30198,30279,30374,30456,30503,30803,30878,30936,30997,31217,31417,31773,31838,32823,32913,33090,33176,33541,33618,33774,34472,35045,35132,42940,42996,43141,43300,43909,46300,46369,46444,46508,46580,46669,46742,48213", "endColumns": "69,78,68,68,77,61,58,55,86,81,87,96,96,85,88,77,88,95,93,70,90,84,88,112,85,90,77,97,88,103,88,90,93,124,84,94,101,94,84,64,704,692,73,129,105,54,107,99,67,93,99,56,87,51,79,102,76,71,66,65,50,80,94,81,46,50,74,57,60,219,199,158,64,88,89,95,85,86,76,88,78,112,86,89,55,144,51,54,68,68,74,63,71,88,72,55,75", "endOffsets": "17899,17978,18047,18116,18194,18256,18315,18615,18702,18784,18872,19111,19285,19371,19527,19878,20182,21224,21466,21537,21628,21788,21877,21990,22142,22452,22530,22628,22717,22821,22910,23001,23095,23220,23305,23477,23759,23854,24675,24740,25445,27118,27192,27322,27428,27483,27591,27691,27759,27853,27953,28010,28098,28683,28763,28866,29115,29187,29254,29320,29842,30274,30369,30451,30498,30549,30873,30931,30992,31212,31412,31571,31833,31922,32908,33004,33171,33258,33613,33702,33848,34580,35127,35217,42991,43136,43188,43350,43973,46364,46439,46503,46575,46664,46737,46793,48284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "31927", "endColumns": "91", "endOffsets": "32014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,209", "endColumns": "77,75,76", "endOffsets": "128,204,281"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4715,10094,10473", "endColumns": "77,75,76", "endOffsets": "4788,10165,10545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8431", "endColumns": "144", "endOffsets": "8571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,73", "endOffsets": "258,332"}, "to": {"startLines": "106,513", "startColumns": "4,4", "startOffsets": "10956,50651", "endColumns": "60,77", "endOffsets": "11012,50724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7369,7478,7642,7770,7882,8060,8191,8312,8576,8756,8868,9037,9168,9330,9506,9577,9640", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "7473,7637,7765,7877,8055,8186,8307,8426,8751,8863,9032,9163,9325,9501,9572,9635,9715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,247,315,382,449,529", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "140,242,310,377,444,524,598"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17286,17376,17478,17546,17613,17680,17760", "endColumns": "89,101,67,66,66,79,73", "endOffsets": "17371,17473,17541,17608,17675,17755,17829"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,218,299,399,489,567,644,790,890,1002,1101,1234,1295,1441,1508,1700,1908,2081,2174,2280,2377,2473,2669,2775,2871,3129,3245,3383,3601,3832,3935,4062,4274,4371,4435,4502,4593,4689,4780,4856,4954,5047,5150,5394,5466,5534,5610,5731,5808,5897,5982,6071,6143,6212,6337,6430,6534,6628,6704,6787,6858,6948,7011,7116,7234,7342,7472,7538,7634,7735,7878,7950,8042,8149,8208,8273,8359,8468,8658,8976,9311,9410,9496,9580,9653,9770,9907,9998,10073,10151,10246,10329,10421,10562,10714,10782,10856,10915,11099,11167,11226,11304,11378,11456,11544,11689,11828,11952,12044,12124,12210,12284", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "123,213,294,394,484,562,639,785,885,997,1096,1229,1290,1436,1503,1695,1903,2076,2169,2275,2372,2468,2664,2770,2866,3124,3240,3378,3596,3827,3930,4057,4269,4366,4430,4497,4588,4684,4775,4851,4949,5042,5145,5389,5461,5529,5605,5726,5803,5892,5977,6066,6138,6207,6332,6425,6529,6623,6699,6782,6853,6943,7006,7111,7229,7337,7467,7533,7629,7730,7873,7945,8037,8144,8203,8268,8354,8463,8653,8971,9306,9405,9491,9575,9648,9765,9902,9993,10068,10146,10241,10324,10416,10557,10709,10777,10851,10910,11094,11162,11221,11299,11373,11451,11539,11684,11823,11947,12039,12119,12205,12279,12446"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,348,366,368,371,375,376,377,380,382,384,385,386,387,388,389,390,391,392,393,394,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,443,447,457,458,459,460,461,462,463,464,465,474,475,476,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18320,18393,18483,24244,26185,26275,26353,28426,29696,29847,29959,32690,34585,34709,34978,35282,35474,35682,36012,36201,36406,36503,36599,36795,36901,36997,37255,37371,37509,37727,37958,38144,38271,38483,38580,38644,38711,38802,38898,38989,39065,39163,39256,39359,39603,39675,39743,39819,39940,40017,40106,40191,40280,40352,40421,40996,41089,41193,41287,41363,41446,41517,41607,41670,41775,41893,42001,42131,42197,42293,42394,42537,42848,43193,44953,45012,45077,45163,45272,45462,45780,46115,46214,46853,46937,47010,47127,47264,47355,47430,47508,47603,47686,47778,47919,48071,48139,48289,48348,48532,48600,48659,48737,48811,48889,48977,49122,49261,49385,49477,49557,49643,49717", "endColumns": "72,89,80,99,89,77,76,145,99,111,98,132,60,145,66,191,207,172,92,105,96,95,195,105,95,257,115,137,217,230,102,126,211,96,63,66,90,95,90,75,97,92,102,243,71,67,75,120,76,88,84,88,71,68,124,92,103,93,75,82,70,89,62,104,117,107,129,65,95,100,142,71,91,106,58,64,85,108,189,317,334,98,85,83,72,116,136,90,74,77,94,82,91,140,151,67,73,58,183,67,58,77,73,77,87,144,138,123,91,79,85,73,166", "endOffsets": "18388,18478,18559,24339,26270,26348,26425,28567,29791,29954,30053,32818,34641,34850,35040,35469,35677,35850,36100,36302,36498,36594,36790,36896,36992,37250,37366,37504,37722,37953,38056,38266,38478,38575,38639,38706,38797,38893,38984,39060,39158,39251,39354,39598,39670,39738,39814,39935,40012,40101,40186,40275,40347,40416,40541,41084,41188,41282,41358,41441,41512,41602,41665,41770,41888,41996,42126,42192,42288,42389,42532,42604,42935,43295,45007,45072,45158,45267,45457,45775,46110,46209,46295,46932,47005,47122,47259,47350,47425,47503,47598,47681,47773,47914,48066,48134,48208,48343,48527,48595,48654,48732,48806,48884,48972,49117,49256,49380,49472,49552,49638,49712,49879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,289,387,487,574,659,751,840,928,993,1057,1138,1222,1297,1376,1442", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "196,284,382,482,569,654,746,835,923,988,1052,1133,1217,1292,1371,1437,1557"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4531,4627,9896,9994,10170,11087,11172,16016,16105,16276,16341,16627,16708,17034,50053,50132,50198", "endColumns": "95,87,97,99,86,84,91,88,87,64,63,80,83,74,78,65,119", "endOffsets": "4622,4710,9989,10089,10252,11167,11259,16100,16188,16336,16400,16703,16787,17104,50127,50193,50313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,190,252,336,405,483,542,618,692,767,833", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "127,185,247,331,400,478,537,613,687,762,828,899"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18942,19747,19883,19945,20029,20326,20865,21057,21303,21633,21995,22295", "endColumns": "76,57,61,83,68,77,58,75,73,74,65,70", "endOffsets": "19014,19800,19940,20024,20093,20399,20919,21128,21372,21703,22056,22361"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,16792", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,16869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,337,440,691,738,805,906,975,1265,1328,1426,1494,1549,1613,1691,1785,2107,2183,2247,2300,2353,2444,2575,2691,2748,2837,2918,3004,3071,3177,3533,3613,3690,3753,3813,3876,3936,4010,4093,4189,4288,4371,4477,4577,4651,4730,4821,5060,5308,5425,5555,5614,6420,6524,6589", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "170,332,435,686,733,800,901,970,1260,1323,1421,1489,1544,1608,1686,1780,2102,2178,2242,2295,2348,2439,2570,2686,2743,2832,2913,2999,3066,3172,3528,3608,3685,3748,3808,3871,3931,4005,4088,4184,4283,4366,4472,4572,4646,4725,4816,5055,5303,5420,5550,5609,6415,6519,6584,6639"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,341,342,343,344,345,351,354,359,361,362,363,364,367,369,370,374,378,379,381,383,395,420,421,422,423,424,442,449,450,451,452,454,455,456,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23859,23979,24141,24344,25450,25497,25564,25665,25734,26024,26087,28103,28371,28572,28871,28949,29325,30058,30134,30688,31720,32019,32110,32241,32357,32414,33009,33263,33707,33853,33959,34315,34395,34646,34855,34915,35222,35855,35929,36105,36307,38061,40546,40652,40752,40826,40905,42609,43355,43603,43720,43850,43978,44784,44888,46798", "endColumns": "119,161,102,250,46,66,100,68,289,62,97,67,54,63,77,93,321,75,63,52,52,90,130,115,56,88,80,85,66,105,355,79,76,62,59,62,59,73,82,95,98,82,105,99,73,78,90,238,247,116,129,58,805,103,64,54", "endOffsets": "23974,24136,24239,24590,25492,25559,25660,25729,26019,26082,26180,28166,28421,28631,28944,29038,29642,30129,30193,30736,31768,32105,32236,32352,32409,32498,33085,33344,33769,33954,34310,34390,34467,34704,34910,34973,35277,35924,36007,36196,36401,38139,40647,40747,40821,40900,40991,42843,43598,43715,43845,43904,44779,44883,44948,46848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,348,488,657,737", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "172,260,343,483,652,732,808"}, "to": {"startLines": "92,100,171,175,505,511,512", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9720,10385,16193,16487,49884,50495,50575", "endColumns": "71,87,82,139,168,79,75", "endOffsets": "9787,10468,16271,16622,50048,50570,50646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,17185", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,17281"}}]}]}