{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3523,3621,3723,3822,3924,4028,4132,17430", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3616,3718,3817,3919,4023,4127,4245,17526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,213", "endColumns": "74,82,76", "endOffsets": "125,208,285"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4750,10245,10643", "endColumns": "74,82,76", "endOffsets": "4820,10323,10715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,94", "endOffsets": "139,234"}, "to": {"startLines": "509,510", "startColumns": "4,4", "startOffsets": "51061,51150", "endColumns": "88,94", "endOffsets": "51145,51240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "106,513", "startColumns": "4,4", "startOffsets": "11143,51411", "endColumns": "60,78", "endOffsets": "11199,51485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "340", "startColumns": "4", "startOffsets": "32308", "endColumns": "86", "endOffsets": "32390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,219,379,530,643,731,818,896,986,1083,1198,1312,1407,1502,1614,1733,1816,1901,2092,2186,2296,2418,2530", "endColumns": "163,159,150,112,87,86,77,89,96,114,113,94,94,111,118,82,84,190,93,109,121,111,165", "endOffsets": "214,374,525,638,726,813,891,981,1078,1193,1307,1402,1497,1609,1728,1811,1896,2087,2181,2291,2413,2525,2691"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4825,4989,5149,5300,5413,5501,5588,5666,5756,5853,5968,6082,6177,6272,6384,6503,6586,6671,6862,6956,7066,7188,7300", "endColumns": "163,159,150,112,87,86,77,89,96,114,113,94,94,111,118,82,84,190,93,109,121,111,165", "endOffsets": "4984,5144,5295,5408,5496,5583,5661,5751,5848,5963,6077,6172,6267,6379,6498,6581,6666,6857,6951,7061,7183,7295,7461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,228,290,366,451,512,571,649,727,815,901,1004,1107,1195,1278,1360,1450,1554,1649,1719,1811,1900,1997,2122,2204,2296,2373,2472,2548,2648,2748,2843,2928,3043,3118,3203,3325,3440,3523,3588,4351,5022,5101,5215,5326,5381,5492,5604,5672,5773,5873,5929,6017,6067,6150,6268,6344,6421,6488,6554,6602,6690,6793,6872,6920,6969,7055,7113,7177,7378,7574,7731,7799,7888,7974,8082,8190,8300,8383,8479,8559,8673,8775,8878,8932,9077,9129,9211,9280,9350,9426,9496,9570,9668,9748,9799", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "132,223,285,361,446,507,566,644,722,810,896,999,1102,1190,1273,1355,1445,1549,1644,1714,1806,1895,1992,2117,2199,2291,2368,2467,2543,2643,2743,2838,2923,3038,3113,3198,3320,3435,3518,3583,4346,5017,5096,5210,5321,5376,5487,5599,5667,5768,5868,5924,6012,6062,6145,6263,6339,6416,6483,6549,6597,6685,6788,6867,6915,6964,7050,7108,7172,7373,7569,7726,7794,7883,7969,8077,8185,8295,8378,8474,8554,8668,8770,8873,8927,9072,9124,9206,9275,9345,9421,9491,9565,9663,9743,9794,9873"}, "to": {"startLines": "191,192,193,194,195,196,197,201,202,203,204,207,209,210,212,217,221,236,239,240,241,243,244,245,247,251,252,253,254,255,256,257,258,259,260,262,265,266,272,273,274,285,286,287,288,289,290,291,292,293,294,295,296,303,304,305,308,309,310,311,315,320,321,322,323,324,329,330,331,332,333,334,338,339,349,350,352,353,357,358,360,365,372,373,444,445,446,448,453,466,467,468,469,470,471,472,488", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18073,18155,18246,18308,18384,18469,18530,18838,18916,18994,19082,19314,19498,19601,19756,20116,20410,21472,21714,21809,21879,22038,22127,22224,22409,22711,22803,22880,22979,23055,23155,23255,23350,23435,23550,23706,23979,24101,24878,24961,25026,26783,27454,27533,27647,27758,27813,27924,28036,28104,28205,28305,28361,29005,29055,29138,29418,29494,29571,29638,30160,30562,30650,30753,30832,30880,31188,31274,31332,31396,31597,31793,32151,32219,33240,33326,33522,33630,34052,34135,34302,34934,35504,35606,43632,43686,43831,43997,44624,46994,47064,47140,47210,47284,47382,47462,48975", "endColumns": "81,90,61,75,84,60,58,77,77,87,85,102,102,87,82,81,89,103,94,69,91,88,96,124,81,91,76,98,75,99,99,94,84,114,74,84,121,114,82,64,762,670,78,113,110,54,110,111,67,100,99,55,87,49,82,117,75,76,66,65,47,87,102,78,47,48,85,57,63,200,195,156,67,88,85,107,107,109,82,95,79,113,101,102,53,144,51,81,68,69,75,69,73,97,79,50,78", "endOffsets": "18150,18241,18303,18379,18464,18525,18584,18911,18989,19077,19163,19412,19596,19684,19834,20193,20495,21571,21804,21874,21966,22122,22219,22344,22486,22798,22875,22974,23050,23150,23250,23345,23430,23545,23620,23786,24096,24211,24956,25021,25784,27449,27528,27642,27753,27808,27919,28031,28099,28200,28300,28356,28444,29050,29133,29251,29489,29566,29633,29699,30203,30645,30748,30827,30875,30924,31269,31327,31391,31592,31788,31945,32214,32303,33321,33429,33625,33735,34130,34226,34377,35043,35601,35704,43681,43826,43878,44074,44688,47059,47135,47205,47279,47377,47457,47508,49049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8529", "endColumns": "164", "endOffsets": "8689"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7466,7572,7752,7882,7991,8162,8295,8416,8694,8872,8984,9169,9305,9465,9644,9717,9784", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "7567,7747,7877,7986,8157,8290,8411,8524,8867,8979,9164,9300,9460,9639,9712,9779,9863"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,201,268,353,424,485,557,630,692,764,834,902,962,1036,1112,1183,1246,1311,1376,1461,1542,1633,1730,1859,1941,1992,2040,2132,2196,2271,2342,2462,2551,2663", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "115,196,263,348,419,480,552,625,687,759,829,897,957,1031,1107,1178,1241,1306,1371,1456,1537,1628,1725,1854,1936,1987,2035,2127,2191,2266,2337,2457,2546,2658,2770"}, "to": {"startLines": "205,208,211,213,214,215,222,223,225,226,227,228,229,230,231,233,234,237,248,249,261,263,264,298,299,313,325,326,328,335,336,346,347,355,356", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19168,19417,19689,19839,19924,19995,20500,20572,20716,20778,20850,20920,20988,21048,21122,21256,21327,21576,22491,22556,23625,23791,23882,28523,28652,30025,30929,30977,31124,31950,32025,32898,33018,33828,33940", "endColumns": "64,80,66,84,70,60,71,72,61,71,69,67,59,73,75,70,62,64,64,84,80,90,96,128,81,50,47,91,63,74,70,119,88,111,111", "endOffsets": "19228,19493,19751,19919,19990,20051,20567,20640,20773,20845,20915,20983,21043,21117,21193,21322,21385,21636,22551,22636,23701,23877,23974,28647,28729,30071,30972,31064,31183,32020,32091,33013,33102,33935,34047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,363,444,527,636,731,829,959,1044,1109,1175,1272,1355,1421,1523,1588,1663,1719,1798,1858,1912,2034,2093,2155,2209,2291,2426,2518,2593,2688,2769,2853,2997,3076,3157,3298,3391,3470,3525,3576,3642,3722,3803,3874,3954,4027,4105,4178,4250,4362,4455,4527,4619,4711,4785,4869,4961,5018,5102,5168,5251,5338,5400,5464,5527,5605,5707,5811,5908,6012,6071,6126,6215,6302,6379", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "278,358,439,522,631,726,824,954,1039,1104,1170,1267,1350,1416,1518,1583,1658,1714,1793,1853,1907,2029,2088,2150,2204,2286,2421,2513,2588,2683,2764,2848,2992,3071,3152,3293,3386,3465,3520,3571,3637,3717,3798,3869,3949,4022,4100,4173,4245,4357,4450,4522,4614,4706,4780,4864,4956,5013,5097,5163,5246,5333,5395,5459,5522,5600,5702,5806,5903,6007,6066,6121,6210,6297,6374,6455"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3155,3236,3319,3428,4250,4348,4478,10415,10480,10720,11204,11458,11524,11626,11691,11766,11822,11901,11961,12015,12137,12196,12258,12312,12394,12529,12621,12696,12791,12872,12956,13100,13179,13260,13401,13494,13573,13628,13679,13745,13825,13906,13977,14057,14130,14208,14281,14353,14465,14558,14630,14722,14814,14888,14972,15064,15121,15205,15271,15354,15441,15503,15567,15630,15708,15810,15914,16011,16115,16174,16625,17109,17196,17349", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,79,80,82,108,94,97,129,84,64,65,96,82,65,101,64,74,55,78,59,53,121,58,61,53,81,134,91,74,94,80,83,143,78,80,140,92,78,54,50,65,79,80,70,79,72,77,72,71,111,92,71,91,91,73,83,91,56,83,65,82,86,61,63,62,77,101,103,96,103,58,54,88,86,76,80", "endOffsets": "328,3150,3231,3314,3423,3518,4343,4473,4558,10475,10541,10812,11282,11519,11621,11686,11761,11817,11896,11956,12010,12132,12191,12253,12307,12389,12524,12616,12691,12786,12867,12951,13095,13174,13255,13396,13489,13568,13623,13674,13740,13820,13901,13972,14052,14125,14203,14276,14348,14460,14553,14625,14717,14809,14883,14967,15059,15116,15200,15266,15349,15436,15498,15562,15625,15703,15805,15909,16006,16110,16169,16224,16709,17191,17268,17425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4563,4662,10045,10145,10328,11287,11366,16229,16321,16486,16557,16856,16937,17273,50792,50870,50939", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4657,4745,10140,10240,10410,11361,11453,16316,16403,16552,16620,16932,17017,17344,50865,50934,51056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9938,10817,10919,11038", "endColumns": "106,101,118,104", "endOffsets": "10040,10914,11033,11138"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,145,251,318,386,454,529", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "140,246,313,381,449,524,592"}, "to": {"startLines": "184,185,186,187,188,189,190", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17531,17621,17727,17794,17862,17930,18005", "endColumns": "89,105,66,67,67,74,67", "endOffsets": "17616,17722,17789,17857,17925,18000,18068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,221,304,388,482,566,649,801,885,1001,1105,1238,1304,1428,1493,1664,1863,2036,2126,2236,2338,2440,2633,2755,2857,3120,3231,3373,3604,3859,3959,4096,4306,4405,4466,4532,4617,4719,4822,4897,4997,5089,5194,5447,5517,5589,5670,5804,5879,5972,6039,6123,6194,6263,6383,6480,6594,6697,6785,6884,6955,7050,7115,7230,7347,7454,7596,7661,7765,7877,8028,8100,8196,8310,8372,8430,8509,8633,8813,9124,9458,9557,9637,9738,9814,9939,10096,10182,10270,10340,10427,10519,10616,10747,10889,10969,11041,11100,11264,11333,11394,11479,11555,11633,11727,11883,12004,12125,12217,12293,12375,12452", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "129,216,299,383,477,561,644,796,880,996,1100,1233,1299,1423,1488,1659,1858,2031,2121,2231,2333,2435,2628,2750,2852,3115,3226,3368,3599,3854,3954,4091,4301,4400,4461,4527,4612,4714,4817,4892,4992,5084,5189,5442,5512,5584,5665,5799,5874,5967,6034,6118,6189,6258,6378,6475,6589,6692,6780,6879,6950,7045,7110,7225,7342,7449,7591,7656,7760,7872,8023,8095,8191,8305,8367,8425,8504,8628,8808,9119,9453,9552,9632,9733,9809,9934,10091,10177,10265,10335,10422,10514,10611,10742,10884,10964,11036,11095,11259,11328,11389,11474,11550,11628,11722,11878,11999,12120,12212,12288,12370,12447,12605"}, "to": {"startLines": "198,199,200,270,282,283,284,301,314,316,317,348,366,368,371,375,376,377,380,382,384,385,386,387,388,389,390,391,392,393,394,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,443,447,457,458,459,460,461,462,463,464,465,474,475,476,477,478,479,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,498,499,500,501,502,503,504", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18589,18668,18755,24584,26522,26616,26700,28789,30076,30208,30324,33107,35048,35191,35439,35773,35944,36143,36479,36695,36910,37012,37114,37307,37429,37531,37794,37905,38047,38278,38533,38732,38869,39079,39178,39239,39305,39390,39492,39595,39670,39770,39862,39967,40220,40290,40362,40443,40577,40652,40745,40812,40896,40967,41036,41621,41718,41832,41935,42023,42122,42193,42288,42353,42468,42585,42692,42834,42899,43003,43115,43266,43536,43883,45667,45729,45787,45866,45990,46170,46481,46815,46914,47571,47672,47748,47873,48030,48116,48204,48274,48361,48453,48550,48681,48823,48903,49054,49113,49277,49346,49407,49492,49568,49646,49740,49896,50017,50138,50230,50306,50388,50465", "endColumns": "78,86,82,83,93,83,82,151,83,115,103,132,65,123,64,170,198,172,89,109,101,101,192,121,101,262,110,141,230,254,99,136,209,98,60,65,84,101,102,74,99,91,104,252,69,71,80,133,74,92,66,83,70,68,119,96,113,102,87,98,70,94,64,114,116,106,141,64,103,111,150,71,95,113,61,57,78,123,179,310,333,98,79,100,75,124,156,85,87,69,86,91,96,130,141,79,71,58,163,68,60,84,75,77,93,155,120,120,91,75,81,76,157", "endOffsets": "18663,18750,18833,24663,26611,26695,26778,28936,30155,30319,30423,33235,35109,35310,35499,35939,36138,36311,36564,36800,37007,37109,37302,37424,37526,37789,37900,38042,38273,38528,38628,38864,39074,39173,39234,39300,39385,39487,39590,39665,39765,39857,39962,40215,40285,40357,40438,40572,40647,40740,40807,40891,40962,41031,41151,41713,41827,41930,42018,42117,42188,42283,42348,42463,42580,42687,42829,42894,42998,43110,43261,43333,43627,43992,45724,45782,45861,45985,46165,46476,46810,46909,46989,47667,47743,47868,48025,48111,48199,48269,48356,48448,48545,48676,48818,48898,48970,49108,49272,49341,49402,49487,49563,49641,49735,49891,50012,50133,50225,50301,50383,50460,50618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,444,559,669,751,857,987,1065,1141,1232,1325,1423,1518,1618,1711,1804,1899,1990,2081,2167,2277,2388,2491,2602,2710,2817,2976,17022", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "439,554,664,746,852,982,1060,1136,1227,1320,1418,1513,1613,1706,1799,1894,1985,2076,2162,2272,2383,2486,2597,2705,2812,2971,3070,17104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,196,258,340,408,479,537,619,692,759,819", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "131,191,253,335,403,474,532,614,687,754,814,884"}, "to": {"startLines": "206,216,218,219,220,224,232,235,238,242,246,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19233,20056,20198,20260,20342,20645,21198,21390,21641,21971,22349,22641", "endColumns": "80,59,61,81,67,70,57,81,72,66,59,69", "endOffsets": "19309,20111,20255,20337,20405,20711,21251,21467,21709,22033,22404,22706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,322,423,633,680,752,856,931,1208,1271,1366,1440,1495,1559,1632,1721,2042,2109,2176,2231,2286,2380,2530,2643,2702,2789,2877,2965,3036,3126,3428,3509,3588,3665,3727,3789,3853,3937,4016,4142,4247,4346,4464,4567,4641,4720,4811,5009,5218,5348,5492,5554,6355,6463,6528", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "154,317,418,628,675,747,851,926,1203,1266,1361,1435,1490,1554,1627,1716,2037,2104,2171,2226,2281,2375,2525,2638,2697,2784,2872,2960,3031,3121,3423,3504,3583,3660,3722,3784,3848,3932,4011,4137,4242,4341,4459,4562,4636,4715,4806,5004,5213,5343,5487,5549,6350,6458,6523,6581"}, "to": {"startLines": "267,268,269,271,275,276,277,278,279,280,281,297,300,302,306,307,312,318,319,327,337,341,342,343,344,345,351,354,359,361,362,363,364,367,369,370,374,378,379,381,383,395,420,421,422,423,424,442,449,450,451,452,454,455,456,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24216,24320,24483,24668,25789,25836,25908,26012,26087,26364,26427,28449,28734,28941,29256,29329,29704,30428,30495,31069,32096,32395,32489,32639,32752,32811,33434,33740,34231,34382,34472,34774,34855,35114,35315,35377,35709,36316,36400,36569,36805,38633,41156,41274,41377,41451,41530,43338,44079,44288,44418,44562,44693,45494,45602,47513", "endColumns": "103,162,100,209,46,71,103,74,276,62,94,73,54,63,72,88,320,66,66,54,54,93,149,112,58,86,87,87,70,89,301,80,78,76,61,61,63,83,78,125,104,98,117,102,73,78,90,197,208,129,143,61,800,107,64,57", "endOffsets": "24315,24478,24579,24873,25831,25903,26007,26082,26359,26422,26517,28518,28784,29000,29324,29413,30020,30490,30557,31119,32146,32484,32634,32747,32806,32893,33517,33823,34297,34467,34769,34850,34929,35186,35372,35434,35768,36395,36474,36690,36905,38727,41269,41372,41446,41525,41616,43531,44283,44413,44557,44619,45489,45597,45662,47566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "92,100,171,175,505,511,512", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9868,10546,16408,16714,50623,51245,51331", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "9933,10638,16481,16851,50787,51326,51406"}}]}]}