{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,207,282,373,468,548,627,749,840,943,1036,1161,1220,1349,1416,1601,1794,1943,2031,2127,2220,2321,2467,2571,2668,2901,3016,3142,3328,3520,3615,3726,3907,3998,4056,4119,4202,4294,4394,4465,4558,4659,4763,4945,5012,5078,5153,5266,5344,5430,5493,5567,5641,5710,5820,5911,6018,6109,6179,6258,6326,6411,6471,6571,6677,6780,6903,6968,7059,7155,7297,7366,7451,7546,7600,7657,7740,7840,8001,8242,8498,8596,8673,8757,8830,8935,9045,9131,9205,9275,9359,9429,9520,9637,9762,9833,9904,9961,10138,10210,10272,10351,10424,10498,10573,10695,10805,10905,10991,11068,11151,11216", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "119,202,277,368,463,543,622,744,835,938,1031,1156,1215,1344,1411,1596,1789,1938,2026,2122,2215,2316,2462,2566,2663,2896,3011,3137,3323,3515,3610,3721,3902,3993,4051,4114,4197,4289,4389,4460,4553,4654,4758,4940,5007,5073,5148,5261,5339,5425,5488,5562,5636,5705,5815,5906,6013,6104,6174,6253,6321,6406,6466,6566,6672,6775,6898,6963,7054,7150,7292,7361,7446,7541,7595,7652,7735,7835,7996,8237,8493,8591,8668,8752,8825,8930,9040,9126,9200,9270,9354,9424,9515,9632,9757,9828,9899,9956,10133,10205,10267,10346,10419,10493,10568,10690,10800,10900,10986,11063,11146,11211,11349"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14213,14282,14365,20063,21714,21809,21889,23704,24909,25045,25148,27598,29290,29408,29660,29955,30140,30333,30624,30807,31002,31095,31196,31342,31446,31543,31776,31891,32017,32203,32395,32573,32684,32865,32956,33014,33077,33160,33252,33352,33423,33516,33617,33721,33903,33970,34036,34111,34224,34302,34388,34451,34525,34599,34668,35255,35346,35453,35544,35614,35693,35761,35846,35906,36006,36112,36215,36338,36403,36494,36590,36732,37004,37297,38789,38843,38900,38983,39083,39244,39485,39741,39839,40435,40519,40592,40697,40807,40893,40967,41037,41121,41191,41282,41399,41524,41595,41745,41802,41979,42051,42113,42192,42265,42339,42414,42536,42646,42746,42832,42909,42992,43057", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "14277,14360,14435,20149,21804,21884,21963,23821,24995,25143,25236,27718,29344,29532,29722,30135,30328,30477,30707,30898,31090,31191,31337,31441,31538,31771,31886,32012,32198,32390,32485,32679,32860,32951,33009,33072,33155,33247,33347,33418,33511,33612,33716,33898,33965,34031,34106,34219,34297,34383,34446,34520,34594,34663,34773,35341,35448,35539,35609,35688,35756,35841,35901,36001,36107,36210,36333,36398,36489,36585,36727,36796,37084,37387,38838,38895,38978,39078,39239,39480,39736,39834,39911,40514,40587,40692,40802,40888,40962,41032,41116,41186,41277,41394,41519,41590,41661,41797,41974,42046,42108,42187,42260,42334,42409,42531,42641,42741,42827,42904,42987,43052,43190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "26911", "endColumns": "79", "endOffsets": "26986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "7803,43655", "endColumns": "60,74", "endOffsets": "7859,43725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,13628", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,13724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3302,3370,3436,4010,4563,4638,4744,4838,4893,4980,5064,5130,5214,5297,5354,5428,5477,5560,5658,5727,5798,5865,5931,5976,6051,6145,6219,6269,6315,6385,6443,6508,6675,6828,6944,7009,7087,7157,7244,7321,7401,7470,7564,7634,7742,7822,7908,7958,8068,8116,8173,8246,8308,8376,8442,8514,8597,8661,8710", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3297,3365,3431,4005,4558,4633,4739,4833,4888,4975,5059,5125,5209,5292,5349,5423,5472,5555,5653,5722,5793,5860,5926,5971,6046,6140,6214,6264,6310,6380,6438,6503,6670,6823,6939,7004,7082,7152,7239,7316,7396,7465,7559,7629,7737,7817,7903,7953,8063,8111,8168,8241,8303,8371,8437,8509,8592,8656,8705,8784"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13729,13798,13876,13938,14006,14083,14145,14440,14495,14570,14646,14872,15064,15174,15336,15702,16003,17035,17306,17408,17479,17644,17738,17826,18001,18323,18422,18500,18606,18676,18765,18851,18934,19015,19114,19258,19495,19596,20369,20437,20503,21968,22521,22596,22702,22796,22851,22938,23022,23088,23172,23255,23312,23890,23939,24022,24278,24347,24418,24485,25000,25379,25454,25548,25622,25672,25950,26020,26078,26143,26310,26463,26768,26833,27723,27793,27954,28031,28369,28438,28600,29182,29727,29807,37089,37139,37249,37392,37961,39916,39978,40046,40112,40184,40267,40331,41666", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "13793,13871,13933,14001,14078,14140,14208,14490,14565,14641,14720,14977,15169,15263,15424,15785,16090,17136,17403,17474,17573,17733,17821,17937,18083,18417,18495,18601,18671,18760,18846,18929,19010,19109,19177,19327,19591,19685,20432,20498,21072,22516,22591,22697,22791,22846,22933,23017,23083,23167,23250,23307,23381,23934,24017,24115,24342,24413,24480,24546,25040,25449,25543,25617,25667,25713,26015,26073,26138,26305,26458,26574,26828,26906,27788,27875,28026,28106,28433,28527,28665,29285,29802,29888,37134,37244,37292,37444,38029,39973,40041,40107,40179,40262,40326,40375,41740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4605,4711,4858,4981,5088,5224,5348,5467,5704,5848,5953,6100,6222,6362,6513,6577,6645", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4706,4853,4976,5083,5219,5343,5462,5570,5843,5948,6095,6217,6357,6508,6572,6640,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14790,15642,15790,15854,15939,16222,16767,16957,17232,17578,17942,18252", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "14867,15697,15849,15934,15998,16289,16821,17030,17301,17639,17996,18318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4456,6832,6929,7107,7934,8019,12688,12774,12857,12922,13065,13151,13479,43195,43273,43340", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4451,4528,6924,7025,7190,8014,8099,12769,12852,12917,12983,13146,13235,13547,43268,43335,43458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "43463,43560", "endColumns": "96,94", "endOffsets": "43555,43650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,326,428,643,692,760,849,920,1132,1192,1280,1357,1412,1476,1547,1634,1942,2010,2080,2133,2186,2263,2378,2474,2542,2619,2693,2777,2845,2941,3205,3279,3357,3416,3478,3539,3601,3668,3743,3838,3937,4020,4139,4242,4315,4394,4497,4700,4912,5029,5158,5212,5808,5905,5967", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "161,321,423,638,687,755,844,915,1127,1187,1275,1352,1407,1471,1542,1629,1937,2005,2075,2128,2181,2258,2373,2469,2537,2614,2688,2772,2840,2936,3200,3274,3352,3411,3473,3534,3596,3663,3738,3833,3932,4015,4134,4237,4310,4389,4492,4695,4907,5024,5153,5207,5803,5900,5962,6017"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19690,19801,19961,20154,21077,21126,21194,21283,21354,21566,21626,23386,23649,23826,24120,24191,24551,25241,25309,25833,26715,26991,27068,27183,27279,27347,27880,28111,28532,28670,28766,29030,29104,29349,29537,29599,29893,30482,30549,30712,30903,32490,34778,34897,35000,35073,35152,36801,37449,37661,37778,37907,38034,38630,38727,40380", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "19796,19956,20058,20364,21121,21189,21278,21349,21561,21621,21709,23458,23699,23885,24186,24273,24854,25304,25374,25881,26763,27063,27178,27274,27342,27419,27949,28190,28595,28761,29025,29099,29177,29403,29594,29655,29950,30544,30619,30802,30997,32568,34892,34995,35068,35147,35250,36999,37656,37773,37902,37956,38625,38722,38784,40430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,7195,7263,7405,7864,8104,8164,8251,8317,8382,8443,8507,8568,8622,8723,8784,8844,8898,8968,9079,9166,9243,9330,9412,9493,9636,9715,9797,9929,10021,10099,10153,10206,10272,10342,10420,10491,10571,10643,10721,10790,10859,10957,11039,11127,11220,11314,11388,11457,11552,11604,11687,11755,11840,11928,11990,12054,12117,12187,12287,12383,12480,12573,12631,12988,13322,13404,13552", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,7258,7328,7490,7929,8159,8246,8312,8377,8438,8502,8563,8617,8718,8779,8839,8893,8963,9074,9161,9238,9325,9407,9488,9631,9710,9792,9924,10016,10094,10148,10201,10267,10337,10415,10486,10566,10638,10716,10785,10854,10952,11034,11122,11215,11309,11383,11452,11547,11599,11682,11750,11835,11923,11985,12049,12112,12182,12282,12378,12475,12568,12626,12683,13060,13399,13474,13623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6729,7495,7594,7705", "endColumns": "102,98,110,97", "endOffsets": "6827,7589,7700,7798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14725,14982,15268,15429,15514,15581,16095,16160,16294,16359,16432,16504,16573,16634,16703,16826,16893,17141,18088,18153,19182,19332,19410,23463,23579,24859,25718,25765,25886,26579,26638,27424,27513,28195,28284", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "14785,15059,15331,15509,15576,15637,16155,16217,16354,16427,16499,16568,16629,16698,16762,16888,16952,17227,18148,18247,19253,19405,19490,23574,23644,24904,25760,25828,25945,26633,26710,27508,27593,28279,28364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4533,7030,7333", "endColumns": "71,76,71", "endOffsets": "4600,7102,7400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,13240", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,13317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5575", "endColumns": "128", "endOffsets": "5699"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,207,282,373,468,548,627,749,840,943,1036,1161,1220,1349,1416,1601,1794,1943,2031,2127,2220,2321,2467,2571,2668,2901,3016,3142,3328,3520,3615,3726,3907,3998,4056,4119,4202,4294,4394,4465,4558,4659,4763,4945,5012,5078,5153,5266,5344,5430,5493,5567,5641,5710,5820,5911,6018,6109,6179,6258,6326,6411,6471,6571,6677,6780,6903,6968,7059,7155,7297,7366,7451,7546,7600,7657,7740,7840,8001,8242,8498,8596,8673,8757,8830,8935,9045,9131,9205,9275,9359,9429,9520,9637,9762,9833,9904,9961,10138,10210,10272,10351,10424,10498,10573,10695,10805,10905,10991,11068,11151,11216", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "119,202,277,368,463,543,622,744,835,938,1031,1156,1215,1344,1411,1596,1789,1938,2026,2122,2215,2316,2462,2566,2663,2896,3011,3137,3323,3515,3610,3721,3902,3993,4051,4114,4197,4289,4389,4460,4553,4654,4758,4940,5007,5073,5148,5261,5339,5425,5488,5562,5636,5705,5815,5906,6013,6104,6174,6253,6321,6406,6466,6566,6672,6775,6898,6963,7054,7150,7292,7361,7446,7541,7595,7652,7735,7835,7996,8237,8493,8591,8668,8752,8825,8930,9040,9126,9200,9270,9354,9424,9515,9632,9757,9828,9899,9956,10133,10205,10267,10346,10419,10493,10568,10690,10800,10900,10986,11063,11146,11211,11349"}, "to": {"startLines": "164,165,166,236,248,249,250,267,280,282,283,314,332,334,337,341,342,343,346,348,350,351,352,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,409,413,423,424,425,426,427,428,429,430,431,440,441,442,443,444,445,446,447,448,449,450,451,452,453,455,456,457,458,459,460,461,462,463,464,465,466,467,468,469,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14213,14282,14365,20063,21714,21809,21889,23704,24909,25045,25148,27598,29290,29408,29660,29955,30140,30333,30624,30807,31002,31095,31196,31342,31446,31543,31776,31891,32017,32203,32395,32573,32684,32865,32956,33014,33077,33160,33252,33352,33423,33516,33617,33721,33903,33970,34036,34111,34224,34302,34388,34451,34525,34599,34668,35255,35346,35453,35544,35614,35693,35761,35846,35906,36006,36112,36215,36338,36403,36494,36590,36732,37004,37297,38789,38843,38900,38983,39083,39244,39485,39741,39839,40435,40519,40592,40697,40807,40893,40967,41037,41121,41191,41282,41399,41524,41595,41745,41802,41979,42051,42113,42192,42265,42339,42414,42536,42646,42746,42832,42909,42992,43057", "endColumns": "68,82,74,90,94,79,78,121,90,102,92,124,58,128,66,184,192,148,87,95,92,100,145,103,96,232,114,125,185,191,94,110,180,90,57,62,82,91,99,70,92,100,103,181,66,65,74,112,77,85,62,73,73,68,109,90,106,90,69,78,67,84,59,99,105,102,122,64,90,95,141,68,84,94,53,56,82,99,160,240,255,97,76,83,72,104,109,85,73,69,83,69,90,116,124,70,70,56,176,71,61,78,72,73,74,121,109,99,85,76,82,64,137", "endOffsets": "14277,14360,14435,20149,21804,21884,21963,23821,24995,25143,25236,27718,29344,29532,29722,30135,30328,30477,30707,30898,31090,31191,31337,31441,31538,31771,31886,32012,32198,32390,32485,32679,32860,32951,33009,33072,33155,33247,33347,33418,33511,33612,33716,33898,33965,34031,34106,34219,34297,34383,34446,34520,34594,34663,34773,35341,35448,35539,35609,35688,35756,35841,35901,36001,36107,36210,36333,36398,36489,36585,36727,36796,37084,37387,38838,38895,38978,39078,39239,39480,39736,39834,39911,40514,40587,40692,40802,40888,40962,41032,41116,41186,41277,41394,41519,41590,41661,41797,41974,42046,42108,42187,42260,42334,42409,42531,42641,42741,42827,42904,42987,43052,43190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "79", "endOffsets": "130"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "26911", "endColumns": "79", "endOffsets": "26986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "81,476", "startColumns": "4,4", "startOffsets": "7803,43655", "endColumns": "60,74", "endOffsets": "7859,43725"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,13628", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,13724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,264,332,409,471,539,594,669,745,824,934,1044,1138,1231,1319,1411,1517,1619,1690,1789,1883,1971,2087,2174,2273,2351,2457,2527,2616,2702,2785,2866,2965,3033,3107,3208,3302,3370,3436,4010,4563,4638,4744,4838,4893,4980,5064,5130,5214,5297,5354,5428,5477,5560,5658,5727,5798,5865,5931,5976,6051,6145,6219,6269,6315,6385,6443,6508,6675,6828,6944,7009,7087,7157,7244,7321,7401,7470,7564,7634,7742,7822,7908,7958,8068,8116,8173,8246,8308,8376,8442,8514,8597,8661,8710", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "119,197,259,327,404,466,534,589,664,740,819,929,1039,1133,1226,1314,1406,1512,1614,1685,1784,1878,1966,2082,2169,2268,2346,2452,2522,2611,2697,2780,2861,2960,3028,3102,3203,3297,3365,3431,4005,4558,4633,4739,4833,4888,4975,5059,5125,5209,5292,5349,5423,5472,5555,5653,5722,5793,5860,5926,5971,6046,6140,6214,6264,6310,6380,6438,6503,6670,6823,6939,7004,7082,7152,7239,7316,7396,7465,7559,7629,7737,7817,7903,7953,8063,8111,8168,8241,8303,8371,8437,8509,8592,8656,8705,8784"}, "to": {"startLines": "157,158,159,160,161,162,163,167,168,169,170,173,175,176,178,183,187,202,205,206,207,209,210,211,213,217,218,219,220,221,222,223,224,225,226,228,231,232,238,239,240,251,252,253,254,255,256,257,258,259,260,261,262,269,270,271,274,275,276,277,281,286,287,288,289,290,295,296,297,298,299,300,304,305,315,316,318,319,323,324,326,331,338,339,410,411,412,414,419,432,433,434,435,436,437,438,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13729,13798,13876,13938,14006,14083,14145,14440,14495,14570,14646,14872,15064,15174,15336,15702,16003,17035,17306,17408,17479,17644,17738,17826,18001,18323,18422,18500,18606,18676,18765,18851,18934,19015,19114,19258,19495,19596,20369,20437,20503,21968,22521,22596,22702,22796,22851,22938,23022,23088,23172,23255,23312,23890,23939,24022,24278,24347,24418,24485,25000,25379,25454,25548,25622,25672,25950,26020,26078,26143,26310,26463,26768,26833,27723,27793,27954,28031,28369,28438,28600,29182,29727,29807,37089,37139,37249,37392,37961,39916,39978,40046,40112,40184,40267,40331,41666", "endColumns": "68,77,61,67,76,61,67,54,74,75,78,109,109,93,92,87,91,105,101,70,98,93,87,115,86,98,77,105,69,88,85,82,80,98,67,73,100,93,67,65,573,552,74,105,93,54,86,83,65,83,82,56,73,48,82,97,68,70,66,65,44,74,93,73,49,45,69,57,64,166,152,115,64,77,69,86,76,79,68,93,69,107,79,85,49,109,47,56,72,61,67,65,71,82,63,48,78", "endOffsets": "13793,13871,13933,14001,14078,14140,14208,14490,14565,14641,14720,14977,15169,15263,15424,15785,16090,17136,17403,17474,17573,17733,17821,17937,18083,18417,18495,18601,18671,18760,18846,18929,19010,19109,19177,19327,19591,19685,20432,20498,21072,22516,22591,22697,22791,22846,22933,23017,23083,23167,23250,23307,23381,23934,24017,24115,24342,24413,24480,24546,25040,25449,25543,25617,25667,25713,26015,26073,26138,26305,26458,26574,26828,26906,27788,27875,28026,28106,28433,28527,28665,29285,29802,29888,37134,37244,37292,37444,38029,39973,40041,40107,40179,40262,40326,40375,41740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4605,4711,4858,4981,5088,5224,5348,5467,5704,5848,5953,6100,6222,6362,6513,6577,6645", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "4706,4853,4976,5083,5219,5343,5462,5570,5843,5948,6095,6217,6357,6508,6572,6640,6724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,137,197,261,346,410,482,541,619,693,759,818", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "132,192,256,341,405,477,536,614,688,754,813,884"}, "to": {"startLines": "172,182,184,185,186,190,198,201,204,208,212,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14790,15642,15790,15854,15939,16222,16767,16957,17232,17578,17942,18252", "endColumns": "81,59,63,84,63,71,58,77,73,65,58,70", "endOffsets": "14867,15697,15849,15934,15998,16289,16821,17030,17301,17639,17996,18318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,268,365,466,554,639,724,810,893,958,1024,1110,1199,1272,1350,1417", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "186,263,360,461,549,634,719,805,888,953,1019,1105,1194,1267,1345,1412,1535"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,471,472,473", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4370,4456,6832,6929,7107,7934,8019,12688,12774,12857,12922,13065,13151,13479,43195,43273,43340", "endColumns": "85,76,96,100,87,84,84,85,82,64,65,85,88,72,77,66,122", "endOffsets": "4451,4528,6924,7025,7190,8014,8099,12769,12852,12917,12983,13146,13235,13547,43268,43335,43458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,94", "endOffsets": "147,242"}, "to": {"startLines": "474,475", "startColumns": "4,4", "startOffsets": "43463,43560", "endColumns": "96,94", "endOffsets": "43555,43650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,326,428,643,692,760,849,920,1132,1192,1280,1357,1412,1476,1547,1634,1942,2010,2080,2133,2186,2263,2378,2474,2542,2619,2693,2777,2845,2941,3205,3279,3357,3416,3478,3539,3601,3668,3743,3838,3937,4020,4139,4242,4315,4394,4497,4700,4912,5029,5158,5212,5808,5905,5967", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "161,321,423,638,687,755,844,915,1127,1187,1275,1352,1407,1471,1542,1629,1937,2005,2075,2128,2181,2258,2373,2469,2537,2614,2688,2772,2840,2936,3200,3274,3352,3411,3473,3534,3596,3663,3738,3833,3932,4015,4134,4237,4310,4389,4492,4695,4907,5024,5153,5207,5803,5900,5962,6017"}, "to": {"startLines": "233,234,235,237,241,242,243,244,245,246,247,263,266,268,272,273,278,284,285,293,303,307,308,309,310,311,317,320,325,327,328,329,330,333,335,336,340,344,345,347,349,361,386,387,388,389,390,408,415,416,417,418,420,421,422,439", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19690,19801,19961,20154,21077,21126,21194,21283,21354,21566,21626,23386,23649,23826,24120,24191,24551,25241,25309,25833,26715,26991,27068,27183,27279,27347,27880,28111,28532,28670,28766,29030,29104,29349,29537,29599,29893,30482,30549,30712,30903,32490,34778,34897,35000,35073,35152,36801,37449,37661,37778,37907,38034,38630,38727,40380", "endColumns": "110,159,101,214,48,67,88,70,211,59,87,76,54,63,70,86,307,67,69,52,52,76,114,95,67,76,73,83,67,95,263,73,77,58,61,60,61,66,74,94,98,82,118,102,72,78,102,202,211,116,128,53,595,96,61,54", "endOffsets": "19796,19956,20058,20364,21121,21189,21278,21349,21561,21621,21709,23458,23699,23885,24186,24273,24854,25304,25374,25881,26763,27063,27178,27274,27342,27419,27949,28190,28595,28761,29025,29099,29177,29403,29594,29655,29950,30544,30619,30802,30997,32568,34892,34995,35068,35147,35250,36999,37656,37773,37902,37956,38625,38722,38784,40430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1044,1114,1204,1274,1334,1421,1487,1552,1613,1677,1738,1792,1893,1954,2014,2068,2138,2249,2336,2413,2500,2582,2663,2806,2885,2967,3099,3191,3269,3323,3376,3442,3512,3590,3661,3741,3813,3891,3960,4029,4127,4209,4297,4390,4484,4558,4627,4722,4774,4857,4925,5010,5098,5160,5224,5287,5357,5457,5553,5650,5743,5801,5858,5935,6017,6092", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "280,353,425,508,593,679,778,891,971,1039,1109,1199,1269,1329,1416,1482,1547,1608,1672,1733,1787,1888,1949,2009,2063,2133,2244,2331,2408,2495,2577,2658,2801,2880,2962,3094,3186,3264,3318,3371,3437,3507,3585,3656,3736,3808,3886,3955,4024,4122,4204,4292,4385,4479,4553,4622,4717,4769,4852,4920,5005,5093,5155,5219,5282,5352,5452,5548,5645,5738,5796,5853,5930,6012,6087,6163"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,7195,7263,7405,7864,8104,8164,8251,8317,8382,8443,8507,8568,8622,8723,8784,8844,8898,8968,9079,9166,9243,9330,9412,9493,9636,9715,9797,9929,10021,10099,10153,10206,10272,10342,10420,10491,10571,10643,10721,10790,10859,10957,11039,11127,11220,11314,11388,11457,11552,11604,11687,11755,11840,11928,11990,12054,12117,12187,12287,12383,12480,12573,12631,12988,13322,13404,13552", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,72,71,82,84,85,98,112,79,67,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,76,86,81,80,142,78,81,131,91,77,53,52,65,69,77,70,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76,81,74,75", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,7258,7328,7490,7929,8159,8246,8312,8377,8438,8502,8563,8617,8718,8779,8839,8893,8963,9074,9161,9238,9325,9407,9488,9631,9710,9792,9924,10016,10094,10148,10201,10267,10337,10415,10486,10566,10638,10716,10785,10854,10952,11034,11122,11215,11309,11383,11452,11547,11599,11682,11750,11835,11923,11985,12049,12112,12182,12282,12378,12475,12568,12626,12683,13060,13399,13474,13623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6729,7495,7594,7705", "endColumns": "102,98,110,97", "endOffsets": "6827,7589,7700,7798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,202,270,355,422,483,548,610,675,748,820,889,950,1019,1083,1150,1214,1305,1370,1469,1545,1623,1708,1824,1894,1944,1991,2059,2123,2182,2259,2348,2433,2522", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "115,197,265,350,417,478,543,605,670,743,815,884,945,1014,1078,1145,1209,1300,1365,1464,1540,1618,1703,1819,1889,1939,1986,2054,2118,2177,2254,2343,2428,2517,2602"}, "to": {"startLines": "171,174,177,179,180,181,188,189,191,192,193,194,195,196,197,199,200,203,214,215,227,229,230,264,265,279,291,292,294,301,302,312,313,321,322", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14725,14982,15268,15429,15514,15581,16095,16160,16294,16359,16432,16504,16573,16634,16703,16826,16893,17141,18088,18153,19182,19332,19410,23463,23579,24859,25718,25765,25886,26579,26638,27424,27513,28195,28284", "endColumns": "64,81,67,84,66,60,64,61,64,72,71,68,60,68,63,66,63,90,64,98,75,77,84,115,69,49,46,67,63,58,76,88,84,88,84", "endOffsets": "14785,15059,15331,15509,15576,15637,16155,16217,16354,16427,16499,16568,16629,16698,16762,16888,16952,17227,18148,18247,19253,19405,19490,23574,23644,24904,25760,25828,25945,26633,26710,27508,27593,28279,28364"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,204", "endColumns": "71,76,71", "endOffsets": "122,199,271"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4533,7030,7333", "endColumns": "71,76,71", "endOffsets": "4600,7102,7400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,13240", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,13317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5575", "endColumns": "128", "endOffsets": "5699"}}]}]}