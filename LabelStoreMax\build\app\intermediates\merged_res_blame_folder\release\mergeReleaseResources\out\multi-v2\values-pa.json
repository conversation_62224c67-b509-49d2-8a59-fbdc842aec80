{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "184", "startColumns": "4", "startOffsets": "16929", "endColumns": "84", "endOffsets": "17009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "189,190", "startColumns": "4,4", "startOffsets": "17447,17534", "endColumns": "86,86", "endOffsets": "17529,17616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "74,75,76,77,78,79,80,81,83,84,85,86,87,88,89,90,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7263,7370,7543,7673,7782,7929,8058,8171,8425,8587,8696,8869,9001,9154,9315,9380,9446", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "7365,7538,7668,7777,7924,8053,8166,8269,8582,8691,8864,8996,9149,9310,9375,9441,9523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "82", "startColumns": "4", "startOffsets": "8274", "endColumns": "150", "endOffsets": "8420"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2975,3052,3131,3212,3311,4133,4241,4353,10055,10111,10343,10814,11059,11118,11203,11266,11328,11386,11450,11511,11565,11679,11737,11797,11851,11921,12048,12129,12219,12318,12415,12494,12629,12705,12782,12911,12995,13077,13132,13187,13253,13322,13399,13470,13549,13617,13693,13763,13828,13930,14025,14098,14192,14285,14359,14428,14522,14578,14661,14728,14812,14900,14962,15026,15089,15156,15253,15359,15450,15552,15611,16058,16522,16607,16755", "endLines": "5,33,34,35,36,37,45,46,47,98,99,102,107,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,174,179,180,182", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "308,3047,3126,3207,3306,3395,4236,4348,4431,10106,10170,10430,10878,11113,11198,11261,11323,11381,11445,11506,11560,11674,11732,11792,11846,11916,12043,12124,12214,12313,12410,12489,12624,12700,12777,12906,12990,13072,13127,13182,13248,13317,13394,13465,13544,13612,13688,13758,13823,13925,14020,14093,14187,14280,14354,14423,14517,14573,14656,14723,14807,14895,14957,15021,15084,15151,15248,15354,15445,15547,15606,15665,16130,16602,16678,16823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,262,341,488,657,737", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "172,257,336,483,652,732,810"}, "to": {"startLines": "92,100,171,175,185,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9528,10175,15842,16135,17014,17621,17701", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "9595,10255,15916,16277,17178,17696,17774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,976,1045,1124,1205,1277,1357,1423", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,971,1040,1119,1200,1272,1352,1418,1536"}, "to": {"startLines": "48,49,94,95,97,108,109,169,170,172,173,176,177,181,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4436,4529,9705,9797,9967,10883,10961,15670,15758,15921,15989,16282,16361,16683,17183,17263,17329", "endColumns": "92,82,91,98,87,77,97,87,83,67,68,78,80,71,79,65,117", "endOffsets": "4524,4607,9792,9891,10050,10956,11054,15753,15837,15984,16053,16356,16437,16750,17258,17324,17442"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,334,482,595,681,770,852,942,1031,1145,1259,1354,1449,1561,1692,1772,1858,2034,2129,2237,2352,2462", "endColumns": "148,129,147,112,85,88,81,89,88,113,113,94,94,111,130,79,85,175,94,107,114,109,164", "endOffsets": "199,329,477,590,676,765,847,937,1026,1140,1254,1349,1444,1556,1687,1767,1853,2029,2124,2232,2347,2457,2622"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4691,4840,4970,5118,5231,5317,5406,5488,5578,5667,5781,5895,5990,6085,6197,6328,6408,6494,6670,6765,6873,6988,7098", "endColumns": "148,129,147,112,85,88,81,89,88,113,113,94,94,111,130,79,85,175,94,107,114,109,164", "endOffsets": "4835,4965,5113,5226,5312,5401,5483,5573,5662,5776,5890,5985,6080,6192,6323,6403,6489,6665,6760,6868,6983,7093,7258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "38,39,40,41,42,43,44,183", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3400,3498,3600,3703,3804,3906,4004,16828", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3493,3595,3698,3799,3901,3999,4128,16924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "93,103,104,105", "startColumns": "4,4,4,4", "startOffsets": "9600,10435,10536,10650", "endColumns": "104,100,113,102", "endOffsets": "9700,10531,10645,10748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,71", "endOffsets": "258,330"}, "to": {"startLines": "106,193", "startColumns": "4,4", "startOffsets": "10753,17779", "endColumns": "60,75", "endOffsets": "10809,17850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,16442", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,16517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,205", "endColumns": "78,70,82", "endOffsets": "129,200,283"}, "to": {"startLines": "50,96,101", "startColumns": "4,4,4", "startOffsets": "4612,9896,10260", "endColumns": "78,70,82", "endOffsets": "4686,9962,10338"}}]}]}