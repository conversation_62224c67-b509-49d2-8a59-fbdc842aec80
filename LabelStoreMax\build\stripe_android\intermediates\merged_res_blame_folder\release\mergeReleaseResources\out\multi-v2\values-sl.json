{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,212", "endColumns": "76,79,77", "endOffsets": "127,207,285"}, "to": {"startLines": "52,74,78", "startColumns": "4,4,4", "startOffsets": "4827,7428,7723", "endColumns": "76,79,77", "endOffsets": "4899,7503,7796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4904,5010,5175,5304,5411,5558,5688,5802,6046,6204,6311,6478,6608,6760,6912,6982,7044", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "5005,5170,5299,5406,5553,5683,5797,5901,6199,6306,6473,6603,6755,6907,6977,7039,7123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5906", "endColumns": "139", "endOffsets": "6041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,14157", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,14253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "163,164", "startColumns": "4,4", "startOffsets": "14611,14701", "endColumns": "89,90", "endOffsets": "14696,14787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,7593,7657,7801,8274,8514,8577,8689,8749,8814,8868,8938,8998,9054,9166,9223,9285,9341,9414,9548,9633,9710,9799,9880,9965,10108,10192,10275,10409,10498,10575,10631,10686,10752,10825,10902,10973,11052,11126,11202,11277,11350,11455,11543,11616,11706,11797,11869,11943,12034,12086,12168,12235,12319,12406,12468,12532,12595,12664,12767,12875,12973,13077,13137,13515,13844,13931,14079", "endLines": "7,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,7652,7718,7890,8345,8572,8684,8744,8809,8863,8933,8993,9049,9161,9218,9280,9336,9409,9543,9628,9705,9794,9875,9960,10103,10187,10270,10404,10493,10570,10626,10681,10747,10820,10897,10968,11047,11121,11197,11272,11345,11450,11538,11611,11701,11792,11864,11938,12029,12081,12163,12230,12314,12401,12463,12527,12590,12659,12762,12870,12968,13072,13132,13191,13587,13926,14002,14152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "71,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7128,7895,7999,8111", "endColumns": "105,103,111,101", "endOffsets": "7229,7994,8106,8208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "83,165", "startColumns": "4,4", "startOffsets": "8213,14792", "endColumns": "60,74", "endOffsets": "8269,14862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "83", "endOffsets": "134"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "14258", "endColumns": "83", "endOffsets": "14337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,13760", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,13839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1361,1431", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1356,1426,1547"}, "to": {"startLines": "50,51,72,73,75,85,86,146,147,148,149,151,152,156,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4651,4746,7234,7330,7508,8350,8427,13196,13288,13370,13443,13592,13674,14007,14342,14420,14490", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "4741,4822,7325,7423,7588,8422,8509,13283,13365,13438,13510,13669,13755,14074,14415,14485,14606"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,212", "endColumns": "76,79,77", "endOffsets": "127,207,285"}, "to": {"startLines": "52,74,78", "startColumns": "4,4,4", "startOffsets": "4827,7428,7723", "endColumns": "76,79,77", "endOffsets": "4899,7503,7796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4904,5010,5175,5304,5411,5558,5688,5802,6046,6204,6311,6478,6608,6760,6912,6982,7044", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "5005,5170,5299,5406,5553,5683,5797,5901,6199,6306,6473,6603,6755,6907,6977,7039,7123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "61", "startColumns": "4", "startOffsets": "5906", "endColumns": "139", "endOffsets": "6041"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "40,41,42,43,44,45,46,158", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3612,3709,3811,3909,4013,4116,4218,14157", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3704,3806,3904,4008,4111,4213,4330,14253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "163,164", "startColumns": "4,4", "startOffsets": "14611,14701", "endColumns": "89,90", "endOffsets": "14696,14787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3157,3246,3335,3423,3521,4335,4441,4567,7593,7657,7801,8274,8514,8577,8689,8749,8814,8868,8938,8998,9054,9166,9223,9285,9341,9414,9548,9633,9710,9799,9880,9965,10108,10192,10275,10409,10498,10575,10631,10686,10752,10825,10902,10973,11052,11126,11202,11277,11350,11455,11543,11616,11706,11797,11869,11943,12034,12086,12168,12235,12319,12406,12468,12532,12595,12664,12767,12875,12973,13077,13137,13515,13844,13931,14079", "endLines": "7,35,36,37,38,39,47,48,49,76,77,79,84,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,150,154,155,157", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "412,3241,3330,3418,3516,3607,4436,4562,4646,7652,7718,7890,8345,8572,8684,8744,8809,8863,8933,8993,9049,9161,9218,9280,9336,9409,9543,9628,9705,9794,9875,9960,10103,10187,10270,10404,10493,10570,10626,10681,10747,10820,10897,10968,11047,11121,11197,11272,11345,11450,11538,11611,11701,11792,11864,11938,12029,12081,12163,12230,12314,12401,12463,12527,12590,12659,12762,12870,12968,13072,13132,13191,13587,13926,14002,14152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "71,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7128,7895,7999,8111", "endColumns": "105,103,111,101", "endOffsets": "7229,7994,8106,8208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,70", "endOffsets": "258,329"}, "to": {"startLines": "83,165", "startColumns": "4,4", "startOffsets": "8213,14792", "endColumns": "60,74", "endOffsets": "8269,14862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "83", "endOffsets": "134"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "14258", "endColumns": "83", "endOffsets": "14337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "417,529,631,739,826,929,1048,1129,1207,1299,1393,1488,1582,1677,1771,1867,1967,2059,2151,2235,2343,2451,2551,2664,2772,2877,3057,13760", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "524,626,734,821,924,1043,1124,1202,1294,1388,1483,1577,1672,1766,1862,1962,2054,2146,2230,2338,2446,2546,2659,2767,2872,3052,3152,13839"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1361,1431", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1356,1426,1547"}, "to": {"startLines": "50,51,72,73,75,85,86,146,147,148,149,151,152,156,160,161,162", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4651,4746,7234,7330,7508,8350,8427,13196,13288,13370,13443,13592,13674,14007,14342,14420,14490", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "4741,4822,7325,7423,7588,8422,8509,13283,13365,13438,13510,13669,13755,14074,14415,14485,14606"}}]}]}