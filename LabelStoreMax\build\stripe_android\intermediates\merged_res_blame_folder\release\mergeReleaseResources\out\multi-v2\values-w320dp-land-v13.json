{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-w320dp-land-v13/values-w320dp-land-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-w320dp-land-v13\\values-w320dp-land-v13.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,110", "endColumns": "54,61", "endOffsets": "105,167"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-w320dp-land-v13/values-w320dp-land-v13.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-w320dp-land-v13\\values-w320dp-land-v13.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,110", "endColumns": "54,61", "endOffsets": "105,167"}}]}]}