{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,188,252,327,391,452,514,575,635,709,783,850,910,978,1042,1109,1173,1239,1298,1375,1448,1514,1586,1661,1738,1785,1834,1900,1962,2020,2086,2165,2232,2305", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "110,183,247,322,386,447,509,570,630,704,778,845,905,973,1037,1104,1168,1234,1293,1370,1443,1509,1581,1656,1733,1780,1829,1895,1957,2015,2081,2160,2227,2300,2372"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14207,14426,14660,14808,14883,14947,15402,15464,15587,15647,15721,15795,15862,15922,15990,16111,16178,16391,17180,17239,18123,18268,18334,21743,21818,22927,23669,23718,23837,24399,24457,25172,25251,25824,25897", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "14262,14494,14719,14878,14942,15003,15459,15520,15642,15716,15790,15857,15917,15985,16049,16173,16237,16452,17234,17311,18191,18329,18401,21813,21890,22969,23713,23779,23894,24452,24518,25246,25313,25892,25964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,184,243,317,375,437,494,561,627,690,750", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "123,179,238,312,370,432,489,556,622,685,745,808"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14267,15008,15138,15197,15271,15525,16054,16242,16457,16745,17045,17316", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "14335,15059,15192,15266,15324,15582,16106,16304,16518,16803,17100,17374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3252,3344,3444,3538,3634,3727,3820,12707", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3339,3439,3533,3629,3722,3815,3916,12803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5345", "endColumns": "121", "endOffsets": "5462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6384,7091,7186,7287", "endColumns": "92,94,100,94", "endOffsets": "6472,7181,7282,7377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "73", "endOffsets": "124"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "24707", "endColumns": "73", "endOffsets": "24776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,64", "endOffsets": "258,323"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "7382,38915", "endColumns": "60,68", "endOffsets": "7438,38979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,189,251,318,389,462,534,627,694,768,846,933,989,1078,1137,1277,1422,1530,1615,1697,1784,1871,1984,2071,2158,2295,2389,2493,2658,2829,2925,3019,3139,3218,3276,3337,3415,3495,3576,3643,3724,3803,3889,4028,4095,4159,4222,4318,4391,4468,4532,4604,4675,4743,4830,4911,5007,5089,5156,5234,5299,5371,5427,5514,5602,5690,5798,5857,5937,6027,6134,6202,6274,6357,6410,6464,6528,6609,6753,6956,7174,7260,7326,7396,7459,7545,7636,7715,7787,7857,7928,7998,8077,8160,8262,8333,8396,8447,8579,8642,8698,8767,8829,8901,8968,9081,9171,9252,9329,9397,9465,9526", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "112,184,246,313,384,457,529,622,689,763,841,928,984,1073,1132,1272,1417,1525,1610,1692,1779,1866,1979,2066,2153,2290,2384,2488,2653,2824,2920,3014,3134,3213,3271,3332,3410,3490,3571,3638,3719,3798,3884,4023,4090,4154,4217,4313,4386,4463,4527,4599,4670,4738,4825,4906,5002,5084,5151,5229,5294,5366,5422,5509,5597,5685,5793,5852,5932,6022,6129,6197,6269,6352,6405,6459,6523,6604,6748,6951,7169,7255,7321,7391,7454,7540,7631,7710,7782,7852,7923,7993,8072,8155,8257,8328,8391,8442,8574,8637,8693,8762,8824,8896,8963,9076,9166,9247,9324,9392,9460,9521,9636"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13751,13813,13885,18899,20227,20298,20371,21949,22974,23086,23160,25318,26726,26837,27045,27317,27457,27602,27845,28020,28183,28270,28357,28470,28557,28644,28781,28875,28979,29144,29315,29490,29584,29704,29783,29841,29902,29980,30060,30141,30208,30289,30368,30454,30593,30660,30724,30787,30883,30956,31033,31097,31169,31240,31308,31827,31908,32004,32086,32153,32231,32296,32368,32424,32511,32599,32687,32795,32854,32934,33024,33131,33324,33584,34655,34708,34762,34826,34907,35051,35254,35472,35558,36113,36183,36246,36332,36423,36502,36574,36644,36715,36785,36864,36947,37049,37120,37245,37296,37428,37491,37547,37616,37678,37750,37817,37930,38020,38101,38178,38246,38314,38375", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "13808,13880,13942,18961,20293,20366,20438,22037,23036,23155,23233,25400,26777,26921,27099,27452,27597,27705,27925,28097,28265,28352,28465,28552,28639,28776,28870,28974,29139,29310,29406,29579,29699,29778,29836,29897,29975,30055,30136,30203,30284,30363,30449,30588,30655,30719,30782,30878,30951,31028,31092,31164,31235,31303,31390,31903,31999,32081,32148,32226,32291,32363,32419,32506,32594,32682,32790,32849,32929,33019,33126,33194,33391,33662,34703,34757,34821,34902,35046,35249,35467,35553,35619,36178,36241,36327,36418,36497,36569,36639,36710,36780,36859,36942,37044,37115,37178,37291,37423,37486,37542,37611,37673,37745,37812,37925,38015,38096,38173,38241,38309,38370,38485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4529,4663,4783,4889,5021,5141,5246,5467,5601,5702,5835,5954,6074,6194,6254,6313", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "4524,4658,4778,4884,5016,5136,5241,5340,5596,5697,5830,5949,6069,6189,6249,6308,6379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,223,290,357,420,490", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "129,218,285,352,415,485,546"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12808,12887,12976,13043,13110,13173,13243", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "12882,12971,13038,13105,13168,13238,13299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,81", "endOffsets": "135,217"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "38748,38833", "endColumns": "84,81", "endOffsets": "38828,38910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,201", "endColumns": "74,70,73", "endOffsets": "125,196,270"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4350,6663,6933", "endColumns": "74,70,73", "endOffsets": "4420,6729,7002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,12337", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,12411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4186,4272,6477,6566,6734,7507,7585,11826,11911,11986,12050,12187,12261,12567,38490,38566,38631", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "4267,4345,6561,6658,6812,7580,7658,11906,11981,12045,12109,12256,12332,12631,38561,38626,38743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,189,256,318,388,442,502,555,626,692,762,848,934,1009,1093,1167,1240,1322,1403,1466,1544,1622,1697,1781,1856,1934,2004,2089,2158,2237,2310,2380,2455,2536,2600,2672,2767,2852,2914,2975,3433,3864,3934,4025,4114,4169,4253,4331,4392,4467,4547,4602,4676,4724,4795,4881,4947,5013,5080,5146,5191,5259,5343,5414,5457,5500,5569,5627,5686,5797,5908,6000,6063,6133,6197,6275,6343,6410,6473,6548,6612,6692,6768,6847,6896,6990,7035,7090,7152,7211,7279,7338,7401,7479,7539,7586", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "113,184,251,313,383,437,497,550,621,687,757,843,929,1004,1088,1162,1235,1317,1398,1461,1539,1617,1692,1776,1851,1929,1999,2084,2153,2232,2305,2375,2450,2531,2595,2667,2762,2847,2909,2970,3428,3859,3929,4020,4109,4164,4248,4326,4387,4462,4542,4597,4671,4719,4790,4876,4942,5008,5075,5141,5186,5254,5338,5409,5452,5495,5564,5622,5681,5792,5903,5995,6058,6128,6192,6270,6338,6405,6468,6543,6607,6687,6763,6842,6891,6985,7030,7085,7147,7206,7274,7333,7396,7474,7534,7581,7643"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13304,13367,13438,13505,13567,13637,13691,13947,14000,14071,14137,14340,14499,14585,14724,15064,15329,16309,16523,16604,16667,16808,16886,16961,17105,17379,17457,17527,17612,17681,17760,17833,17903,17978,18059,18196,18406,18501,19101,19163,19224,20443,20874,20944,21035,21124,21179,21263,21341,21402,21477,21557,21612,22106,22154,22225,22469,22535,22601,22668,23041,23360,23428,23512,23583,23626,23899,23968,24026,24085,24196,24307,24574,24637,25405,25469,25615,25683,25969,26032,26170,26646,27104,27180,33396,33445,33539,33667,34106,35624,35683,35751,35810,35873,35951,36011,37183", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "13362,13433,13500,13562,13632,13686,13746,13995,14066,14132,14202,14421,14580,14655,14803,15133,15397,16386,16599,16662,16740,16881,16956,17040,17175,17452,17522,17607,17676,17755,17828,17898,17973,18054,18118,18263,18496,18581,19158,19219,19677,20869,20939,21030,21119,21174,21258,21336,21397,21472,21552,21607,21681,22149,22220,22306,22530,22596,22663,22729,23081,23423,23507,23578,23621,23664,23963,24021,24080,24191,24302,24394,24632,24702,25464,25542,25678,25745,26027,26102,26229,26721,27175,27254,33440,33534,33579,33717,34163,35678,35746,35805,35868,35946,36006,36053,37240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,286,368,503,546,606,686,750,911,968,1048,1105,1159,1223,1294,1381,1574,1635,1696,1749,1800,1871,1973,2060,2114,2191,2259,2333,2396,2472,2664,2734,2808,2863,2921,2982,3040,3104,3175,3265,3346,3425,3527,3616,3695,3773,3857,3982,4116,4215,4312,4366,4713,4796,4853", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "137,281,363,498,541,601,681,745,906,963,1043,1100,1154,1218,1289,1376,1569,1630,1691,1744,1795,1866,1968,2055,2109,2186,2254,2328,2391,2467,2659,2729,2803,2858,2916,2977,3035,3099,3170,3260,3341,3420,3522,3611,3690,3768,3852,3977,4111,4210,4307,4361,4708,4791,4848,4903"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18586,18673,18817,18966,19682,19725,19785,19865,19929,20090,20147,21686,21895,22042,22311,22382,22734,23238,23299,23784,24523,24781,24852,24954,25041,25095,25547,25750,26107,26234,26310,26502,26572,26782,26926,26984,27259,27710,27774,27930,28102,29411,31395,31497,31586,31665,31743,33199,33722,33856,33955,34052,34168,34515,34598,36058", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "18668,18812,18894,19096,19720,19780,19860,19924,20085,20142,20222,21738,21944,22101,22377,22464,22922,23294,23355,23832,24569,24847,24949,25036,25090,25167,25610,25819,26165,26305,26497,26567,26641,26832,26979,27040,27312,27769,27840,28015,28178,29485,31492,31581,31660,31738,31822,33319,33851,33950,34047,34101,34510,34593,34650,36108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3170,3921,4006,4110,6817,6870,7007,7443,7663,7721,7802,7863,7927,7982,8041,8098,8152,8245,8301,8358,8412,8478,8578,8654,8725,8804,8877,8958,9080,9142,9204,9305,9384,9459,9512,9563,9629,9699,9769,9840,9910,9974,10045,10113,10176,10267,10346,10409,10489,10571,10643,10714,10786,10834,10906,10970,11045,11122,11184,11248,11311,11378,11464,11550,11631,11714,11771,12114,12416,12494,12636", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "298,2951,3015,3084,3165,3247,4001,4105,4181,6865,6928,7086,7502,7716,7797,7858,7922,7977,8036,8093,8147,8240,8296,8353,8407,8473,8573,8649,8720,8799,8872,8953,9075,9137,9199,9300,9379,9454,9507,9558,9624,9694,9764,9835,9905,9969,10040,10108,10171,10262,10341,10404,10484,10566,10638,10709,10781,10829,10901,10965,11040,11117,11179,11243,11306,11373,11459,11545,11626,11709,11766,11821,12182,12489,12562,12702"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,188,252,327,391,452,514,575,635,709,783,850,910,978,1042,1109,1173,1239,1298,1375,1448,1514,1586,1661,1738,1785,1834,1900,1962,2020,2086,2165,2232,2305", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "110,183,247,322,386,447,509,570,630,704,778,845,905,973,1037,1104,1168,1234,1293,1370,1443,1509,1581,1656,1733,1780,1829,1895,1957,2015,2081,2160,2227,2300,2372"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14207,14426,14660,14808,14883,14947,15402,15464,15587,15647,15721,15795,15862,15922,15990,16111,16178,16391,17180,17239,18123,18268,18334,21743,21818,22927,23669,23718,23837,24399,24457,25172,25251,25824,25897", "endColumns": "59,72,63,74,63,60,61,60,59,73,73,66,59,67,63,66,63,65,58,76,72,65,71,74,76,46,48,65,61,57,65,78,66,72,71", "endOffsets": "14262,14494,14719,14878,14942,15003,15459,15520,15642,15716,15790,15857,15917,15985,16049,16173,16237,16452,17234,17311,18191,18329,18401,21813,21890,22969,23713,23779,23894,24452,24518,25246,25313,25892,25964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,184,243,317,375,437,494,561,627,690,750", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "123,179,238,312,370,432,489,556,622,685,745,808"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14267,15008,15138,15197,15271,15525,16054,16242,16457,16745,17045,17316", "endColumns": "72,55,58,73,57,61,56,66,65,62,59,62", "endOffsets": "14335,15059,15192,15266,15324,15582,16106,16304,16518,16803,17100,17374"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3252,3344,3444,3538,3634,3727,3820,12707", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3339,3439,3533,3629,3722,3815,3916,12803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5345", "endColumns": "121", "endOffsets": "5462"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6384,7091,7186,7287", "endColumns": "92,94,100,94", "endOffsets": "6472,7181,7282,7377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "73", "endOffsets": "124"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "24707", "endColumns": "73", "endOffsets": "24776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,64", "endOffsets": "258,323"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "7382,38915", "endColumns": "60,68", "endOffsets": "7438,38979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,189,251,318,389,462,534,627,694,768,846,933,989,1078,1137,1277,1422,1530,1615,1697,1784,1871,1984,2071,2158,2295,2389,2493,2658,2829,2925,3019,3139,3218,3276,3337,3415,3495,3576,3643,3724,3803,3889,4028,4095,4159,4222,4318,4391,4468,4532,4604,4675,4743,4830,4911,5007,5089,5156,5234,5299,5371,5427,5514,5602,5690,5798,5857,5937,6027,6134,6202,6274,6357,6410,6464,6528,6609,6753,6956,7174,7260,7326,7396,7459,7545,7636,7715,7787,7857,7928,7998,8077,8160,8262,8333,8396,8447,8579,8642,8698,8767,8829,8901,8968,9081,9171,9252,9329,9397,9465,9526", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "112,184,246,313,384,457,529,622,689,763,841,928,984,1073,1132,1272,1417,1525,1610,1692,1779,1866,1979,2066,2153,2290,2384,2488,2653,2824,2920,3014,3134,3213,3271,3332,3410,3490,3571,3638,3719,3798,3884,4023,4090,4154,4217,4313,4386,4463,4527,4599,4670,4738,4825,4906,5002,5084,5151,5229,5294,5366,5422,5509,5597,5685,5793,5852,5932,6022,6129,6197,6269,6352,6405,6459,6523,6604,6748,6951,7169,7255,7321,7391,7454,7540,7631,7710,7782,7852,7923,7993,8072,8155,8257,8328,8391,8442,8574,8637,8693,8762,8824,8896,8963,9076,9166,9247,9324,9392,9460,9521,9636"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13751,13813,13885,18899,20227,20298,20371,21949,22974,23086,23160,25318,26726,26837,27045,27317,27457,27602,27845,28020,28183,28270,28357,28470,28557,28644,28781,28875,28979,29144,29315,29490,29584,29704,29783,29841,29902,29980,30060,30141,30208,30289,30368,30454,30593,30660,30724,30787,30883,30956,31033,31097,31169,31240,31308,31827,31908,32004,32086,32153,32231,32296,32368,32424,32511,32599,32687,32795,32854,32934,33024,33131,33324,33584,34655,34708,34762,34826,34907,35051,35254,35472,35558,36113,36183,36246,36332,36423,36502,36574,36644,36715,36785,36864,36947,37049,37120,37245,37296,37428,37491,37547,37616,37678,37750,37817,37930,38020,38101,38178,38246,38314,38375", "endColumns": "61,71,61,66,70,72,71,92,66,73,77,86,55,88,58,139,144,107,84,81,86,86,112,86,86,136,93,103,164,170,95,93,119,78,57,60,77,79,80,66,80,78,85,138,66,63,62,95,72,76,63,71,70,67,86,80,95,81,66,77,64,71,55,86,87,87,107,58,79,89,106,67,71,82,52,53,63,80,143,202,217,85,65,69,62,85,90,78,71,69,70,69,78,82,101,70,62,50,131,62,55,68,61,71,66,112,89,80,76,67,67,60,114", "endOffsets": "13808,13880,13942,18961,20293,20366,20438,22037,23036,23155,23233,25400,26777,26921,27099,27452,27597,27705,27925,28097,28265,28352,28465,28552,28639,28776,28870,28974,29139,29310,29406,29579,29699,29778,29836,29897,29975,30055,30136,30203,30284,30363,30449,30588,30655,30719,30782,30878,30951,31028,31092,31164,31235,31303,31390,31903,31999,32081,32148,32226,32291,32363,32419,32506,32594,32682,32790,32849,32929,33019,33126,33194,33391,33662,34703,34757,34821,34902,35046,35249,35467,35553,35619,36178,36241,36327,36418,36497,36569,36639,36710,36780,36859,36942,37044,37115,37178,37291,37423,37486,37542,37611,37673,37745,37812,37925,38015,38096,38173,38241,38309,38370,38485"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4425,4529,4663,4783,4889,5021,5141,5246,5467,5601,5702,5835,5954,6074,6194,6254,6313", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "4524,4658,4778,4884,5016,5136,5241,5340,5596,5697,5830,5949,6069,6189,6249,6308,6379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,134,223,290,357,420,490", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "129,218,285,352,415,485,546"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "12808,12887,12976,13043,13110,13173,13243", "endColumns": "78,88,66,66,62,69,60", "endOffsets": "12882,12971,13038,13105,13168,13238,13299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,81", "endOffsets": "135,217"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "38748,38833", "endColumns": "84,81", "endOffsets": "38828,38910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,201", "endColumns": "74,70,73", "endOffsets": "125,196,270"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4350,6663,6933", "endColumns": "74,70,73", "endOffsets": "4420,6729,7002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,12337", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,12411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4186,4272,6477,6566,6734,7507,7585,11826,11911,11986,12050,12187,12261,12567,38490,38566,38631", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "4267,4345,6561,6658,6812,7580,7658,11906,11981,12045,12109,12256,12332,12631,38561,38626,38743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,189,256,318,388,442,502,555,626,692,762,848,934,1009,1093,1167,1240,1322,1403,1466,1544,1622,1697,1781,1856,1934,2004,2089,2158,2237,2310,2380,2455,2536,2600,2672,2767,2852,2914,2975,3433,3864,3934,4025,4114,4169,4253,4331,4392,4467,4547,4602,4676,4724,4795,4881,4947,5013,5080,5146,5191,5259,5343,5414,5457,5500,5569,5627,5686,5797,5908,6000,6063,6133,6197,6275,6343,6410,6473,6548,6612,6692,6768,6847,6896,6990,7035,7090,7152,7211,7279,7338,7401,7479,7539,7586", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "113,184,251,313,383,437,497,550,621,687,757,843,929,1004,1088,1162,1235,1317,1398,1461,1539,1617,1692,1776,1851,1929,1999,2084,2153,2232,2305,2375,2450,2531,2595,2667,2762,2847,2909,2970,3428,3859,3929,4020,4109,4164,4248,4326,4387,4462,4542,4597,4671,4719,4790,4876,4942,5008,5075,5141,5186,5254,5338,5409,5452,5495,5564,5622,5681,5792,5903,5995,6058,6128,6192,6270,6338,6405,6468,6543,6607,6687,6763,6842,6891,6985,7030,7085,7147,7206,7274,7333,7396,7474,7534,7581,7643"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13304,13367,13438,13505,13567,13637,13691,13947,14000,14071,14137,14340,14499,14585,14724,15064,15329,16309,16523,16604,16667,16808,16886,16961,17105,17379,17457,17527,17612,17681,17760,17833,17903,17978,18059,18196,18406,18501,19101,19163,19224,20443,20874,20944,21035,21124,21179,21263,21341,21402,21477,21557,21612,22106,22154,22225,22469,22535,22601,22668,23041,23360,23428,23512,23583,23626,23899,23968,24026,24085,24196,24307,24574,24637,25405,25469,25615,25683,25969,26032,26170,26646,27104,27180,33396,33445,33539,33667,34106,35624,35683,35751,35810,35873,35951,36011,37183", "endColumns": "62,70,66,61,69,53,59,52,70,65,69,85,85,74,83,73,72,81,80,62,77,77,74,83,74,77,69,84,68,78,72,69,74,80,63,71,94,84,61,60,457,430,69,90,88,54,83,77,60,74,79,54,73,47,70,85,65,65,66,65,44,67,83,70,42,42,68,57,58,110,110,91,62,69,63,77,67,66,62,74,63,79,75,78,48,93,44,54,61,58,67,58,62,77,59,46,61", "endOffsets": "13362,13433,13500,13562,13632,13686,13746,13995,14066,14132,14202,14421,14580,14655,14803,15133,15397,16386,16599,16662,16740,16881,16956,17040,17175,17452,17522,17607,17676,17755,17828,17898,17973,18054,18118,18263,18496,18581,19158,19219,19677,20869,20939,21030,21119,21174,21258,21336,21397,21472,21552,21607,21681,22149,22220,22306,22530,22596,22663,22729,23081,23423,23507,23578,23621,23664,23963,24021,24080,24191,24302,24394,24632,24702,25464,25542,25678,25745,26027,26102,26229,26721,27175,27254,33440,33534,33579,33717,34163,35678,35746,35805,35868,35946,36006,36053,37240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,142,286,368,503,546,606,686,750,911,968,1048,1105,1159,1223,1294,1381,1574,1635,1696,1749,1800,1871,1973,2060,2114,2191,2259,2333,2396,2472,2664,2734,2808,2863,2921,2982,3040,3104,3175,3265,3346,3425,3527,3616,3695,3773,3857,3982,4116,4215,4312,4366,4713,4796,4853", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "137,281,363,498,541,601,681,745,906,963,1043,1100,1154,1218,1289,1376,1569,1630,1691,1744,1795,1866,1968,2055,2109,2186,2254,2328,2391,2467,2659,2729,2803,2858,2916,2977,3035,3099,3170,3260,3341,3420,3522,3611,3690,3768,3852,3977,4111,4210,4307,4361,4708,4791,4848,4903"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18586,18673,18817,18966,19682,19725,19785,19865,19929,20090,20147,21686,21895,22042,22311,22382,22734,23238,23299,23784,24523,24781,24852,24954,25041,25095,25547,25750,26107,26234,26310,26502,26572,26782,26926,26984,27259,27710,27774,27930,28102,29411,31395,31497,31586,31665,31743,33199,33722,33856,33955,34052,34168,34515,34598,36058", "endColumns": "86,143,81,134,42,59,79,63,160,56,79,56,53,63,70,86,192,60,60,52,50,70,101,86,53,76,67,73,62,75,191,69,73,54,57,60,57,63,70,89,80,78,101,88,78,77,83,124,133,98,96,53,346,82,56,54", "endOffsets": "18668,18812,18894,19096,19720,19780,19860,19924,20085,20142,20222,21738,21944,22101,22377,22464,22922,23294,23355,23832,24569,24847,24949,25036,25090,25167,25610,25819,26165,26305,26497,26567,26641,26832,26979,27040,27312,27769,27840,28015,28178,29485,31492,31581,31660,31738,31822,33319,33851,33950,34047,34101,34510,34593,34650,36108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3170,3921,4006,4110,6817,6870,7007,7443,7663,7721,7802,7863,7927,7982,8041,8098,8152,8245,8301,8358,8412,8478,8578,8654,8725,8804,8877,8958,9080,9142,9204,9305,9384,9459,9512,9563,9629,9699,9769,9840,9910,9974,10045,10113,10176,10267,10346,10409,10489,10571,10643,10714,10786,10834,10906,10970,11045,11122,11184,11248,11311,11378,11464,11550,11631,11714,11771,12114,12416,12494,12636", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "298,2951,3015,3084,3165,3247,4001,4105,4181,6865,6928,7086,7502,7716,7797,7858,7922,7977,8036,8093,8147,8240,8296,8353,8407,8473,8573,8649,8720,8799,8872,8953,9075,9137,9199,9300,9379,9454,9507,9558,9624,9694,9764,9835,9905,9969,10040,10108,10171,10262,10341,10404,10484,10566,10638,10709,10781,10829,10901,10965,11040,11117,11179,11243,11306,11373,11459,11545,11626,11709,11766,11821,12182,12489,12562,12702"}}]}]}