{"logs": [{"outputFile": "com.flutter.stripe.stripe_android-release-91:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,248,315,382,448,521", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "139,243,310,377,443,516,583"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13856,13945,14049,14116,14183,14249,14322", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "13940,14044,14111,14178,14244,14317,14384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4529,6998,7093,7268,8071,8148,12824,12913,12995,13060,13205,13286,13608,44479,44557,44624", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4524,4604,7088,7187,7345,8143,8232,12908,12990,13055,13120,13281,13365,13673,44552,44619,44739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,74", "endOffsets": "125,201,276"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4609,7192,7473", "endColumns": "74,75,74", "endOffsets": "4679,7263,7543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "27718", "endColumns": "81", "endOffsets": "27795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,211,291,383,470,548,625,754,846,944,1033,1150,1208,1327,1391,1569,1760,1898,1987,2093,2184,2285,2466,2580,2678,2927,3035,3160,3352,3562,3658,3770,3958,4052,4110,4174,4255,4350,4451,4528,4628,4717,4827,5040,5109,5175,5249,5354,5431,5520,5586,5667,5738,5806,5914,6001,6111,6201,6272,6360,6427,6513,6570,6670,6776,6879,7005,7064,7155,7255,7376,7447,7534,7632,7686,7742,7816,7918,8087,8352,8642,8744,8815,8905,8976,9081,9196,9287,9361,9430,9515,9599,9687,9803,9937,10004,10074,10128,10298,10367,10426,10500,10574,10651,10732,10868,10992,11105,11188,11263,11348,11415", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "119,206,286,378,465,543,620,749,841,939,1028,1145,1203,1322,1386,1564,1755,1893,1982,2088,2179,2280,2461,2575,2673,2922,3030,3155,3347,3557,3653,3765,3953,4047,4105,4169,4250,4345,4446,4523,4623,4712,4822,5035,5104,5170,5244,5349,5426,5515,5581,5662,5733,5801,5909,5996,6106,6196,6267,6355,6422,6508,6565,6665,6771,6874,7000,7059,7150,7250,7371,7442,7529,7627,7681,7737,7811,7913,8082,8347,8637,8739,8810,8900,8971,9076,9191,9282,9356,9425,9510,9594,9682,9798,9932,9999,10069,10123,10293,10362,10421,10495,10569,10646,10727,10863,10987,11100,11183,11258,11343,11410,11567"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14856,14925,15012,20518,22319,22406,22484,24451,25678,25815,25913,28464,30236,30361,30602,30894,31072,31263,31547,31736,31938,32029,32130,32311,32425,32523,32772,32880,33005,33197,33407,33595,33707,33895,33989,34047,34111,34192,34287,34388,34465,34565,34654,34764,34977,35046,35112,35186,35291,35368,35457,35523,35604,35675,35743,36284,36371,36481,36571,36642,36730,36797,36883,36940,37040,37146,37249,37375,37434,37525,37625,37746,38015,38334,39927,39981,40037,40111,40213,40382,40647,40937,41039,41646,41736,41807,41912,42027,42118,42192,42261,42346,42430,42518,42634,42768,42835,42981,43035,43205,43274,43333,43407,43481,43558,43639,43775,43899,44012,44095,44170,44255,44322", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "14920,15007,15087,20605,22401,22479,22556,24575,25765,25908,25997,28576,30289,30475,30661,31067,31258,31396,31631,31837,32024,32125,32306,32420,32518,32767,32875,33000,33192,33402,33498,33702,33890,33984,34042,34106,34187,34282,34383,34460,34560,34649,34759,34972,35041,35107,35181,35286,35363,35452,35518,35599,35670,35738,35846,36366,36476,36566,36637,36725,36792,36878,36935,37035,37141,37244,37370,37429,37520,37620,37741,37812,38097,38427,39976,40032,40106,40208,40377,40642,40932,41034,41105,41731,41802,41907,42022,42113,42187,42256,42341,42425,42513,42629,42763,42830,42900,43030,43200,43269,43328,43402,43476,43553,43634,43770,43894,44007,44090,44165,44250,44317,44474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,260,328,403,463,522,575,647,727,805,903,1001,1087,1166,1243,1326,1419,1508,1574,1660,1746,1830,1934,2014,2101,2183,2285,2359,2447,2543,2629,2710,2809,2881,2956,3060,3157,3227,3292,3967,4618,4692,4809,4909,4964,5062,5152,5220,5311,5397,5454,5537,5588,5665,5761,5831,5902,5969,6035,6080,6158,6250,6326,6373,6421,6489,6547,6612,6794,6957,7073,7137,7221,7298,7399,7488,7572,7648,7740,7820,7922,8004,8090,8143,8274,8322,8376,8443,8510,8584,8648,8720,8808,8874,8924", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "118,195,255,323,398,458,517,570,642,722,800,898,996,1082,1161,1238,1321,1414,1503,1569,1655,1741,1825,1929,2009,2096,2178,2280,2354,2442,2538,2624,2705,2804,2876,2951,3055,3152,3222,3287,3962,4613,4687,4804,4904,4959,5057,5147,5215,5306,5392,5449,5532,5583,5660,5756,5826,5897,5964,6030,6075,6153,6245,6321,6368,6416,6484,6542,6607,6789,6952,7068,7132,7216,7293,7394,7483,7567,7643,7735,7815,7917,7999,8085,8138,8269,8317,8371,8438,8505,8579,8643,8715,8803,8869,8919,8995"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14389,14457,14534,14594,14662,14737,14797,15092,15145,15217,15297,15518,15694,15792,15945,16297,16581,17590,17828,17917,17983,18135,18221,18305,18469,18769,18856,18938,19040,19114,19202,19298,19384,19465,19564,19710,19953,20057,20819,20889,20954,22561,23212,23286,23403,23503,23558,23656,23746,23814,23905,23991,24048,24644,24695,24772,25038,25108,25179,25246,25770,26141,26219,26311,26387,26434,26727,26795,26853,26918,27100,27263,27570,27634,28581,28658,28838,28927,29269,29345,29503,30134,30666,30748,38102,38155,38286,38432,38980,41110,41177,41251,41315,41387,41475,41541,42905", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "14452,14529,14589,14657,14732,14792,14851,15140,15212,15292,15370,15611,15787,15873,16019,16369,16659,17678,17912,17978,18064,18216,18300,18404,18544,18851,18933,19035,19109,19197,19293,19379,19460,19559,19631,19780,20052,20149,20884,20949,21624,23207,23281,23398,23498,23553,23651,23741,23809,23900,23986,24043,24126,24690,24767,24863,25103,25174,25241,25307,25810,26214,26306,26382,26429,26477,26790,26848,26913,27095,27258,27374,27629,27713,28653,28754,28922,29006,29340,29432,29578,30231,30743,30829,38150,38281,38329,38481,39042,41172,41246,41310,41382,41470,41536,41586,42976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,324,419,628,676,743,842,911,1166,1226,1318,1389,1444,1508,1585,1678,1996,2070,2135,2188,2241,2337,2461,2577,2634,2721,2800,2883,2949,3048,3346,3423,3500,3567,3627,3689,3749,3818,3895,3995,4091,4183,4287,4379,4452,4531,4616,4814,5021,5133,5253,5308,6025,6124,6188", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "162,319,414,623,671,738,837,906,1161,1221,1313,1384,1439,1503,1580,1673,1991,2065,2130,2183,2236,2332,2456,2572,2629,2716,2795,2878,2944,3043,3341,3418,3495,3562,3622,3684,3744,3813,3890,3990,4086,4178,4282,4374,4447,4526,4611,4809,5016,5128,5248,5303,6020,6119,6183,6238"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20154,20266,20423,20610,21629,21677,21744,21843,21912,22167,22227,24131,24396,24580,24868,24945,25312,26002,26076,26612,27517,27800,27896,28020,28136,28193,28759,29011,29437,29583,29682,29980,30057,30294,30480,30540,30834,31401,31470,31636,31842,33503,35851,35955,36047,36120,36199,37817,38486,38693,38805,38925,39047,39764,39863,41591", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "20261,20418,20513,20814,21672,21739,21838,21907,22162,22222,22314,24197,24446,24639,24940,25033,25625,26071,26136,26660,27565,27891,28015,28131,28188,28275,28833,29089,29498,29677,29975,30052,30129,30356,30535,30597,30889,31465,31542,31731,31933,33590,35950,36042,36115,36194,36279,38010,38688,38800,38920,38975,39759,39858,39922,41641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,353,420,481,550,617,681,749,817,877,935,1008,1072,1142,1205,1278,1342,1431,1505,1582,1673,1782,1867,1915,1970,2045,2107,2176,2245,2342,2429,2517", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "115,193,260,348,415,476,545,612,676,744,812,872,930,1003,1067,1137,1200,1273,1337,1426,1500,1577,1668,1777,1862,1910,1965,2040,2102,2171,2240,2337,2424,2512,2599"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15375,15616,15878,16024,16112,16179,16664,16733,16870,16934,17002,17070,17130,17188,17261,17384,17454,17683,18549,18613,19636,19785,19862,24202,24311,25630,26482,26537,26665,27379,27448,28280,28377,29094,29182", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "15435,15689,15940,16107,16174,16235,16728,16795,16929,16997,17065,17125,17183,17256,17320,17449,17512,17751,18608,18697,19705,19857,19948,24306,24391,25673,26532,26607,26722,27443,27512,28372,28459,29177,29264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,397,467,526,599,671,737,797", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "128,185,247,332,392,462,521,594,666,732,792,859"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15440,16240,16374,16436,16521,16800,17325,17517,17756,18069,18409,18702", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "15513,16292,16431,16516,16576,16865,17379,17585,17823,18130,18464,18764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "44744,44834", "endColumns": "89,86", "endOffsets": "44829,44916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,7350,7409,7548,7998,8237,8297,8384,8448,8510,8572,8640,8705,8759,8877,8935,8996,9052,9127,9253,9339,9416,9507,9591,9671,9812,9890,9970,10092,10178,10256,10312,10363,10429,10497,10571,10642,10717,10789,10867,10937,11010,11114,11198,11275,11363,11452,11526,11599,11684,11733,11811,11877,11957,12040,12102,12166,12229,12298,12406,12509,12610,12709,12769,13125,13450,13530,13678", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,7404,7468,7629,8066,8292,8379,8443,8505,8567,8635,8700,8754,8872,8930,8991,9047,9122,9248,9334,9411,9502,9586,9666,9807,9885,9965,10087,10173,10251,10307,10358,10424,10492,10566,10637,10712,10784,10862,10932,11005,11109,11193,11270,11358,11447,11521,11594,11679,11728,11806,11872,11952,12035,12097,12161,12224,12293,12401,12504,12605,12704,12764,12819,13200,13525,13603,13750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6886,7634,7733,7840", "endColumns": "111,98,106,96", "endOffsets": "6993,7728,7835,7932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5679", "endColumns": "126", "endOffsets": "5801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,13755", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,13851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,13370", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,13445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4790,4950,5077,5186,5329,5454,5574,5806,5962,6068,6230,6357,6502,6680,6746,6808", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4785,4945,5072,5181,5324,5449,5569,5674,5957,6063,6225,6352,6497,6675,6741,6803,6881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "7937,44921", "endColumns": "60,73", "endOffsets": "7993,44990"}}]}, {"outputFile": "com.flutter.stripe.stripe_android-mergeReleaseResources-89:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b53e9d59086260338ee7975e5a141b\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,144,248,315,382,448,521", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "139,243,310,377,443,516,583"}, "to": {"startLines": "157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "13856,13945,14049,14116,14183,14249,14322", "endColumns": "88,103,66,66,65,72,66", "endOffsets": "13940,14044,14111,14178,14244,14317,14384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161c0e0312fdbe1c4dc27a1c26660c71\\transformed\\jetified-ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "48,49,70,71,73,83,84,144,145,146,147,149,150,154,478,479,480", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4529,6998,7093,7268,8071,8148,12824,12913,12995,13060,13205,13286,13608,44479,44557,44624", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "4524,4604,7088,7187,7345,8143,8232,12908,12990,13055,13120,13281,13365,13673,44552,44619,44739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13f3f4a3c71436b6b5994ca18cfa270e\\transformed\\jetified-material3-1.0.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,206", "endColumns": "74,75,74", "endOffsets": "125,201,276"}, "to": {"startLines": "50,72,76", "startColumns": "4,4,4", "startOffsets": "4609,7192,7473", "endColumns": "74,75,74", "endOffsets": "4679,7263,7543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e112303fa4750aac2b7bbb557cf55a97\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "81", "endOffsets": "132"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "27718", "endColumns": "81", "endOffsets": "27795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\adbcc2d4d2dd06f20830afc2b9e0db31\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,211,291,383,470,548,625,754,846,944,1033,1150,1208,1327,1391,1569,1760,1898,1987,2093,2184,2285,2466,2580,2678,2927,3035,3160,3352,3562,3658,3770,3958,4052,4110,4174,4255,4350,4451,4528,4628,4717,4827,5040,5109,5175,5249,5354,5431,5520,5586,5667,5738,5806,5914,6001,6111,6201,6272,6360,6427,6513,6570,6670,6776,6879,7005,7064,7155,7255,7376,7447,7534,7632,7686,7742,7816,7918,8087,8352,8642,8744,8815,8905,8976,9081,9196,9287,9361,9430,9515,9599,9687,9803,9937,10004,10074,10128,10298,10367,10426,10500,10574,10651,10732,10868,10992,11105,11188,11263,11348,11415", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "119,206,286,378,465,543,620,749,841,939,1028,1145,1203,1322,1386,1564,1755,1893,1982,2088,2179,2280,2461,2575,2673,2922,3030,3155,3347,3557,3653,3765,3953,4047,4105,4169,4250,4345,4446,4523,4623,4712,4822,5035,5104,5170,5244,5349,5426,5515,5581,5662,5733,5801,5909,5996,6106,6196,6267,6355,6422,6508,6565,6665,6771,6874,7000,7059,7150,7250,7371,7442,7529,7627,7681,7737,7811,7913,8082,8347,8637,8739,8810,8900,8971,9076,9191,9282,9356,9425,9510,9594,9682,9798,9932,9999,10069,10123,10293,10362,10421,10495,10569,10646,10727,10863,10987,11100,11183,11258,11343,11410,11567"}, "to": {"startLines": "171,172,173,243,255,256,257,274,287,289,290,321,339,341,344,348,349,350,353,355,357,358,359,360,361,362,363,364,365,366,367,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,416,420,430,431,432,433,434,435,436,437,438,447,448,449,450,451,452,453,454,455,456,457,458,459,460,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14856,14925,15012,20518,22319,22406,22484,24451,25678,25815,25913,28464,30236,30361,30602,30894,31072,31263,31547,31736,31938,32029,32130,32311,32425,32523,32772,32880,33005,33197,33407,33595,33707,33895,33989,34047,34111,34192,34287,34388,34465,34565,34654,34764,34977,35046,35112,35186,35291,35368,35457,35523,35604,35675,35743,36284,36371,36481,36571,36642,36730,36797,36883,36940,37040,37146,37249,37375,37434,37525,37625,37746,38015,38334,39927,39981,40037,40111,40213,40382,40647,40937,41039,41646,41736,41807,41912,42027,42118,42192,42261,42346,42430,42518,42634,42768,42835,42981,43035,43205,43274,43333,43407,43481,43558,43639,43775,43899,44012,44095,44170,44255,44322", "endColumns": "68,86,79,91,86,77,76,128,91,97,88,116,57,118,63,177,190,137,88,105,90,100,180,113,97,248,107,124,191,209,95,111,187,93,57,63,80,94,100,76,99,88,109,212,68,65,73,104,76,88,65,80,70,67,107,86,109,89,70,87,66,85,56,99,105,102,125,58,90,99,120,70,86,97,53,55,73,101,168,264,289,101,70,89,70,104,114,90,73,68,84,83,87,115,133,66,69,53,169,68,58,73,73,76,80,135,123,112,82,74,84,66,156", "endOffsets": "14920,15007,15087,20605,22401,22479,22556,24575,25765,25908,25997,28576,30289,30475,30661,31067,31258,31396,31631,31837,32024,32125,32306,32420,32518,32767,32875,33000,33192,33402,33498,33702,33890,33984,34042,34106,34187,34282,34383,34460,34560,34649,34759,34972,35041,35107,35181,35286,35363,35452,35518,35599,35670,35738,35846,36366,36476,36566,36637,36725,36792,36878,36935,37035,37141,37244,37370,37429,37520,37620,37741,37812,38097,38427,39976,40032,40106,40208,40377,40642,40932,41034,41105,41731,41802,41907,42022,42113,42187,42256,42341,42425,42513,42629,42763,42830,42900,43030,43200,43269,43328,43402,43476,43553,43634,43770,43894,44007,44090,44165,44250,44317,44474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b59a5b1cb8ceb4fcfd39f7b09f5d4ea4\\transformed\\jetified-payments-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,200,260,328,403,463,522,575,647,727,805,903,1001,1087,1166,1243,1326,1419,1508,1574,1660,1746,1830,1934,2014,2101,2183,2285,2359,2447,2543,2629,2710,2809,2881,2956,3060,3157,3227,3292,3967,4618,4692,4809,4909,4964,5062,5152,5220,5311,5397,5454,5537,5588,5665,5761,5831,5902,5969,6035,6080,6158,6250,6326,6373,6421,6489,6547,6612,6794,6957,7073,7137,7221,7298,7399,7488,7572,7648,7740,7820,7922,8004,8090,8143,8274,8322,8376,8443,8510,8584,8648,8720,8808,8874,8924", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "118,195,255,323,398,458,517,570,642,722,800,898,996,1082,1161,1238,1321,1414,1503,1569,1655,1741,1825,1929,2009,2096,2178,2280,2354,2442,2538,2624,2705,2804,2876,2951,3055,3152,3222,3287,3962,4613,4687,4804,4904,4959,5057,5147,5215,5306,5392,5449,5532,5583,5660,5756,5826,5897,5964,6030,6075,6153,6245,6321,6368,6416,6484,6542,6607,6789,6952,7068,7132,7216,7293,7394,7483,7567,7643,7735,7815,7917,7999,8085,8138,8269,8317,8371,8438,8505,8579,8643,8715,8803,8869,8919,8995"}, "to": {"startLines": "164,165,166,167,168,169,170,174,175,176,177,180,182,183,185,190,194,209,212,213,214,216,217,218,220,224,225,226,227,228,229,230,231,232,233,235,238,239,245,246,247,258,259,260,261,262,263,264,265,266,267,268,269,276,277,278,281,282,283,284,288,293,294,295,296,297,302,303,304,305,306,307,311,312,322,323,325,326,330,331,333,338,345,346,417,418,419,421,426,439,440,441,442,443,444,445,461", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14389,14457,14534,14594,14662,14737,14797,15092,15145,15217,15297,15518,15694,15792,15945,16297,16581,17590,17828,17917,17983,18135,18221,18305,18469,18769,18856,18938,19040,19114,19202,19298,19384,19465,19564,19710,19953,20057,20819,20889,20954,22561,23212,23286,23403,23503,23558,23656,23746,23814,23905,23991,24048,24644,24695,24772,25038,25108,25179,25246,25770,26141,26219,26311,26387,26434,26727,26795,26853,26918,27100,27263,27570,27634,28581,28658,28838,28927,29269,29345,29503,30134,30666,30748,38102,38155,38286,38432,38980,41110,41177,41251,41315,41387,41475,41541,42905", "endColumns": "67,76,59,67,74,59,58,52,71,79,77,97,97,85,78,76,82,92,88,65,85,85,83,103,79,86,81,101,73,87,95,85,80,98,71,74,103,96,69,64,674,650,73,116,99,54,97,89,67,90,85,56,82,50,76,95,69,70,66,65,44,77,91,75,46,47,67,57,64,181,162,115,63,83,76,100,88,83,75,91,79,101,81,85,52,130,47,53,66,66,73,63,71,87,65,49,75", "endOffsets": "14452,14529,14589,14657,14732,14792,14851,15140,15212,15292,15370,15611,15787,15873,16019,16369,16659,17678,17912,17978,18064,18216,18300,18404,18544,18851,18933,19035,19109,19197,19293,19379,19460,19559,19631,19780,20052,20149,20884,20949,21624,23207,23281,23398,23498,23553,23651,23741,23809,23900,23986,24043,24126,24690,24767,24863,25103,25174,25241,25307,25810,26214,26306,26382,26429,26477,26790,26848,26913,27095,27258,27374,27629,27713,28653,28754,28922,29006,29340,29432,29578,30231,30743,30829,38150,38281,38329,38481,39042,41172,41246,41310,41382,41470,41536,41586,42976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e73e6777176bc0af5d3600397475ed74\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,324,419,628,676,743,842,911,1166,1226,1318,1389,1444,1508,1585,1678,1996,2070,2135,2188,2241,2337,2461,2577,2634,2721,2800,2883,2949,3048,3346,3423,3500,3567,3627,3689,3749,3818,3895,3995,4091,4183,4287,4379,4452,4531,4616,4814,5021,5133,5253,5308,6025,6124,6188", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "162,319,414,623,671,738,837,906,1161,1221,1313,1384,1439,1503,1580,1673,1991,2065,2130,2183,2236,2332,2456,2572,2629,2716,2795,2878,2944,3043,3341,3418,3495,3562,3622,3684,3744,3813,3890,3990,4086,4178,4282,4374,4447,4526,4611,4809,5016,5128,5248,5303,6020,6119,6183,6238"}, "to": {"startLines": "240,241,242,244,248,249,250,251,252,253,254,270,273,275,279,280,285,291,292,300,310,314,315,316,317,318,324,327,332,334,335,336,337,340,342,343,347,351,352,354,356,368,393,394,395,396,397,415,422,423,424,425,427,428,429,446", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20154,20266,20423,20610,21629,21677,21744,21843,21912,22167,22227,24131,24396,24580,24868,24945,25312,26002,26076,26612,27517,27800,27896,28020,28136,28193,28759,29011,29437,29583,29682,29980,30057,30294,30480,30540,30834,31401,31470,31636,31842,33503,35851,35955,36047,36120,36199,37817,38486,38693,38805,38925,39047,39764,39863,41591", "endColumns": "111,156,94,208,47,66,98,68,254,59,91,70,54,63,76,92,317,73,64,52,52,95,123,115,56,86,78,82,65,98,297,76,76,66,59,61,59,68,76,99,95,91,103,91,72,78,84,197,206,111,119,54,716,98,63,54", "endOffsets": "20261,20418,20513,20814,21672,21739,21838,21907,22162,22222,22314,24197,24446,24639,24940,25033,25625,26071,26136,26660,27565,27891,28015,28131,28188,28275,28833,29089,29498,29677,29975,30052,30129,30356,30535,30597,30889,31465,31542,31731,31933,33590,35950,36042,36115,36194,36279,38010,38688,38800,38920,38975,39759,39858,39922,41641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5503dc18b49a35e56a79a1d97de8acc7\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,198,265,353,420,481,550,617,681,749,817,877,935,1008,1072,1142,1205,1278,1342,1431,1505,1582,1673,1782,1867,1915,1970,2045,2107,2176,2245,2342,2429,2517", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "115,193,260,348,415,476,545,612,676,744,812,872,930,1003,1067,1137,1200,1273,1337,1426,1500,1577,1668,1777,1862,1910,1965,2040,2102,2171,2240,2337,2424,2512,2599"}, "to": {"startLines": "178,181,184,186,187,188,195,196,198,199,200,201,202,203,204,206,207,210,221,222,234,236,237,271,272,286,298,299,301,308,309,319,320,328,329", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15375,15616,15878,16024,16112,16179,16664,16733,16870,16934,17002,17070,17130,17188,17261,17384,17454,17683,18549,18613,19636,19785,19862,24202,24311,25630,26482,26537,26665,27379,27448,28280,28377,29094,29182", "endColumns": "64,77,66,87,66,60,68,66,63,67,67,59,57,72,63,69,62,72,63,88,73,76,90,108,84,47,54,74,61,68,68,96,86,87,86", "endOffsets": "15435,15689,15940,16107,16174,16235,16728,16795,16929,16997,17065,17125,17183,17256,17320,17449,17512,17751,18608,18697,19705,19857,19948,24306,24391,25673,26532,26607,26722,27443,27512,28372,28459,29177,29264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a915145b02e2d03a7736738bee6b52a4\\transformed\\jetified-stripe-core-21.6.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,190,252,337,397,467,526,599,671,737,797", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "128,185,247,332,392,462,521,594,666,732,792,859"}, "to": {"startLines": "179,189,191,192,193,197,205,208,211,215,219,223", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15440,16240,16374,16436,16521,16800,17325,17517,17756,18069,18409,18702", "endColumns": "77,56,61,84,59,69,58,72,71,65,59,66", "endOffsets": "15513,16292,16431,16516,16576,16865,17379,17585,17823,18130,18464,18764"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f512b4218784e5586af1fe189e6353e1\\transformed\\jetified-foundation-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,86", "endOffsets": "140,227"}, "to": {"startLines": "481,482", "startColumns": "4,4", "startOffsets": "44744,44834", "endColumns": "89,86", "endOffsets": "44829,44916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\142ea8e1d53b889debe590f1a028f752\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,7350,7409,7548,7998,8237,8297,8384,8448,8510,8572,8640,8705,8759,8877,8935,8996,9052,9127,9253,9339,9416,9507,9591,9671,9812,9890,9970,10092,10178,10256,10312,10363,10429,10497,10571,10642,10717,10789,10867,10937,11010,11114,11198,11275,11363,11452,11526,11599,11684,11733,11811,11877,11957,12040,12102,12166,12229,12298,12406,12509,12610,12709,12769,13125,13450,13530,13678", "endLines": "5,33,34,35,36,37,45,46,47,74,75,77,82,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,148,152,153,155", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,7404,7468,7629,8066,8292,8379,8443,8505,8567,8635,8700,8754,8872,8930,8991,9047,9122,9248,9334,9411,9502,9586,9666,9807,9885,9965,10087,10173,10251,10307,10358,10424,10492,10566,10637,10712,10784,10862,10932,11005,11109,11193,11270,11358,11447,11521,11594,11679,11728,11806,11872,11952,12035,12097,12161,12224,12293,12401,12504,12605,12704,12764,12819,13200,13525,13603,13750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\99cb531c85b4e78d256404c559b7f854\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "69,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "6886,7634,7733,7840", "endColumns": "111,98,106,96", "endOffsets": "6993,7728,7835,7932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca1c782e5d96a65022bcdf1482464a1d\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5679", "endColumns": "126", "endOffsets": "5801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fb8f63f444529d06383a62feba7898e\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,156", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,13755", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,13851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1d699932866bd3bebbc433d96259732\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,13370", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,13445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1d5a0988e27b8cabce1211053b272581\\transformed\\jetified-play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4684,4790,4950,5077,5186,5329,5454,5574,5806,5962,6068,6230,6357,6502,6680,6746,6808", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4785,4945,5072,5181,5324,5449,5569,5674,5957,6063,6225,6352,6497,6675,6741,6803,6881"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2f9b794b42ee204c17ebb0be32064c11\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,69", "endOffsets": "258,328"}, "to": {"startLines": "81,483", "startColumns": "4,4", "startOffsets": "7937,44921", "endColumns": "60,73", "endOffsets": "7993,44990"}}]}]}