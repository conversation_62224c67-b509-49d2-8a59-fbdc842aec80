{"logs": [{"outputFile": "com.velvete.ly.app-mergeReleaseResources-108:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\93faec8a41fe746c4c16eed1694e60eb\\transformed\\jetified-play-services-wallet-19.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5", "startColumns": "0,0", "startOffsets": "202,259", "endColumns": "56,74", "endOffsets": "258,333"}, "to": {"startLines": "108,515", "startColumns": "4,4", "startOffsets": "10989,50288", "endColumns": "60,78", "endOffsets": "11045,50362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ca4f41cbd00ab33cfa3a1ba77e230168\\transformed\\jetified-material3-1.0.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "52,98,103", "startColumns": "4,4,4", "startOffsets": "4806,10084,10490", "endColumns": "76,80,77", "endOffsets": "4878,10160,10563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fc19047cd54e21c30bae5bff58239c4a\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "84", "startColumns": "4", "startOffsets": "8428", "endColumns": "156", "endOffsets": "8580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32a9e3a05e9238088b3f4a5e4aa55da0\\transformed\\jetified-payments-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,202,272,354,450,515,586,641,712,801,875,980,1085,1173,1255,1340,1430,1532,1634,1708,1807,1900,1990,2105,2190,2289,2370,2476,2550,2643,2748,2842,2936,3037,3103,3174,3290,3399,3470,3534,4277,4954,5028,5134,5241,5296,5385,5471,5540,5627,5729,5785,5875,5924,6001,6108,6173,6244,6311,6377,6426,6506,6598,6673,6720,6774,6848,6906,6985,7164,7319,7452,7519,7613,7692,7789,7872,7953,8029,8118,8200,8308,8395,8489,8545,8682,8732,8787,8876,8943,9012,9082,9151,9239,9310,9361", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "119,197,267,349,445,510,581,636,707,796,870,975,1080,1168,1250,1335,1425,1527,1629,1703,1802,1895,1985,2100,2185,2284,2365,2471,2545,2638,2743,2837,2931,3032,3098,3169,3285,3394,3465,3529,4272,4949,5023,5129,5236,5291,5380,5466,5535,5622,5724,5780,5870,5919,5996,6103,6168,6239,6306,6372,6421,6501,6593,6668,6715,6769,6843,6901,6980,7159,7314,7447,7514,7608,7687,7784,7867,7948,8024,8113,8195,8303,8390,8484,8540,8677,8727,8782,8871,8938,9007,9077,9146,9234,9305,9356,9430"}, "to": {"startLines": "193,194,195,196,197,198,199,203,204,205,206,209,211,212,214,219,223,238,241,242,243,245,246,247,249,253,254,255,256,257,258,259,260,261,262,264,267,268,274,275,276,287,288,289,290,291,292,293,294,295,296,297,298,305,306,307,310,311,312,313,317,322,323,324,325,326,331,332,333,334,335,336,340,341,351,352,354,355,359,360,362,367,374,375,446,447,448,450,455,468,469,470,471,472,473,474,490", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17853,17922,18000,18070,18152,18248,18313,18628,18683,18754,18843,19060,19245,19350,19505,19861,20160,21224,21491,21593,21667,21834,21927,22017,22192,22502,22601,22682,22788,22862,22955,23060,23154,23248,23349,23491,23748,23864,24700,24771,24835,26557,27234,27308,27414,27521,27576,27665,27751,27820,27907,28009,28065,28689,28738,28815,29090,29155,29226,29293,29856,30225,30305,30397,30472,30519,30827,30901,30959,31038,31217,31372,31704,31771,32761,32840,33019,33102,33466,33542,33716,34377,34941,35028,42786,42842,42979,43140,43732,45999,46066,46135,46205,46274,46362,46433,47892", "endColumns": "68,77,69,81,95,64,70,54,70,88,73,104,104,87,81,84,89,101,101,73,98,92,89,114,84,98,80,105,73,92,104,93,93,100,65,70,115,108,70,63,742,676,73,105,106,54,88,85,68,86,101,55,89,48,76,106,64,70,66,65,48,79,91,74,46,53,73,57,78,178,154,132,66,93,78,96,82,80,75,88,81,107,86,93,55,136,49,54,88,66,68,69,68,87,70,50,73", "endOffsets": "17917,17995,18065,18147,18243,18308,18379,18678,18749,18838,18912,19160,19345,19433,19582,19941,20245,21321,21588,21662,21761,21922,22012,22127,22272,22596,22677,22783,22857,22950,23055,23149,23243,23344,23410,23557,23859,23968,24766,24830,25573,27229,27303,27409,27516,27571,27660,27746,27815,27902,28004,28060,28150,28733,28810,28917,29150,29221,29288,29354,29900,30300,30392,30467,30514,30568,30896,30954,31033,31212,31367,31500,31766,31860,32835,32932,33097,33178,33537,33626,33793,34480,35023,35117,42837,42974,43024,43190,43816,46061,46130,46200,46269,46357,46428,46479,47961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\4ce322de103688eff69f9ca63ef8e11d\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "40,41,42,43,44,45,46,185", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3577,3675,3777,3878,3979,4084,4187,17206", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3670,3772,3873,3974,4079,4182,4299,17302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\161ecdf87e80b58eed8d2286c731b54d\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,16803", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,16880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b72fde255f7d5f902bd69f07371f54d\\transformed\\jetified-facebook-login-18.0.3\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,215,349,496,609,694,783,859,948,1036,1149,1256,1345,1434,1533,1655,1740,1827,2024,2119,2220,2335,2441", "endColumns": "159,133,146,112,84,88,75,88,87,112,106,88,88,98,121,84,86,196,94,100,114,105,159", "endOffsets": "210,344,491,604,689,778,854,943,1031,1144,1251,1340,1429,1528,1650,1735,1822,2019,2114,2215,2330,2436,2596"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4883,5043,5177,5324,5437,5522,5611,5687,5776,5864,5977,6084,6173,6262,6361,6483,6568,6655,6852,6947,7048,7163,7269", "endColumns": "159,133,146,112,84,88,75,88,87,112,106,88,88,98,121,84,86,196,94,100,114,105,159", "endOffsets": "5038,5172,5319,5432,5517,5606,5682,5771,5859,5972,6079,6168,6257,6356,6478,6563,6650,6847,6942,7043,7158,7264,7424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0457c542cce9afbad7be9a9320c6e161\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "94,102,173,177,507,513,514", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "9702,10398,16189,16487,49503,50128,50210", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "9771,10485,16261,16627,49667,50205,50283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2abb2661987f7c6e3ea5c9716013ed8c\\transformed\\jetified-stripe-3ds2-android-6.2.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,141,244,311,378,442,529", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "136,239,306,373,437,524,596"}, "to": {"startLines": "186,187,188,189,190,191,192", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "17307,17393,17496,17563,17630,17694,17781", "endColumns": "85,102,66,66,63,86,71", "endOffsets": "17388,17491,17558,17625,17689,17776,17848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\12bf204f4a6ab2b647dac2d3cbf4bd22\\transformed\\jetified-ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "50,51,96,97,99,110,111,171,172,174,175,178,179,183,508,509,510", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4630,4723,9884,9982,10165,11127,11209,16019,16107,16266,16337,16632,16716,17055,49672,49756,49826", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "4718,4801,9977,10079,10252,11204,11294,16102,16184,16332,16402,16711,16798,17122,49751,49821,49944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d25cc0d5fa9a24b638d99b4ef8f806b8\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "76,77,78,79,80,81,82,83,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7429,7536,7702,7828,7938,8080,8209,8324,8585,8766,8873,9036,9162,9329,9487,9556,9616", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "7531,7697,7823,7933,8075,8204,8319,8423,8761,8868,9031,9157,9324,9482,9551,9611,9697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9d74c733895accc053be45587d98f531\\transformed\\jetified-paymentsheet-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,222,299,408,514,597,680,822,931,1035,1110,1253,1310,1429,1502,1680,1893,2043,2134,2232,2327,2423,2626,2739,2836,3086,3184,3316,3557,3796,3901,4013,4216,4308,4370,4438,4529,4622,4725,4803,4902,4997,5101,5316,5390,5459,5534,5648,5732,5817,5888,5972,6046,6123,6271,6363,6471,6569,6643,6724,6793,6879,6942,7046,7181,7292,7426,7490,7583,7681,7819,7890,7982,8093,8152,8215,8317,8425,8611,8907,9218,9323,9396,9493,9566,9696,9814,9910,10004,10076,10162,10241,10331,10462,10597,10662,10738,10796,10982,11049,11112,11190,11262,11340,11423,11540,11664,11773,11862,11951,12039,12118", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "127,217,294,403,509,592,675,817,926,1030,1105,1248,1305,1424,1497,1675,1888,2038,2129,2227,2322,2418,2621,2734,2831,3081,3179,3311,3552,3791,3896,4008,4211,4303,4365,4433,4524,4617,4720,4798,4897,4992,5096,5311,5385,5454,5529,5643,5727,5812,5883,5967,6041,6118,6266,6358,6466,6564,6638,6719,6788,6874,6937,7041,7176,7287,7421,7485,7578,7676,7814,7885,7977,8088,8147,8210,8312,8420,8606,8902,9213,9318,9391,9488,9561,9691,9809,9905,9999,10071,10157,10236,10326,10457,10592,10657,10733,10791,10977,11044,11107,11185,11257,11335,11418,11535,11659,11768,11857,11946,12034,12113,12270"}, "to": {"startLines": "200,201,202,272,284,285,286,303,316,318,319,350,368,370,373,377,378,379,382,384,386,387,388,389,390,391,392,393,394,395,396,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,445,449,459,460,461,462,463,464,465,466,467,476,477,478,479,480,481,482,483,484,485,486,487,488,489,491,492,493,494,495,496,497,498,499,500,501,502,503,504,505,506", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18384,18461,18551,24379,26285,26391,26474,28483,29747,29905,30009,32618,34485,34624,34868,35182,35360,35573,35877,36075,36272,36367,36463,36666,36779,36876,37126,37224,37356,37597,37836,38042,38154,38357,38449,38511,38579,38670,38763,38866,38944,39043,39138,39242,39457,39531,39600,39675,39789,39873,39958,40029,40113,40187,40264,40888,40980,41088,41186,41260,41341,41410,41496,41559,41663,41798,41909,42043,42107,42200,42298,42436,42694,43029,44696,44755,44818,44920,45028,45214,45510,45821,45926,46550,46647,46720,46850,46968,47064,47158,47230,47316,47395,47485,47616,47751,47816,47966,48024,48210,48277,48340,48418,48490,48568,48651,48768,48892,49001,49090,49179,49267,49346", "endColumns": "76,89,76,108,105,82,82,141,108,103,74,142,56,118,72,177,212,149,90,97,94,95,202,112,96,249,97,131,240,238,104,111,202,91,61,67,90,92,102,77,98,94,103,214,73,68,74,113,83,84,70,83,73,76,147,91,107,97,73,80,68,85,62,103,134,110,133,63,92,97,137,70,91,110,58,62,101,107,185,295,310,104,72,96,72,129,117,95,93,71,85,78,89,130,134,64,75,57,185,66,62,77,71,77,82,116,123,108,88,88,87,78,156", "endOffsets": "18456,18546,18623,24483,26386,26469,26552,28620,29851,30004,30079,32756,34537,34738,34936,35355,35568,35718,35963,36168,36362,36458,36661,36774,36871,37121,37219,37351,37592,37831,37936,38149,38352,38444,38506,38574,38665,38758,38861,38939,39038,39133,39237,39452,39526,39595,39670,39784,39868,39953,40024,40108,40182,40259,40407,40975,41083,41181,41255,41336,41405,41491,41554,41658,41793,41904,42038,42102,42195,42293,42431,42502,42781,43135,44750,44813,44915,45023,45209,45505,45816,45921,45994,46642,46715,46845,46963,47059,47153,47225,47311,47390,47480,47611,47746,47811,47887,48019,48205,48272,48335,48413,48485,48563,48646,48763,48887,48996,49085,49174,49262,49341,49498"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\17f8ffee4e0d1cf45a84aa1eb9fe4fbe\\transformed\\jetified-stripe-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,198,265,349,418,479,551,615,680,766,836,908,971,1045,1109,1182,1246,1334,1399,1485,1561,1651,1747,1861,1942,1993,2044,2130,2194,2266,2340,2456,2550,2651", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "113,193,260,344,413,474,546,610,675,761,831,903,966,1040,1104,1177,1241,1329,1394,1480,1556,1646,1742,1856,1937,1988,2039,2125,2189,2261,2335,2451,2545,2646,2744"}, "to": {"startLines": "207,210,213,215,216,217,224,225,227,228,229,230,231,232,233,235,236,239,250,251,263,265,266,300,301,315,327,328,330,337,338,348,349,357,358", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18917,19165,19438,19587,19671,19740,20250,20322,20458,20523,20609,20679,20751,20814,20888,21010,21083,21326,22277,22342,23415,23562,23652,28234,28348,29696,30573,30624,30763,31505,31577,32408,32524,33267,33368", "endColumns": "62,79,66,83,68,60,71,63,64,85,69,71,62,73,63,72,63,87,64,85,75,89,95,113,80,50,50,85,63,71,73,115,93,100,97", "endOffsets": "18975,19240,19500,19666,19735,19796,20317,20381,20518,20604,20674,20746,20809,20883,20947,21078,21142,21409,22337,22423,23486,23647,23743,28343,28424,29742,30619,30705,30822,31572,31646,32519,32613,33363,33461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\19cfd4b8655087d7e85f227c9eae5dbe\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,100,101,104,109,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,176,181,182,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3148,3226,3304,3388,3486,4304,4401,4538,10257,10332,10568,11050,11299,11362,11480,11541,11606,11663,11733,11794,11848,11964,12021,12083,12137,12211,12339,12427,12514,12617,12709,12795,12932,13016,13101,13235,13326,13402,13456,13507,13573,13645,13723,13794,13876,13956,14032,14109,14186,14293,14382,14455,14545,14640,14714,14795,14888,14943,15024,15090,15176,15261,15323,15387,15450,15522,15620,15719,15814,15906,15964,16407,16885,16979,17127", "endLines": "7,35,36,37,38,39,47,48,49,100,101,104,109,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,176,181,182,184", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3221,3299,3383,3481,3572,4396,4533,4625,10327,10393,10662,11122,11357,11475,11536,11601,11658,11728,11789,11843,11959,12016,12078,12132,12206,12334,12422,12509,12612,12704,12790,12927,13011,13096,13230,13321,13397,13451,13502,13568,13640,13718,13789,13871,13951,14027,14104,14181,14288,14377,14450,14540,14635,14709,14790,14883,14938,15019,15085,15171,15256,15318,15382,15445,15517,15615,15714,15809,15901,15959,16014,16482,16974,17050,17201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d096be5bf3defe9833e433dd53e520\\transformed\\jetified-stripe-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,135,195,259,344,409,481,539,616,693,761,821", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "130,190,254,339,404,476,534,611,688,756,816,890"}, "to": {"startLines": "208,218,220,221,222,226,234,237,240,244,248,252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18980,19801,19946,20010,20095,20386,20952,21147,21414,21766,22132,22428", "endColumns": "79,59,63,84,64,71,57,76,76,67,59,73", "endOffsets": "19055,19856,20005,20090,20155,20453,21005,21219,21486,21829,22187,22497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bb10723edee8237d3346b0c820444d9e\\transformed\\jetified-payments-ui-core-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,359,461,673,719,786,885,960,1228,1288,1380,1459,1513,1577,1653,1745,2082,2155,2223,2276,2329,2408,2523,2634,2701,2780,2862,2946,3031,3150,3444,3533,3610,3692,3752,3817,3877,3949,4031,4138,4237,4338,4467,4565,4639,4718,4814,5001,5212,5343,5475,5538,6245,6349,6413", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "179,354,456,668,714,781,880,955,1223,1283,1375,1454,1508,1572,1648,1740,2077,2150,2218,2271,2324,2403,2518,2629,2696,2775,2857,2941,3026,3145,3439,3528,3605,3687,3747,3812,3872,3944,4026,4133,4232,4333,4462,4560,4634,4713,4809,4996,5207,5338,5470,5533,6240,6344,6408,6474"}, "to": {"startLines": "269,270,271,273,277,278,279,280,281,282,283,299,302,304,308,309,314,320,321,329,339,343,344,345,346,347,353,356,361,363,364,365,366,369,371,372,376,380,381,383,385,397,422,423,424,425,426,444,451,452,453,454,456,457,458,475", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "23973,24102,24277,24488,25578,25624,25691,25790,25865,26133,26193,28155,28429,28625,28922,28998,29359,30084,30157,30710,31651,31957,32036,32151,32262,32329,32937,33183,33631,33798,33917,34211,34300,34542,34743,34803,35122,35723,35795,35968,36173,37941,40412,40541,40639,40713,40792,42507,43195,43406,43537,43669,43821,44528,44632,46484", "endColumns": "128,174,101,211,45,66,98,74,267,59,91,78,53,63,75,91,336,72,67,52,52,78,114,110,66,78,81,83,84,118,293,88,76,81,59,64,59,71,81,106,98,100,128,97,73,78,95,186,210,130,131,62,706,103,63,65", "endOffsets": "24097,24272,24374,24695,25619,25686,25785,25860,26128,26188,26280,28229,28478,28684,28993,29085,29691,30152,30220,30758,31699,32031,32146,32257,32324,32403,33014,33262,33711,33912,34206,34295,34372,34619,34798,34863,35177,35790,35872,36070,36267,38037,40536,40634,40708,40787,40883,42689,43401,43532,43664,43727,44523,44627,44691,46545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f587360e9af95cef83d780637ea1dc1c\\transformed\\jetified-hcaptcha-21.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "342", "startColumns": "4", "startOffsets": "31865", "endColumns": "91", "endOffsets": "31952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a5e8ad80af0b5b549ee8ae2f5683bfa5\\transformed\\jetified-foundation-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "511,512", "startColumns": "4,4", "startOffsets": "49949,50037", "endColumns": "87,90", "endOffsets": "50032,50123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\06b5623b7b3ff4a029d48adbb7df6ac7\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "95,105,106,107", "startColumns": "4,4,4,4", "startOffsets": "9776,10667,10772,10884", "endColumns": "107,104,111,104", "endOffsets": "9879,10767,10879,10984"}}]}]}